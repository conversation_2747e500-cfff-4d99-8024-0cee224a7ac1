!function(){var a={},b=function(b){for(var c=a[b],e=c.deps,f=c.defn,g=e.length,h=new Array(g),i=0;g>i;++i)h[i]=d(e[i]);var j=f.apply(null,h);if(void 0===j)throw"module ["+b+"] returned undefined";c.instance=j},c=function(b,c,d){if("string"!=typeof b)throw"module id must be a string";if(void 0===c)throw"no dependencies for "+b;if(void 0===d)throw"no definition function for "+b;a[b]={deps:c,defn:d,instance:void 0}},d=function(c){var d=a[c];if(void 0===d)throw"module ["+c+"] was undefined";return void 0===d.instance&&b(c),d.instance},e=function(a,b){for(var c=a.length,e=new Array(c),f=0;c>f;++f)e.push(d(a[f]));b.apply(null,b)},f={};f.bolt={module:{api:{define:c,require:e,demand:d}}};var g=c,h=function(a,b){g(a,[],function(){return b})};h("1",tinymce.PluginManager),h("2",tinymce.Env),h("3",tinymce.util.Promise),h("4",tinymce.util.URI),h("5",tinymce.util.Tools),h("6",tinymce.util.Delay),g("j",[],function(){function a(a,b){return c(document.createElement("canvas"),a,b)}function b(a){return a.getContext("2d")}function c(a,b,c){return a.width=b,a.height=c,a}return{create:a,resize:c,get2dContext:b}}),g("k",[],function(){function a(a){return a.naturalWidth||a.width}function b(a){return a.naturalHeight||a.height}return{getWidth:a,getHeight:b}}),g("l",[],function(){function a(a,b){return function(){a.apply(b,arguments)}}function b(b){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof b)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],h(b,a(d,this),a(e,this))}function c(a){var b=this;return null===this._state?void this._deferreds.push(a):void i(function(){var c=b._state?a.onFulfilled:a.onRejected;if(null===c)return void(b._state?a.resolve:a.reject)(b._value);var d;try{d=c(b._value)}catch(e){return void a.reject(e)}a.resolve(d)})}function d(b){try{if(b===this)throw new TypeError("A promise cannot be resolved with itself.");if(b&&("object"==typeof b||"function"==typeof b)){var c=b.then;if("function"==typeof c)return void h(a(c,b),a(d,this),a(e,this))}this._state=!0,this._value=b,f.call(this)}catch(g){e.call(this,g)}}function e(a){this._state=!1,this._value=a,f.call(this)}function f(){for(var a=0,b=this._deferreds.length;b>a;a++)c.call(this,this._deferreds[a]);this._deferreds=null}function g(a,b,c,d){this.onFulfilled="function"==typeof a?a:null,this.onRejected="function"==typeof b?b:null,this.resolve=c,this.reject=d}function h(a,b,c){var d=!1;try{a(function(a){d||(d=!0,b(a))},function(a){d||(d=!0,c(a))})}catch(e){if(d)return;d=!0,c(e)}}if(window.Promise)return window.Promise;var i=b.immediateFn||"function"==typeof setImmediate&&setImmediate||function(a){setTimeout(a,1)},j=Array.isArray||function(a){return"[object Array]"===Object.prototype.toString.call(a)};return b.prototype["catch"]=function(a){return this.then(null,a)},b.prototype.then=function(a,d){var e=this;return new b(function(b,f){c.call(e,new g(a,d,b,f))})},b.all=function(){var a=Array.prototype.slice.call(1===arguments.length&&j(arguments[0])?arguments[0]:arguments);return new b(function(b,c){function d(f,g){try{if(g&&("object"==typeof g||"function"==typeof g)){var h=g.then;if("function"==typeof h)return void h.call(g,function(a){d(f,a)},c)}a[f]=g,0===--e&&b(a)}catch(i){c(i)}}if(0===a.length)return b([]);for(var e=a.length,f=0;f<a.length;f++)d(f,a[f])})},b.resolve=function(a){return a&&"object"==typeof a&&a.constructor===b?a:new b(function(b){b(a)})},b.reject=function(a){return new b(function(b,c){c(a)})},b.race=function(a){return new b(function(b,c){for(var d=0,e=a.length;e>d;d++)a[d].then(b,c)})},b}),g("m",[],function(){function a(a){var b=document.createElement("a");return b.href=a,b.pathname}function b(b){var c=a(b).split("."),d=c[c.length-1],e={jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png"};return d&&(d=d.toLowerCase()),e[d]}return{guessMimeType:b}}),g("c",["l","j","m","k"],function(a,b,c,d){function e(b){return new a(function(a){function c(){b.removeEventListener("load",c),a(b)}b.complete?a(b):b.addEventListener("load",c)})}function f(a){return e(a).then(function(a){var c,e;return e=b.create(d.getWidth(a),d.getHeight(a)),c=b.get2dContext(e),c.drawImage(a,0,0),e})}function g(a){return e(a).then(function(a){var b=a.src;return 0===b.indexOf("blob:")?i(b):0===b.indexOf("data:")?j(b):f(a).then(function(a){return j(a.toDataURL(c.guessMimeType(b)))})})}function h(b){return new a(function(a){function c(){d.removeEventListener("load",c),a(d)}var d=new Image;d.addEventListener("load",c),d.src=URL.createObjectURL(b),d.complete&&c()})}function i(b){return new a(function(a){var c=new XMLHttpRequest;c.open("GET",b,!0),c.responseType="blob",c.onload=function(){200==this.status&&a(this.response)},c.send()})}function j(b){return new a(function(a){var c,d,e,f,g,h;if(b=b.split(","),f=/data:([^;]+)/.exec(b[0]),f&&(g=f[1]),c=atob(b[1]),window.WebKitBlobBuilder){for(h=new WebKitBlobBuilder,d=new ArrayBuffer(c.length),e=0;e<d.length;e++)d[e]=c.charCodeAt(e);return h.append(d),void a(h.getBlob(g))}for(d=new Uint8Array(c.length),e=0;e<d.length;e++)d[e]=c.charCodeAt(e);a(new Blob([d],{type:g}))})}function k(a){return 0===a.indexOf("blob:")?i(a):0===a.indexOf("data:")?j(a):null}function l(a,b){return j(a.toDataURL(b))}function m(b){return new a(function(a){var c=new FileReader;c.onloadend=function(){a(c.result)},c.readAsDataURL(b)})}function n(a){return m(a).then(function(a){return a.split(",")[1]})}function o(a){URL.revokeObjectURL(a.src)}return{blobToImage:h,imageToBlob:g,blobToDataUri:m,blobToBase64:n,imageToCanvas:f,canvasToBlob:l,revokeImageUrl:o,uriToBlob:k}}),g("n",[],function(){function a(a,b,c){return a=parseFloat(a),a>c?a=c:b>a&&(a=b),a}function b(){return[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1]}function c(a,b){var c,d,e,f,g=[],h=new Array(10);for(c=0;5>c;c++){for(d=0;5>d;d++)g[d]=b[d+5*c];for(d=0;5>d;d++){for(f=0,e=0;5>e;e++)f+=a[d+5*e]*g[e];h[d+5*c]=f}}return h}function d(b,c){return c=a(c,0,1),b.map(function(b,d){return d%6===0?b=1-(1-b)*c:b*=c,a(b,0,1)})}function e(b,d){var e;return d=a(d,-1,1),d*=100,0>d?e=127+d/100*127:(e=d%1,e=0===e?l[d]:l[Math.floor(d)]*(1-e)+l[Math.floor(d)+1]*e,e=127*e+127),c(b,[e/127,0,0,0,.5*(127-e),0,e/127,0,0,.5*(127-e),0,0,e/127,0,.5*(127-e),0,0,0,1,0,0,0,0,0,1])}function f(b,d){var e,f,g,h;return d=a(d,-1,1),e=1+(d>0?3*d:d),f=.3086,g=.6094,h=.082,c(b,[f*(1-e)+e,g*(1-e),h*(1-e),0,0,f*(1-e),g*(1-e)+e,h*(1-e),0,0,f*(1-e),g*(1-e),h*(1-e)+e,0,0,0,0,0,1,0,0,0,0,0,1])}function g(b,d){var e,f,g,h,i;return d=a(d,-180,180)/180*Math.PI,e=Math.cos(d),f=Math.sin(d),g=.213,h=.715,i=.072,c(b,[g+e*(1-g)+f*-g,h+e*-h+f*-h,i+e*-i+f*(1-i),0,0,g+e*-g+.143*f,h+e*(1-h)+.14*f,i+e*-i+f*-.283,0,0,g+e*-g+f*-(1-g),h+e*-h+f*h,i+e*(1-i)+f*i,0,0,0,0,0,1,0,0,0,0,0,1])}function h(b,d){return d=a(255*d,-255,255),c(b,[1,0,0,0,d,0,1,0,0,d,0,0,1,0,d,0,0,0,1,0,0,0,0,0,1])}function i(b,d,e,f){return d=a(d,0,2),e=a(e,0,2),f=a(f,0,2),c(b,[d,0,0,0,0,0,e,0,0,0,0,0,f,0,0,0,0,0,1,0,0,0,0,0,1])}function j(b,e){return e=a(e,0,1),c(b,d([.393,.769,.189,0,0,.349,.686,.168,0,0,.272,.534,.131,0,0,0,0,0,1,0,0,0,0,0,1],e))}function k(b,e){return e=a(e,0,1),c(b,d([.33,.34,.33,0,0,.33,.34,.33,0,0,.33,.34,.33,0,0,0,0,0,1,0,0,0,0,0,1],e))}var l=[0,.01,.02,.04,.05,.06,.07,.08,.1,.11,.12,.14,.15,.16,.17,.18,.2,.21,.22,.24,.25,.27,.28,.3,.32,.34,.36,.38,.4,.42,.44,.46,.48,.5,.53,.56,.59,.62,.65,.68,.71,.74,.77,.8,.83,.86,.89,.92,.95,.98,1,1.06,1.12,1.18,1.24,1.3,1.36,1.42,1.48,1.54,1.6,1.66,1.72,1.78,1.84,1.9,1.96,2,2.12,2.25,2.37,2.5,2.62,2.75,2.87,3,3.2,3.4,3.6,3.8,4,4.3,4.7,4.9,5,5.5,6,6.5,6.8,7,7.3,7.5,7.8,8,8.4,8.7,9,9.4,9.6,9.8,10];return{identity:b,adjust:d,multiply:c,adjustContrast:e,adjustBrightness:h,adjustSaturation:f,adjustHue:g,adjustColors:i,adjustSepia:j,adjustGrayscale:k}}),g("a",["j","k","c","n"],function(a,b,c,d){function e(d,e){return c.blobToImage(d).then(function(d){function f(a,b){var c,d,e,f,g,h=a.data,i=b[0],j=b[1],k=b[2],l=b[3],m=b[4],n=b[5],o=b[6],p=b[7],q=b[8],r=b[9],s=b[10],t=b[11],u=b[12],v=b[13],w=b[14],x=b[15],y=b[16],z=b[17],A=b[18],B=b[19];for(g=0;g<h.length;g+=4)c=h[g],d=h[g+1],e=h[g+2],f=h[g+3],h[g]=c*i+d*j+e*k+f*l+m,h[g+1]=c*n+d*o+e*p+f*q+r,h[g+2]=c*s+d*t+e*u+f*v+w,h[g+3]=c*x+d*y+e*z+f*A+B;return a}var g,h=a.create(b.getWidth(d),b.getHeight(d)),i=a.get2dContext(h);return i.drawImage(d,0,0),k(d),g=f(i.getImageData(0,0,h.width,h.height),e),i.putImageData(g,0,0),c.canvasToBlob(h)})}function f(d,e){return c.blobToImage(d).then(function(d){function f(a,b,c){function d(a,b,c){return a>c?a=c:b>a&&(a=b),a}var e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u;for(g=Math.round(Math.sqrt(c.length)),h=Math.floor(g/2),e=a.data,f=b.data,t=a.width,u=a.height,j=0;u>j;j++)for(i=0;t>i;i++){for(k=l=m=0,o=0;g>o;o++)for(n=0;g>n;n++)p=d(i+n-h,0,t-1),q=d(j+o-h,0,u-1),r=4*(q*t+p),s=c[o*g+n],k+=e[r]*s,l+=e[r+1]*s,m+=e[r+2]*s;r=4*(j*t+i),f[r]=d(k,0,255),f[r+1]=d(l,0,255),f[r+2]=d(m,0,255)}return b}var g,h,i=a.create(b.getWidth(d),b.getHeight(d)),j=a.get2dContext(i);return j.drawImage(d,0,0),k(d),g=j.getImageData(0,0,i.width,i.height),h=j.getImageData(0,0,i.width,i.height),h=f(g,h,e),j.putImageData(h,0,0),c.canvasToBlob(i)})}function g(d){return function(e,f){return c.blobToImage(e).then(function(e){function g(a,b){var c,d=a.data;for(c=0;c<d.length;c+=4)d[c]=b[d[c]],d[c+1]=b[d[c+1]],d[c+2]=b[d[c+2]];return a}var h,i,j=a.create(b.getWidth(e),b.getHeight(e)),l=a.get2dContext(j),m=new Array(256);for(i=0;i<m.length;i++)m[i]=d(i,f);return l.drawImage(e,0,0),k(e),h=g(l.getImageData(0,0,j.width,j.height),m),l.putImageData(h,0,0),c.canvasToBlob(j)})}}function h(a){return function(b,c){return e(b,a(d.identity(),c))}}function i(a){return function(b){return e(b,a)}}function j(a){return function(b){return f(b,a)}}var k=c.revokeImageUrl;return{invert:i([-1,0,0,0,255,0,-1,0,0,255,0,0,-1,0,255,0,0,0,1,0]),brightness:h(d.adjustBrightness),hue:h(d.adjustHue),saturate:h(d.adjustSaturation),contrast:h(d.adjustContrast),grayscale:h(d.adjustGrayscale),sepia:h(d.adjustSepia),colorize:function(a,b,c,f){return e(a,d.adjustColors(d.identity(),b,c,f))},sharpen:j([0,-1,0,-1,5,-1,0,-1,0]),emboss:j([-2,-1,0,-1,1,1,0,1,2]),gamma:g(function(a,b){return 255*Math.pow(a/255,1-b)}),exposure:g(function(a,b){return 255*(1-Math.exp(-(a/255)*b))}),colorFilter:e,convoluteFilter:f}}),g("b",["c","j","k"],function(a,b,c){function d(d,e){return a.blobToImage(d).then(function(f){var g=b.create(c.getWidth(f),c.getHeight(f)),i=b.get2dContext(g),j=0,k=0;return e=0>e?360+e:e,90!=e&&270!=e||b.resize(g,g.height,g.width),90!=e&&180!=e||(j=g.width),270!=e&&180!=e||(k=g.height),i.translate(j,k),i.rotate(e*Math.PI/180),i.drawImage(f,0,0),h(f),a.canvasToBlob(g,d.type)})}function e(d,e){return a.blobToImage(d).then(function(d){var f=b.create(c.getWidth(d),c.getHeight(d)),g=b.get2dContext(f);return"v"==e?(g.scale(1,-1),g.drawImage(d,0,-f.height)):(g.scale(-1,1),g.drawImage(d,-f.width,0)),h(d),a.canvasToBlob(f)})}function f(c,d,e,f,g){return a.blobToImage(c).then(function(c){var i=b.create(f,g),j=b.get2dContext(i);return j.drawImage(c,-d,-e),h(c),a.canvasToBlob(i)})}function g(c,d,e){return a.blobToImage(c).then(function(f){var g=b.create(d,e),i=b.get2dContext(g);return i.drawImage(f,0,0,d,e),h(f),a.canvasToBlob(g,c.type)})}var h=a.revokeImageUrl;return{rotate:d,flip:e,crop:f,resize:g}}),g("7",["a","b"],function(a,b){var c=function(b){return a.invert(b)},d=function(b){return a.sharpen(b)},e=function(b){return a.emboss(b)},f=function(b,c){return a.gamma(b,c)},g=function(b,c){return a.exposure(b,c)},h=function(b,c,d,e){return a.colorize(b,c,d,e)},i=function(b,c){return a.brightness(b,c)},j=function(b,c){return a.hue(b,c)},k=function(b,c){return a.saturate(b,c)},l=function(b,c){return a.contrast(b,c)},m=function(b,c){return a.grayscale(b,c)},n=function(b,c){return a.sepia(b,c)},o=function(a,c){return b.flip(a,c)},p=function(a,c,d,e,f){return b.crop(a,c,d,e,f)},q=function(a,c,d){return b.resize(a,c,d)},r=function(a,c){return b.rotate(a,c)};return{invert:c,sharpen:d,emboss:e,brightness:i,hue:j,saturate:k,contrast:l,grayscale:m,sepia:n,colorize:h,gamma:f,exposure:g,flip:o,crop:p,resize:q,rotate:r}}),g("8",["c"],function(a){var b=function(b){return a.blobToImage(b)},c=function(b){return a.imageToBlob(b)},d=function(b){return a.blobToDataUri(b)},e=function(b){return a.blobToBase64(b)};return{blobToImage:b,imageToBlob:c,blobToDataUri:d,blobToBase64:e}}),h("d",tinymce.dom.DOMUtils),h("e",tinymce.ui.Factory),h("f",tinymce.ui.Form),h("g",tinymce.ui.Container),h("o",tinymce.ui.Control),h("p",tinymce.ui.DragHelper),h("q",tinymce.geom.Rect),h("s",tinymce.dom.DomQuery),h("t",tinymce.util.Observable),h("u",tinymce.util.VK),g("r",["s","p","q","5","t","u"],function(a,b,c,d,e,f){var g=0;return function(h,i,j,k,l){function m(a,b){return{x:b.x+a.x,y:b.y+a.y,w:b.w,h:b.h}}function n(a,b){return{x:b.x-a.x,y:b.y-a.y,w:b.w,h:b.h}}function o(){return n(j,h)}function p(a,b,d,e){var f,g,i,k,l;f=b.x,g=b.y,i=b.w,k=b.h,f+=d*a.deltaX,g+=e*a.deltaY,i+=d*a.deltaW,k+=e*a.deltaH,20>i&&(i=20),20>k&&(k=20),l=h=c.clamp({x:f,y:g,w:i,h:k},j,"move"==a.name),l=n(j,l),y.fire("updateRect",{rect:l}),v(l)}function q(){function c(a){var c;return new b(D,{document:k.ownerDocument,handle:D+"-"+a.name,start:function(){c=h},drag:function(b){p(a,c,b.deltaX,b.deltaY)}})}a('<div id="'+D+'" class="'+C+'croprect-container" role="grid" aria-dropeffect="execute">').appendTo(k),d.each(B,function(b){a("#"+D,k).append('<div id="'+D+"-"+b+'"class="'+C+'croprect-block" style="display: none" data-mce-bogus="all">')}),d.each(z,function(b){a("#"+D,k).append('<div id="'+D+"-"+b.name+'" class="'+C+"croprect-handle "+C+"croprect-handle-"+b.name+'"style="display: none" data-mce-bogus="all" role="gridcell" tabindex="-1" aria-label="'+b.label+'" aria-grabbed="false">')}),A=d.map(z,c),s(h),a(k).on("focusin focusout",function(b){a(b.target).attr("aria-grabbed","focus"===b.type)}),a(k).on("keydown",function(a){function b(a,b,d,e,f){a.stopPropagation(),a.preventDefault(),p(c,d,e,f)}var c;switch(d.each(z,function(b){return a.target.id==D+"-"+b.name?(c=b,!1):void 0}),a.keyCode){case f.LEFT:b(a,c,h,-10,0);break;case f.RIGHT:b(a,c,h,10,0);break;case f.UP:b(a,c,h,0,-10);break;case f.DOWN:b(a,c,h,0,10);break;case f.ENTER:case f.SPACEBAR:a.preventDefault(),l()}})}function r(b){var c;c=d.map(z,function(a){return"#"+D+"-"+a.name}).concat(d.map(B,function(a){return"#"+D+"-"+a})).join(","),b?a(c,k).show():a(c,k).hide()}function s(b){function c(b,c){c.h<0&&(c.h=0),c.w<0&&(c.w=0),a("#"+D+"-"+b,k).css({left:c.x,top:c.y,width:c.w,height:c.h})}d.each(z,function(c){a("#"+D+"-"+c.name,k).css({left:b.w*c.xMul+b.x,top:b.h*c.yMul+b.y})}),c("top",{x:i.x,y:i.y,w:i.w,h:b.y-i.y}),c("right",{x:b.x+b.w,y:b.y,w:i.w-b.x-b.w+i.x,h:b.h}),c("bottom",{x:i.x,y:b.y+b.h,w:i.w,h:i.h-b.y-b.h+i.y}),c("left",{x:i.x,y:b.y,w:b.x-i.x,h:b.h}),c("move",b)}function t(a){h=a,s(h)}function u(a){i=a,s(h)}function v(a){t(m(j,a))}function w(a){j=a,s(h)}function x(){d.each(A,function(a){a.destroy()}),A=[]}var y,z,A,B,C="mce-",D=C+"crid-"+g++;return z=[{name:"move",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:0,deltaH:0,label:"Crop Mask"},{name:"nw",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:-1,deltaH:-1,label:"Top Left Crop Handle"},{name:"ne",xMul:1,yMul:0,deltaX:0,deltaY:1,deltaW:1,deltaH:-1,label:"Top Right Crop Handle"},{name:"sw",xMul:0,yMul:1,deltaX:1,deltaY:0,deltaW:-1,deltaH:1,label:"Bottom Left Crop Handle"},{name:"se",xMul:1,yMul:1,deltaX:0,deltaY:0,deltaW:1,deltaH:1,label:"Bottom Right Crop Handle"}],B=["top","right","bottom","left"],q(k),y=d.extend({toggleVisibility:r,setClampRect:w,setRect:t,getInnerRect:o,setInnerRect:v,setViewPortRect:u,destroy:x},e)}}),g("h",["o","p","q","5","3","r"],function(a,b,c,d,e,f){function g(a){return new e(function(b){function c(){a.removeEventListener("load",c),b(a)}a.complete?b(a):a.addEventListener("load",c)})}return a.extend({Defaults:{classes:"imagepanel"},selection:function(a){return arguments.length?(this.state.set("rect",a),this):this.state.get("rect")},imageSize:function(){var a=this.state.get("viewRect");return{w:a.w,h:a.h}},toggleCropRect:function(a){this.state.set("cropEnabled",a)},imageSrc:function(a){var b=this,d=new Image;d.src=a,g(d).then(function(){var a,e,f=b.state.get("viewRect");e=b.$el.find("img"),e[0]?e.replaceWith(d):b.getEl().appendChild(d),a={x:0,y:0,w:d.naturalWidth,h:d.naturalHeight},b.state.set("viewRect",a),b.state.set("rect",c.inflate(a,-20,-20)),f&&f.w==a.w&&f.h==a.h||b.zoomFit(),b.repaintImage(),b.fire("load")})},zoom:function(a){return arguments.length?(this.state.set("zoom",a),this):this.state.get("zoom")},postRender:function(){return this.imageSrc(this.settings.imageSrc),this._super()},zoomFit:function(){var a,b,c,d,e,f,g,h=this;g=10,a=h.$el.find("img"),b=h.getEl().clientWidth,c=h.getEl().clientHeight,d=a[0].naturalWidth,e=a[0].naturalHeight,f=Math.min((b-g)/d,(c-g)/e),f>=1&&(f=1),h.zoom(f)},repaintImage:function(){var a,b,c,d,e,f,g,h,i,j;j=this.getEl(),h=this.zoom(),i=this.state.get("rect"),g=this.$el.find("img"),e=j.offsetWidth,f=j.offsetHeight,c=g[0].naturalWidth*h,d=g[0].naturalHeight*h,a=Math.max(0,e/2-c/2),b=Math.max(0,f/2-d/2),g.css({left:a,top:b,width:c,height:d}),this.cropRect&&(this.cropRect.setRect({x:i.x*h+a,y:i.y*h+b,w:i.w*h,h:i.h*h}),this.cropRect.setClampRect({x:a,y:b,w:c,h:d}),this.cropRect.setViewPortRect({x:0,y:0,w:e,h:f}))},bindStates:function(){function a(a){b.cropRect=new f(a,b.state.get("viewRect"),b.state.get("viewRect"),b.getEl(),function(){b.fire("crop")}),b.cropRect.on("updateRect",function(a){var c=a.rect,d=b.zoom();c={x:Math.round(c.x/d),y:Math.round(c.y/d),w:Math.round(c.w/d),h:Math.round(c.h/d)},b.state.set("rect",c)}),b.on("remove",b.cropRect.destroy)}var b=this;b.state.on("change:cropEnabled",function(a){b.cropRect.toggleVisibility(a.value),b.repaintImage()}),b.state.on("change:zoom",function(){b.repaintImage()}),b.state.on("change:rect",function(c){var d=c.value;b.cropRect||a(d),b.cropRect.setRect(d)})}})}),g("i",[],function(){return function(){function a(a){var b;return b=f.splice(++g),f.push(a),{state:a,removed:b}}function b(){return d()?f[--g]:void 0}function c(){return e()?f[++g]:void 0}function d(){return g>0}function e(){return-1!=g&&g<f.length-1}var f=[],g=-1;return{data:f,add:a,undo:b,redo:c,canUndo:d,canRedo:e}}}),g("9",["d","5","3","e","f","g","h","7","8","i"],function(a,b,c,d,e,f,g,h,i,j){function k(a){return{blob:a,url:URL.createObjectURL(a)}}function l(a){a&&URL.revokeObjectURL(a.url)}function m(a){b.each(a,l)}function n(c,i,n){function o(a){var b,c,d,e;b=L.find("#w")[0],c=L.find("#h")[0],d=parseInt(b.value(),10),e=parseInt(c.value(),10),L.find("#constrain")[0].checked()&&ga&&ha&&d&&e&&("w"==a.control.settings.name?(e=Math.round(d*ia),c.value(e)):(d=Math.round(e*ja),b.value(d))),ga=d,ha=e}function p(a){return Math.round(100*a)+"%"}function q(){L.find("#undo").disabled(!ka.canUndo()),L.find("#redo").disabled(!ka.canRedo()),L.statusbar.find("#save").disabled(!ka.canUndo())}function r(){L.find("#undo").disabled(!0),L.find("#redo").disabled(!0)}function s(a){a&&S.imageSrc(a.url)}function t(a){return function(){var c=b.grep(fa,function(b){return b.settings.name!=a});b.each(c,function(a){a.hide()}),a.show(),a.focus()}}function u(a){O=k(a),s(O)}function v(a){c=k(a),s(c),m(ka.add(c).removed),q()}function w(){var a=S.selection();h.crop(c.blob,a.x,a.y,a.w,a.h).then(function(a){v(a),z()})}function x(a){var b=[].slice.call(arguments,1);return function(){var d=O||c;a.apply(this,[d.blob].concat(b)).then(u)}}function y(a){var b=[].slice.call(arguments,1);return function(){a.apply(this,[c.blob].concat(b)).then(v)}}function z(){s(c),l(O),t(M)(),q()}function A(){O&&(v(O.blob),z())}function B(){var a=S.zoom();2>a&&(a+=.1),S.zoom(a)}function C(){var a=S.zoom();a>.1&&(a-=.1),S.zoom(a)}function D(){c=ka.undo(),s(c),q()}function E(){c=ka.redo(),s(c),q()}function F(){i(c.blob),L.close()}function G(a){return new e({layout:"flex",direction:"row",labelGap:5,border:"0 0 1 0",align:"center",pack:"center",padding:"0 10 0 10",spacing:5,flex:0,minHeight:60,defaults:{classes:"imagetool",type:"button"},items:a})}function H(a,b){return G([{text:"Back",onclick:z},{type:"spacer",flex:1},{text:"Apply",subtype:"primary",onclick:A}]).hide().on("show",function(){r(),b(c.blob).then(function(a){var b=k(a);s(b),l(O),O=b})})}function I(a,b,d,e,f){function g(a){b(c.blob,a).then(function(a){var b=k(a);s(b),l(O),O=b})}return G([{text:"Back",onclick:z},{type:"spacer",flex:1},{type:"slider",flex:1,ondragend:function(a){g(a.value)},minValue:e,maxValue:f,value:d,previewFilter:p},{type:"spacer",flex:1},{text:"Apply",subtype:"primary",onclick:A}]).hide().on("show",function(){this.find("slider").value(d),r()})}function J(a,b){function d(){var a,d,e;a=L.find("#r")[0].value(),d=L.find("#g")[0].value(),e=L.find("#b")[0].value(),b(c.blob,a,d,e).then(function(a){var b=k(a);s(b),l(O),O=b})}return G([{text:"Back",onclick:z},{type:"spacer",flex:1},{type:"slider",label:"R",name:"r",minValue:0,value:1,maxValue:2,ondragend:d,previewFilter:p},{type:"slider",label:"G",name:"g",minValue:0,value:1,maxValue:2,ondragend:d,previewFilter:p},{type:"slider",label:"B",name:"b",minValue:0,value:1,maxValue:2,ondragend:d,previewFilter:p},{type:"spacer",flex:1},{text:"Apply",subtype:"primary",onclick:A}]).hide().on("show",function(){L.find("#r,#g,#b").value(1),r()})}function K(a){a.control.value()===!0&&(ia=ha/ga,ja=ga/ha)}var L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ba,ca,da,ea,fa,ga,ha,ia,ja,ka=new j;P=G([{text:"Back",onclick:z},{type:"spacer",flex:1},{text:"Apply",subtype:"primary",onclick:w}]).hide().on("show hide",function(a){S.toggleCropRect("show"==a.type)}).on("show",r),Q=G([{text:"Back",onclick:z},{type:"spacer",flex:1},{type:"textbox",name:"w",label:"Width",size:4,onkeyup:o},{type:"textbox",name:"h",label:"Height",size:4,onkeyup:o},{type:"checkbox",name:"constrain",text:"Constrain proportions",checked:!0,onchange:K},{type:"spacer",flex:1},{text:"Apply",subtype:"primary",onclick:"submit"}]).hide().on("submit",function(a){var b=parseInt(L.find("#w").value(),10),c=parseInt(L.find("#h").value(),10);a.preventDefault(),y(h.resize,b,c)(),z()}).on("show",r),R=G([{text:"Back",onclick:z},{type:"spacer",flex:1},{icon:"fliph",tooltip:"Flip horizontally",onclick:x(h.flip,"h")},{icon:"flipv",tooltip:"Flip vertically",onclick:x(h.flip,"v")},{icon:"rotateleft",tooltip:"Rotate counterclockwise",onclick:x(h.rotate,-90)},{icon:"rotateright",tooltip:"Rotate clockwise",onclick:x(h.rotate,90)},{type:"spacer",flex:1},{text:"Apply",subtype:"primary",onclick:A}]).hide().on("show",r),V=H("Invert",h.invert),ba=H("Sharpen",h.sharpen),ca=H("Emboss",h.emboss),W=I("Brightness",h.brightness,0,-1,1),X=I("Hue",h.hue,180,0,360),Y=I("Saturate",h.saturate,0,-1,1),Z=I("Contrast",h.contrast,0,-1,1),$=I("Grayscale",h.grayscale,0,0,1),_=I("Sepia",h.sepia,0,0,1),aa=J("Colorize",h.colorize),da=I("Gamma",h.gamma,0,-1,1),ea=I("Exposure",h.exposure,1,0,2),N=G([{text:"Back",onclick:z},{type:"spacer",flex:1},{text:"hue",icon:"hue",onclick:t(X)},{text:"saturate",icon:"saturate",onclick:t(Y)},{text:"sepia",icon:"sepia",onclick:t(_)},{text:"emboss",icon:"emboss",onclick:t(ca)},{text:"exposure",icon:"exposure",onclick:t(ea)},{type:"spacer",flex:1}]).hide(),M=G([{tooltip:"Crop",icon:"crop",onclick:t(P)},{tooltip:"Resize",icon:"resize2",onclick:t(Q)},{tooltip:"Orientation",icon:"orientation",onclick:t(R)},{tooltip:"Brightness",icon:"sun",onclick:t(W)},{tooltip:"Sharpen",icon:"sharpen",onclick:t(ba)},{tooltip:"Contrast",icon:"contrast",onclick:t(Z)},{tooltip:"Color levels",icon:"drop",onclick:t(aa)},{tooltip:"Gamma",icon:"gamma",onclick:t(da)},{tooltip:"Invert",icon:"invert",onclick:t(V)}]),S=new g({flex:1,imageSrc:c.url}),T=new f({layout:"flex",direction:"column",border:"0 1 0 0",padding:5,spacing:5,items:[{type:"button",icon:"undo",tooltip:"Undo",name:"undo",onclick:D},{type:"button",icon:"redo",tooltip:"Redo",name:"redo",onclick:E},{type:"button",icon:"zoomin",tooltip:"Zoom in",onclick:B},{type:"button",icon:"zoomout",tooltip:"Zoom out",onclick:C}]}),U=new f({type:"container",layout:"flex",direction:"row",align:"stretch",flex:1,items:[T,S]}),fa=[M,P,Q,R,N,V,W,X,Y,Z,$,_,aa,ba,ca,da,ea],L=d.create("window",{layout:"flex",direction:"column",align:"stretch",minWidth:Math.min(a.DOM.getViewPort().w,800),minHeight:Math.min(a.DOM.getViewPort().h,650),title:"Edit image",items:fa.concat([U]),buttons:[{text:"Save",name:"save",subtype:"primary",onclick:F},{text:"Cancel",onclick:"close"}]}),L.renderTo(document.body).reflow(),L.on("close",function(){n(),m(ka.data),ka=null,O=null}),ka.add(c),q(),S.on("load",function(){ga=S.imageSize().w,ha=S.imageSize().h,ia=ha/ga,ja=ga/ha,L.find("#w").value(ga),L.find("#h").value(ha)}),S.on("crop",w)}function o(a){return new c(function(b,c){n(k(a),b,c)})}return{edit:o}}),g("0",["1","2","3","4","5","6","7","8","9"],function(a,b,c,d,e,f,g,h,i){var j=function(a){function j(b){function c(a){return a.indexOf("px")==a.length-2}var d,e;return d=b.style.width,e=b.style.height,d||e?c(d)&&c(e)?{w:parseInt(d,10),h:parseInt(e,10)}:null:(d=a.$(b).attr("width"),e=a.$(b).attr("height"),d&&e?{w:parseInt(d,10),h:parseInt(e,10)}:null)}function k(b,c){var d,e;c&&(d=b.style.width,e=b.style.height,(d||e)&&a.$(b).css({width:c.w,height:c.h}).removeAttr("data-mce-style"),d=b.width,e=b.height,(d||e)&&a.$(b).attr({width:c.w,height:c.h}))}function l(a){return{w:a.naturalWidth,h:a.naturalHeight}}function m(){return a.selection.getNode()}function n(){return"imagetools"+H++}function o(b){var c=b.src;return 0===c.indexOf("data:")||0===c.indexOf("blob:")||new d(c).host===a.documentBaseURI.host}function p(b){return-1!==e.inArray(a.settings.imagetools_cors_hosts,new d(b.src).host)}function q(){return a.settings.api_key||a.settings.imagetools_api_key}function r(a){return new c(function(b){var c,d;c=new XMLHttpRequest,c.onload=function(){b(this.response)},c.open("GET",a,!0),d=q(),d&&(c.setRequestHeader("Content-Type","application/json;charset=UTF-8"),c.setRequestHeader("tiny-api-key",d)),c.responseType="blob",c.send()})}function s(b){var c=b.src;if(p(b))return r(b.src);if(!o(b)){if(c=a.settings.imagetools_proxy,c+=(-1===c.indexOf("?")?"?":"&")+"url="+encodeURIComponent(b.src),q())return r(c);b=new Image,b.src=c}return h.imageToBlob(b)}function t(){var b;return b=a.editorUpload.blobCache.getByUri(m().src),b?b:s(m()).then(function(b){return h.blobToBase64(b).then(function(c){var d=a.editorUpload.blobCache,e=d.create(n(),b,c);return d.add(e),e})})}function u(){F=f.setEditorTimeout(a,function(){a.editorUpload.uploadImagesAuto()},3e4)}function v(){clearTimeout(F)}function w(b,c){return h.blobToDataUri(b).then(function(e){var f,g,h,i,j;return j=m(),f=n(),h=a.editorUpload.blobCache,g=d.parseDataUri(e).data,i=h.create(f,b,g),h.add(i),a.undoManager.transact(function(){function b(){a.$(j).off("load",b),a.nodeChanged(),c?a.editorUpload.uploadImagesAuto():(v(),u())}a.$(j).on("load",b),a.$(j).attr({src:i.blobUri()}).removeAttr("data-mce-src")}),i})}function x(b){return function(){return a._scanForImages().then(t).then(b).then(w)}}function y(a){return function(){return x(function(b){var c=j(m());return c&&k(m(),{w:c.h,h:c.w}),g.rotate(b.blob(),a)})()}}function z(a){return function(){return x(function(b){return g.flip(b.blob(),a)})()}}function A(){var a=m(),b=l(a);a&&s(a).then(i.edit).then(function(d){return new c(function(c){h.blobToImage(d).then(function(e){var f=l(e);b.w==f.w&&b.h==f.h||j(a)&&k(a,f),URL.revokeObjectURL(e.src),c(d)})})}).then(function(a){w(a,!0)},function(){})}function B(){a.addButton("rotateleft",{title:"Rotate counterclockwise",onclick:y(-90)}),a.addButton("rotateright",{title:"Rotate clockwise",onclick:y(90)}),a.addButton("flipv",{title:"Flip vertically",onclick:z("v")}),a.addButton("fliph",{title:"Flip horizontally",onclick:z("h")}),a.addButton("editimage",{title:"Edit image",onclick:A}),a.addButton("imageoptions",{title:"Image options",icon:"options",cmd:"mceImage"})}function C(){a.on("NodeChange",function(b){G&&G.src!=b.element.src&&(v(),a.editorUpload.uploadImagesAuto(),G=void 0),D(b.element)&&(G=b.element)})}function D(b){var c=a.dom.is(b,"img:not([data-mce-object],[data-mce-placeholder])");return c&&(o(b)||p(b)||a.settings.imagetools_proxy)}function E(){var b=a.settings.imagetools_toolbar;b||(b="rotateleft rotateright | flipv fliph | crop editimage imageoptions"),a.addContextToolbar(D,b)}var F,G,H=0;b.fileApi&&(B(),E(),C(),a.addCommand("mceEditImage",A))};return a.add("imagetools",j),function(){}}),d("0")()}();