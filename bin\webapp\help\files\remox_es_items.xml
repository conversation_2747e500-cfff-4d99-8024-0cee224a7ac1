﻿<?xml version="1.0" encoding="utf-8"?>
<SystemHelp xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <items>

    <Entry>
      <id>es-item</id>
      <title>إدارة الأصناف</title>
      <content>
        <![CDATA[
الأصناف هي البضائع أو الخدمات التي سيتم التعامل معها في المستندات، و غالبا هي البضائع التي تبيعها أو تشتريها الشركة، أو الخدمات التي تقدمها..

يوجد في النظام نوعين من الأصناف بحسب طبيعة الصنف: 
سلعة مخزنية: سلعة يتم تخزينها في المخزن ونحتاج نراقب مخزونها كم وارد وكم منصرف وكم باقي في المخزون.
خدمة/سلعة غير مخزنية: خدمات يتم تقديمها ولا نحتاج مراقبة مخزونها ويمكن مراقبة مبيعاتها.

يوفر النظام العديد من الخيارات والحقول ليتم تسجيل بيانات الصنف فيها، حيث يمكن ملء جميع الخيارات أو بعضها وذلك بما يناسب عمل المحل أو المؤسسة أو الشركة. 
-	يتيح النظام كتابة الاسم العلمي للصنف، وكذلك كتابة الاسم الأجنبي للصنف.
-	يتيح النظام كتابة رقم للصنف وباركود للصنف نفسه، حيث يمكن كتابتهما معا أو كتابة أحدهما، أو تركهما فراغين ليقوم النظام بكتابة الرقم تلقائيا.
-	يتيح النظام تصنف الأصناف إلى مجموعات ومجموعات فرعية.
-	يتيح النظام تحديد وحدة الشراء ووحدة البيع للصنف لتظهر تلقائيا في فاتورة الشراء وفاتورة البيع، مع إمكانية تغيير هذه الوحدة بسهولة في الفواتير.
-	يتيح النظام حرية تجزئة الصنف وحرية كتابة وحدات القياس.
-	يتيح النظام تحديد عملة أجنبية لتسعير الصنف ولكن يقوم بعرض ما يقابله من العملة الرئيسية "المحلية" في شاشة المبيعات، وهذا الخيار يعد مهم للمحلات التي تبيع أصنافها بما يقابل سعر الدولار، فإذا قام صاحب المحل بتسعير الأصناف بالدولار ولكنه يبيعها بما يقابل سعره بالريال اليمني فإنه سوف لن يحتاج إلى تغيير أسعار الأصناف كل ما تغيير سعر صرف الدولار، كلما يحتاج إليه هو تغيير سعر صرف الدولار مقابل الريال اليمني في شاشة تهيئة النظام-العملات وسيقوم النظام بتغيير أسعار جميع الأصناف طبقاً لسعر صرف اليوم.



]]>
      </content>
      <linked>item_flags</linked>
    </Entry>
    
     <Entry>
     <id>item_id</id>
     <title>رقم الصنف</title>
     <content>
       <![CDATA[
يتم في هذا الحقل إدخال رقم الصنف، كما يمكن تركه فارغا ليقوم النظام بترقيم الصنف آليا عند الحفظ.       
إذا تم تفعيل خيار (ترقيم الأصناف حسب المجموعات) من إعدادات نظام المخزون سوف يتم ترقيم الأصناف آليا بربط رقم المجموعة مع رقم الصنف و سيكون لكل مجموعة تسلسل مختلف
لا يمكن تعديل هذا الحقل

]]>
     </content>
   </Entry>

    <Entry>
      <id>item_code</id>
      <title>باركود الصنف</title>
      <content>
        <![CDATA[
رقم الباركود المطبوع على غلاف الصنف إذا كانت المؤسسة تستخدم قارئ الباركود، و يفضل إدخال هذا الرقم عن طريق قارئ الباركود لتجنب الخطأ. يمكن ترك هذ الحقل فارغا و سيقوم النظام بإستخدام رقم الصنف كباركود للصنف
]]>
      </content>
    </Entry>


    <Entry>
      <id>item_name</id>
      <title>اسم الصنف</title>
      <content>
        <![CDATA[
يتم إدخال اسم الصنف بالتفصيل، ويجب ان يكون واضح ومفهوم ومتفق عليه عند الجميع المحاسب الذي يصدر الفواتير وأمين المخازن الذي يصرف وفقاً لهذا الاسم.
]]>
      </content>
    </Entry>

    <Entry>
      <id>item_type</id>
      <title>طبيعة الصنف</title>
      <content>
        <![CDATA[
في هذا الحقل يجب تحديد طبيعة الصنف 

سلعة مخزنية: سلعة يتم تخزينها في المخزن ونحتاج نراقب مخزونها والكميات الوارد والمنصرف.
خدمة/سلعة غير مخزنية: خدمات يتم تقديمها ولا نحتاج مراقبة مخزونها ويمكن مراقبة مبيعاتها ومشترواتها.
        
 للتسهيل يقوم النظام بإختيار سلعة أو خدمة في هذا الحقل بحسب الاعدادات الأساسية للنظام مثلا عند تفعيل خيار (المؤسسة تقدم خدمات فقط - تعطيل نظام المخزون السلعي) يقوم النظام بإظهار (خدمة/سلعة غير مخزنية) في هذا الحقل وعند تعطيل هذا الخيار يقوم النظام بإظهار (سلعة مخزنية) في هذا الحقل.
]]>
      </content>
    </Entry>

    <Entry>
      <id>item_group</id>
      <title>مجموعة الصنف</title>
      <content>
        <![CDATA[
يتم من هذا الحقل اختيار الحقل المجموعة التي ينتمي لها هذا الصنف (تفيد في تصنف الأصناف إلى مجموعة والتي لها أهمية عند استخراج التقارير). إذا لم يكون اسم المجموعة موجود مسبقا يتم النقر على الزر  + التالي لهذا الحقل وكتابة اسم المجموعة.

هذا الحقل يكون إجباريا إذا تم تفعيل خيار (ترقيم الأصناف حسب المجموعات) أو إنه تم ربط المجموعات المخزنية المالية بمجموعة الأصناف
لا يمكن تعديل هذا الحقل بعد الحفظ إذا تم ربط المجموعات المخزنية بمجموعات الأصناف
]]>
      </content>
    </Entry>

    <Entry>
      <id>item_subgr</id>
      <title>المجموعة الفرعية</title>
      <content>
        <![CDATA[
حقل إختياري يستخدم لتحديد المجموعة الفرعية، و يمكن إستخدام هذا الحقل عنما يكون هناك حاجة لتفصيل مجموعة الأصناف
]]>
      </content>
    </Entry>

    <Entry>
      <id>item_crncy</id>
      <title>عملة تسعير الصنف</title>
      <content>
        <![CDATA[
حقل اجباري، يتم اختيار عملة تسعير البيع لهذا الصنف، يقوم النظام بإختيار عملة المخزون كعملة تلقائية لهذا الحقل ويمكن تغيرها الى عملة أخرى.
*ملاحظة: عند اختيار نوع عملة أجنبية للصنف سيقوم النظام بربط سعر صرف هذا العملة مقابل السعر العملة الرئيسية التي تتعامل بها المنشأة في نظام المبيعات، بمعنى أخر أذا تم اختيار على سبيل المثال الدولار كعملة لتسعير الصنف فإنه عند بيع هذا الصنف بفاتورة بيع عملتها (ريال) سيقوم النظام بعرض سعر هذا الصنف بنفس السعر ولكن بما يقابله بالعملة الرئيسية (الريال). مثال، إذا تم تسجيل عملة تسعير الصنف بالدولار، وكان سعر الصنف 3 دولار، وسعر الدولار يساوي 300 ريال، عند البيع سيعرض النظام سعر الصنف 900 ريال. 
أي سيقوم النظام بعملية تحويل من سعر الصنف بعملة تسعير الصنف إلى السعر بعملة الفاتورة و عند تغيير عملة الفاتورة سيقوم النظام بتغيير الأسعار آليا 

و يمكن تغيير عملة تسعير الصنف و سعر الصنف في أي وقت عند الحاجة لذلك.

]]>
      </content>
    </Entry>

    <Entry>
      <id></id>
      <title></title>
      <content>
        <![CDATA[

]]>
      </content>
    </Entry>

    <Entry>
      <id>item_flags</id>
      <title>خيارات الصنف</title>
      <content>
        <![CDATA[
       
<b class='title'>تفعيل الرصيد المالي للخدمة</b><hr/>
هذا الخيار يخدم الجهات التي تقوم بإعادة بيع الخدمات، أي أنها تشتري الخدمة بسعر معين من المقدم الفعلي للخدمة، وتقوم بإعادة بيعها. أي الجهات التي تعتبر وكيل أو موزع لمقدم الخدمة الفعلي. 
مثل شركات وكالات السفريات، أو الجهات التي تقدم  خدمات تسديد الفواتير أو شحن الأرصدة، وغيرها.. و تستخدم أيضا في حالة الخدمات المركبة

- ينطبق على أصناف الخدمات أو السلع الغير مخزنية، وليس لهذا الخيار أي تأثير إذا كان نوع الصنف سلعة مخزنية
- سيتم التعامل مع الخدمة وكأنه صنف مخزني، ولكن بدون كميات مخزنية
- يتم إعتماد مخزون مالي للخدمة وذلك عن طريق حساب مالي يعرف من خلال المجموعة المخزنية التي سيربط بها الصنف وذلك في حقل حساب المجموعة 
- و سوف يتم السحب من هذا الحساب عند بيع الخدمة، حيث سيتم السحب من حساب رصيد هذه الخدمة ويقيد كمصروف على حساب مصروفات كلفة الخدمة المباعة
- يمكن شراء وبيع هذه الخدمة بشكل طبيعي

- يجب تحديد كلفة الوحدة للصنف يدويا من شاشة الخدمات، و لن يتم عمل إحتساب آلي للكلفة
- هذا الخيار يتطلب تفعيل خيار ربط المجموعات المخزنية ماليا بمجموعة الأصناف



]]>
      </content>

    </Entry>


    <!--   
   <Entry>
     <id></id>
     <title></title>
     <content>
       <![CDATA[

]]>
     </content>
   </Entry>
-->
  </items>
</SystemHelp>