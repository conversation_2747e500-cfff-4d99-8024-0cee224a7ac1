2025/07/26 00:04:49.389 ; Info ; 1 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/07/26 00:04:49.514 ; Trace ; 1 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/07/26 00:04:49.529 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/07/26 00:04:49.529 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/07/26 00:04:49.529 ; Log ; 1 ;  ; 0000: ; 26/7/2025 00:04:49 @Http Req#0
2025/07/26 00:04:49.529 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/07/26 00:04:49.529 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 673  @Http Req#0
2025/07/26 00:04:49.529 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/07/26 00:04:49.529 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/07/26 00:04:49.529 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/07/26 00:04:49.608 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/07/26 00:04:49.623 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/07/26 00:04:49.670 ; Info ; 1 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/07/26 00:04:49.686 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/07/26 00:04:49.686 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/07/26 00:04:49.701 ; Trace ; 1 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/07/26 00:04:49.717 ; Trace ; 1 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/07/26 00:04:49.717 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/07/26 00:04:49.717 ; Trace ; 1 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/07/26 00:04:49.717 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/07/26 00:04:49.732 ; Info ; 1 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/07/26 00:04:49.779 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/07/26 00:04:49.795 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/07/26 00:04:49.873 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/07/26 00:04:49.889 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/07/26 00:04:49.889 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/07/26 00:04:49.904 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/07/26 00:04:50.014 ; Trace ; 1 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/07/26 00:04:50.014 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/07/26 00:04:50.014 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/07/26 00:04:50.014 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/07/26 00:04:50.014 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/07/26 00:04:50.014 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/07/26 00:04:50.029 ; Trace ; 1 ;  ; 0000: ; HsStoredObject cached: 0 @Http Req#0
2025/07/26 00:04:50.217 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/07/26 00:04:50.217 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/07/26 00:04:50.217 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/07/26 00:04:50.233 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/07/26 00:04:50.233 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/07/26 00:04:50.233 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/07/26 00:04:50.233 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/07/26 00:04:50.264 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Building forms auth roles @Http Req#1
2025/07/26 00:04:50.264 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Failed to load form:login @Http Req#1
2025/07/26 00:04:50.264 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Failed to load form:logout @Http Req#1
2025/07/26 00:04:50.295 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/07/26 00:04:50.326 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/07/26 00:04:50.326 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Failed to load form:relation @Http Req#1
2025/07/26 00:04:50.326 ; Log ; 9 ; 176.123.27.8 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/07/26 00:04:50.342 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/07/26 00:04:55.242 ; Log ; 7 ;  ; 0000: ; Started threaded queue processor: DelayedSqlQueue @Http Req#1
2025/07/26 00:04:55.242 ; Log ; 8 ;  ; 0000: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#1
2025/07/26 00:04:55.242 ; Log ; 8 ;  ; 0000: ; Loading scheduled tasks.. @Http Req#1
2025/07/26 00:04:58.748 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/07/26 00:04:58.967 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/07/26 00:04:59.655 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/07/26 00:04:59.702 ; Info ; 9 ; 176.123.27.8 ; 9900: ; Init Client: 9900 @Http Req#1
2025/07/26 00:04:59.717 ; Log ; 9 ; 176.123.27.8 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#1
2025/07/26 00:04:59.717 ; Log ; 9 ; 176.123.27.8 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#1
2025/07/26 00:04:59.717 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#1
2025/07/26 00:04:59.733 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; COA.RebuildAccountsTree @Http Req#1
2025/07/26 00:04:59.733 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; Accounts loaded:65 @Http Req#1
2025/07/26 00:04:59.733 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#1
2025/07/26 00:04:59.733 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; Accounts loaded:0 @Http Req#1
2025/07/26 00:04:59.733 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; Caching Sales Items:9900 @Http Req#1
2025/07/26 00:04:59.733 ; Log ; 9 ; 176.123.27.8 ; 9900: ; Caching SalesItem.. @Http Req#1
2025/07/26 00:04:59.749 ; Log ; 9 ; 176.123.27.8 ; 9900: ; Sales Items Cached: 0 @Http Req#1
2025/07/26 00:04:59.764 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; Caching all items stock data.. @Http Req#1
2025/07/26 00:04:59.780 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#1
2025/07/26 00:04:59.780 ; Log ; 9 ; 176.123.27.8 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#1
2025/07/26 00:04:59.780 ; Log ; 9 ; 176.123.27.8 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#1
2025/07/26 00:04:59.780 ; Log ; 9 ; 176.123.27.8 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#1
2025/07/26 00:04:59.780 ; Info ; 9 ; 176.123.27.8 ; 9900: ; Init Client Completed: 9900 @Http Req#1
2025/07/26 00:04:59.795 ; Trace ; 9 ; 176.123.27.8 ; 9900:admin ; Loading user fav menu:admin @Http Req#1
2025/07/26 00:04:59.811 ; Log ; 9 ; 176.123.27.8 ; 9900:admin ; Successfull user login:admin @Http Req#1
2025/07/26 00:04:59.811 ; Trace ; 9 ; 176.123.27.8 ; 9900:admin ; Redirecting user to: /app @Http Req#1
2025/07/26 00:04:59.936 ; Info ; 9 ; 176.123.27.8 ; 9900:admin ;  ;  ; /app ; 176.123.27.8 @Http Req#2 @Req#1 0s
2025/07/26 00:05:01.478 ; Info ; 10 ; 176.123.27.8 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; 176.123.27.8 @Http Req#3 @Req#2 0s

2025/07/26 00:25:50.266 ; Trace ; 84 ;  ; 9900: ; Session End: User logged out @Http Req#3
2025/07/26 00:25:50.266 ; Log ; 84 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads... @Http Req#3
2025/07/26 00:25:50.266 ; Log ; 7 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue @Http Req#3
2025/07/26 00:25:50.266 ; Log ; 8 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue @Http Req#3
2025/07/26 00:25:50.282 ; Log ; 84 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ******** @Http Req#3

2025/07/26 00:30:09.601 ; Info ; 1 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/07/26 00:30:09.711 ; Trace ; 1 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/07/26 00:30:09.711 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/07/26 00:30:09.711 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/07/26 00:30:09.711 ; Log ; 1 ;  ; 0000: ; 26/7/2025 00:30:09 @Http Req#0
2025/07/26 00:30:09.711 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/07/26 00:30:09.711 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 673  @Http Req#0
2025/07/26 00:30:09.726 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/07/26 00:30:09.726 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/07/26 00:30:09.726 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/07/26 00:30:09.789 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/07/26 00:30:09.789 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/07/26 00:30:09.820 ; Info ; 1 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/07/26 00:30:09.851 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/07/26 00:30:09.851 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/07/26 00:30:09.867 ; Trace ; 1 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/07/26 00:30:09.883 ; Trace ; 1 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/07/26 00:30:09.883 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/07/26 00:30:09.883 ; Trace ; 1 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/07/26 00:30:09.883 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/07/26 00:30:09.898 ; Info ; 1 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/07/26 00:30:09.945 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/07/26 00:30:09.945 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/07/26 00:30:09.961 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/07/26 00:30:09.961 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/07/26 00:30:09.961 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/07/26 00:30:09.976 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/07/26 00:30:09.992 ; Trace ; 1 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/07/26 00:30:10.008 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/07/26 00:30:10.008 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/07/26 00:30:10.008 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/07/26 00:30:10.008 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/07/26 00:30:10.008 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/07/26 00:30:10.023 ; Trace ; 1 ;  ; 0000: ; HsStoredObject cached: 0 @Http Req#0
2025/07/26 00:30:10.101 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/07/26 00:30:10.101 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/07/26 00:30:10.101 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/07/26 00:30:10.101 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/07/26 00:30:10.101 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/07/26 00:30:10.101 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/07/26 00:30:10.101 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/07/26 00:30:10.133 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Building forms auth roles @Http Req#1
2025/07/26 00:30:10.133 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Failed to load form:login @Http Req#1
2025/07/26 00:30:10.133 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Failed to load form:logout @Http Req#1
2025/07/26 00:30:10.164 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/07/26 00:30:10.195 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/07/26 00:30:10.195 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Failed to load form:relation @Http Req#1
2025/07/26 00:30:10.195 ; Log ; 9 ; 176.123.27.8 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/07/26 00:30:10.211 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/07/26 00:30:15.118 ; Log ; 8 ;  ; 0000: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#1
2025/07/26 00:30:15.118 ; Log ; 8 ;  ; 0000: ; Loading scheduled tasks.. @Http Req#1
2025/07/26 00:30:15.118 ; Log ; 7 ;  ; 0000: ; Started threaded queue processor: DelayedSqlQueue @Http Req#1
2025/07/26 00:30:18.572 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/07/26 00:30:18.666 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/07/26 00:30:18.947 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/07/26 00:30:18.978 ; Info ; 9 ; 176.123.27.8 ; 9900: ; Init Client: 9900 @Http Req#2 @Req#1 0s
2025/07/26 00:30:18.994 ; Log ; 9 ; 176.123.27.8 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/07/26 00:30:19.009 ; Log ; 9 ; 176.123.27.8 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/07/26 00:30:19.009 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/07/26 00:30:19.025 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; COA.RebuildAccountsTree @Http Req#2 @Req#1 0s
2025/07/26 00:30:19.025 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; Accounts loaded:65 @Http Req#2 @Req#1 0s
2025/07/26 00:30:19.025 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/07/26 00:30:19.025 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; Accounts loaded:0 @Http Req#2 @Req#1 0s
2025/07/26 00:30:19.025 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; Caching Sales Items:9900 @Http Req#2 @Req#1 0s
2025/07/26 00:30:19.025 ; Log ; 9 ; 176.123.27.8 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/07/26 00:30:19.041 ; Log ; 9 ; 176.123.27.8 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/07/26 00:30:19.041 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; Caching all items stock data.. @Http Req#2 @Req#1 0s
2025/07/26 00:30:19.056 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#2 @Req#1 0s
2025/07/26 00:30:19.056 ; Log ; 9 ; 176.123.27.8 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/07/26 00:30:19.056 ; Log ; 9 ; 176.123.27.8 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/07/26 00:30:19.056 ; Log ; 9 ; 176.123.27.8 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/07/26 00:30:19.056 ; Info ; 9 ; 176.123.27.8 ; 9900: ; Init Client Completed: 9900 @Http Req#2 @Req#1 0s
2025/07/26 00:30:42.050 ; Trace ; 9 ; 176.123.27.8 ; 9900:admin ; Loading user fav menu:admin @Http Req#5 @Req#4 0s
2025/07/26 00:30:42.050 ; Log ; 9 ; 176.123.27.8 ; 9900:admin ; Successfull user login:admin @Http Req#5 @Req#4 0s
2025/07/26 00:30:42.050 ; Trace ; 9 ; 176.123.27.8 ; 9900:admin ; Redirecting user to: /app @Http Req#5 @Req#4 0s
2025/07/26 00:30:42.097 ; Info ; 13 ; 176.123.27.8 ; 9900:admin ;  ;  ; /app ; 176.123.27.8 @Http Req#6 @Req#5 0s
2025/07/26 00:30:42.206 ; Info ; 5 ; 176.123.27.8 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; 176.123.27.8 @Http Req#7 @Req#6 0s

2025/07/26 00:42:50.080 ; Info ; 1 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/07/26 00:42:50.302 ; Trace ; 1 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/07/26 00:42:50.311 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/07/26 00:42:50.312 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/07/26 00:42:50.312 ; Log ; 1 ;  ; 0000: ; 26/7/2025 00:42:50 @Http Req#0
2025/07/26 00:42:50.312 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/07/26 00:42:50.312 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 673  @Http Req#0
2025/07/26 00:42:50.312 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/07/26 00:42:50.316 ; Log ; 1 ;  ; 0000: ; Last shutdown was not clean, check the system @Http Req#0
2025/07/26 00:42:50.323 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/07/26 00:42:50.326 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/07/26 00:42:50.387 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/07/26 00:42:50.387 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/07/26 00:42:50.433 ; Info ; 1 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/07/26 00:42:50.456 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/07/26 00:42:50.459 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/07/26 00:42:50.466 ; Trace ; 1 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/07/26 00:42:50.480 ; Trace ; 1 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/07/26 00:42:50.480 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/07/26 00:42:50.480 ; Trace ; 1 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/07/26 00:42:50.483 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/07/26 00:42:50.487 ; Info ; 1 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/07/26 00:42:50.519 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/07/26 00:42:50.522 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/07/26 00:42:50.553 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/07/26 00:42:50.553 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/07/26 00:42:50.554 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/07/26 00:42:50.556 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/07/26 00:42:50.604 ; Trace ; 1 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/07/26 00:42:50.605 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/07/26 00:42:50.605 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/07/26 00:42:50.606 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/07/26 00:42:50.608 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/07/26 00:42:50.620 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/07/26 00:42:50.622 ; Trace ; 1 ;  ; 0000: ; HsStoredObject cached: 0 @Http Req#0
2025/07/26 00:42:50.714 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/07/26 00:42:50.714 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/07/26 00:42:50.714 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/07/26 00:42:50.714 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/07/26 00:42:50.715 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/07/26 00:42:50.715 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/07/26 00:42:50.715 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/07/26 00:42:50.833 ; Trace ; 12 ; 176.123.27.8 ; 0000: ; Building forms auth roles @Http Req#1
2025/07/26 00:42:50.838 ; Trace ; 12 ; 176.123.27.8 ; 0000: ; Failed to load form:login @Http Req#1
2025/07/26 00:42:50.839 ; Trace ; 12 ; 176.123.27.8 ; 0000: ; Failed to load form:logout @Http Req#1
2025/07/26 00:42:50.886 ; Trace ; 12 ; 176.123.27.8 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/07/26 00:42:50.918 ; Trace ; 12 ; 176.123.27.8 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/07/26 00:42:50.918 ; Trace ; 12 ; 176.123.27.8 ; 0000: ; Failed to load form:relation @Http Req#1
2025/07/26 00:42:50.923 ; Log ; 12 ; 176.123.27.8 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/07/26 00:42:50.933 ; Trace ; 12 ; 176.123.27.8 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/07/26 00:42:55.716 ; Log ; 10 ;  ; 0000: ; Started threaded queue processor: DelayedSqlQueue @Http Req#1
2025/07/26 00:42:55.717 ; Log ; 11 ;  ; 0000: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#1
2025/07/26 00:42:55.718 ; Log ; 11 ;  ; 0000: ; Loading scheduled tasks.. @Http Req#1
2025/07/26 00:42:59.199 ; Trace ; 12 ; 176.123.27.8 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/07/26 00:42:59.296 ; Trace ; 12 ; 176.123.27.8 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/07/26 00:42:59.569 ; Trace ; 12 ; 176.123.27.8 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/07/26 00:42:59.669 ; Info ; 12 ; 176.123.27.8 ; 9900: ; Init Client: 9900 @Http Req#2 @Req#1 0s
2025/07/26 00:42:59.687 ; Log ; 12 ; 176.123.27.8 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/07/26 00:42:59.688 ; Log ; 12 ; 176.123.27.8 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/07/26 00:42:59.692 ; Trace ; 12 ; 176.123.27.8 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/07/26 00:42:59.705 ; Trace ; 12 ; 176.123.27.8 ; 9900: ; COA.RebuildAccountsTree @Http Req#2 @Req#1 0s
2025/07/26 00:42:59.705 ; Trace ; 12 ; 176.123.27.8 ; 9900: ; Accounts loaded:65 @Http Req#2 @Req#1 0s
2025/07/26 00:42:59.705 ; Trace ; 12 ; 176.123.27.8 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/07/26 00:42:59.707 ; Trace ; 12 ; 176.123.27.8 ; 9900: ; Accounts loaded:0 @Http Req#2 @Req#1 0s
2025/07/26 00:42:59.708 ; Trace ; 12 ; 176.123.27.8 ; 9900: ; Caching Sales Items:9900 @Http Req#2 @Req#1 0s
2025/07/26 00:42:59.710 ; Log ; 12 ; 176.123.27.8 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/07/26 00:42:59.726 ; Log ; 12 ; 176.123.27.8 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/07/26 00:42:59.726 ; Trace ; 12 ; 176.123.27.8 ; 9900: ; Caching all items stock data.. @Http Req#2 @Req#1 0s
2025/07/26 00:42:59.740 ; Trace ; 12 ; 176.123.27.8 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#2 @Req#1 0s
2025/07/26 00:42:59.744 ; Log ; 12 ; 176.123.27.8 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/07/26 00:42:59.744 ; Log ; 12 ; 176.123.27.8 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/07/26 00:42:59.747 ; Log ; 12 ; 176.123.27.8 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/07/26 00:42:59.748 ; Info ; 12 ; 176.123.27.8 ; 9900: ; Init Client Completed: 9900 @Http Req#2 @Req#1 0s

2025/07/26 00:44:14.181 ; Trace ; 35 ; 176.123.27.8 ; 9900:guest ; Loading user fav menu:guest @Http Req#3 @Req#2 0s
2025/07/26 00:44:14.197 ; Log ; 35 ; 176.123.27.8 ; 9900:guest ; App Comm Login Success, using user=9900:guest @Http Req#3 @Req#2 0s
2025/07/26 00:44:33.209 ; Trace ; 35 ; 176.123.27.8 ; 9900:guest ; AuthCheck Failed: biz(14,) @Http Req#6 @Req#5 0s
2025/07/26 00:44:33.209 ; Trace ; 35 ; 176.123.27.8 ; 9900:guest ; AuthCheck Failed: fm(vucost,es-item) @Http Req#6 @Req#5 0s
2025/07/26 00:44:33.209 ; Trace ; 35 ; 176.123.27.8 ; 9900:guest ; AuthCheck Failed: fm(vuamnts,es-so-cust) @Http Req#6 @Req#5 0s
2025/07/26 00:44:33.209 ; Trace ; 35 ; 176.123.27.8 ; 9900:guest ; AuthCheck Failed: fm(add,es-so-cust) @Http Req#6 @Req#5 0s

2025/07/26 00:50:26.584 ; Trace ; 69 ; 176.123.27.8 ; 9900:admin ; Loading user fav menu:admin @Http Req#9 @Req#8 0s
2025/07/26 00:50:26.584 ; Log ; 69 ; 176.123.27.8 ; 9900:admin ; Successfull user login:admin @Http Req#9 @Req#8 0s
2025/07/26 00:50:26.584 ; Trace ; 69 ; 176.123.27.8 ; 9900:admin ; Redirecting user to: /app/ @Http Req#9 @Req#8 0s
2025/07/26 00:50:26.646 ; Info ; 73 ; 176.123.27.8 ; 9900:admin ;  ;  ; /app/ ; 176.123.27.8 @Req#9 0s
2025/07/26 00:50:33.898 ; Info ; 72 ; 176.123.27.8 ; 9900:admin ;  ;  ; /app/ ; 176.123.27.8 @Req#10 0s
2025/07/26 00:50:52.154 ; Info ; 72 ; 176.123.27.8 ; 9900:admin ;  ;  ; /app/ ; 176.123.27.8 @Req#11 0s

2025/07/26 00:51:12.549 ; Info ; 72 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; es-sys-stock ; /app/fms/?fm=sys-cfg&id=es-sys-stock ; 176.123.27.8 @Req#12 0s
2025/07/26 00:51:38.148 ; Info ; 63 ; 176.123.27.8 ; 9900:admin ; coding ;  ; /app/fms/?fm=coding&fk0=es-itmgr&sind=y ; 176.123.27.8 @Req#13 0s
2025/07/26 00:51:51.403 ; Info ; 68 ; 176.123.27.8 ; 9900:admin ; hs-cms ;  ; /app/fms/?fm=hs-cms&cmd=list ; 176.123.27.8 @Req#14 0s
2025/07/26 00:51:55.592 ; Info ; 63 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; es-sys-stock ; /app/fms/?fm=sys-cfg&id=es-sys-stock ; 176.123.27.8 @Req#15 0s

2025/07/26 00:52:38.649 ; Info ; 81 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; es-sys-stock ; /app/fms/?fm=sys-cfg&id=es-sys-stock ; 176.123.27.8 @Req#16 0s

2025/07/26 00:59:06.953 ; Info ; 102 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; es-sys-stock ; /app/fms/?fm=sys-cfg&id=es-sys-stock ; 176.123.27.8 @Req#18 0s

2025/07/26 01:01:44.372 ; Info ; 123 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; es-sys-stock ; /app/fms/?fm=sys-cfg&id=es-sys-stock ; 176.123.27.8 @Req#20 0s

2025/07/26 01:09:37.193 ; Info ; 169 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; es-sys-stock ; /app/fms/?fm=sys-cfg&id=es-sys-stock ; 176.123.27.8 @Req#22 0s

2025/07/26 01:11:30.064 ; Info ; 181 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; es-sys-stock ; /app/fms/?fm=sys-cfg&id=es-sys-stock ; 176.123.27.8 @Req#24 0s

2025/07/26 01:12:18.864 ; Info ; 174 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; es-sys-stock ; /app/fms/?fm=sys-cfg&id=es-sys-stock ; 176.123.27.8 @Req#26 0s

2025/07/26 01:14:25.301 ; Info ; 185 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; es-sys-stock ; /app/fms/?fm=sys-cfg&id=es-sys-stock ; 176.123.27.8 @Req#28 0s

2025/07/26 01:14:58.738 ; Info ; 173 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; hs-sys-clt ; /app/fms/?fm=sys-cfg&id=hs-sys-clt ; 176.123.27.8 @Req#30 0s
2025/07/26 01:15:20.835 ; Info ; 162 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; hs-sys-clt ; /app/fms/?fm=sys-cfg&id=hs-sys-clt ; 176.123.27.8 @Req#31 0s
2025/07/26 01:15:24.339 ; Info ; 180 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; hs-sys-clt ; /app/fms/?fm=sys-cfg&id=hs-sys-clt ; 176.123.27.8 @Req#32 0s

2025/07/26 01:15:56.615 ; Info ; 159 ; 176.123.27.8 ; 9900:admin ; sys-cfg ;  ; /app/fms/?fm=sys-cfg&cmd=list ; 176.123.27.8 @Req#34 0s
2025/07/26 01:15:59.745 ; Info ; 147 ; 176.123.27.8 ; 9900:admin ; sys-cfg ;  ; /app/fms/?fm=sys-cfg&cmd=list ; 176.123.27.8 @Req#35 0s
2025/07/26 01:16:02.193 ; Info ; 161 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; hs-sys-clt ; /app/fms/?fm=sys-cfg&id=hs-sys-clt ; 176.123.27.8 @Req#36 0s
2025/07/26 01:16:04.272 ; Info ; 173 ; 176.123.27.8 ; 9900:admin ; sys-cfg ;  ; /app/fms/?fm=sys-cfg&cmd=browse ; 176.123.27.8 @Req#37 0s
2025/07/26 01:16:44.533 ; Trace ; 190 ; 176.123.27.8 ; 9900:admin ; Session End: User logged out @Req#41 0s
2025/07/26 01:16:44.549 ; Trace ; 190 ; 176.123.27.8 ; 9900:admin ; Removed from cache: sdebeuhbv4cj1gbqzq3q1gv5 @Req#41 0s
2025/07/26 01:16:44.549 ; Trace ; 162 ;  ; 9900: ; Session End: User logged out

2025/07/26 01:16:56.083 ; Trace ; 85 ; 176.123.27.8 ; 9900:admin ; Loading user fav menu:admin @Req#47 0s
2025/07/26 01:16:56.083 ; Log ; 85 ; 176.123.27.8 ; 9900:admin ; Successfull user login:admin @Req#47 0s
2025/07/26 01:16:56.083 ; Trace ; 85 ; 176.123.27.8 ; 9900:admin ; Redirecting user to: /app/fms/?fm=sys-cfg&cmd=browse @Req#47 0s
2025/07/26 01:16:56.130 ; Info ; 156 ; 176.123.27.8 ; 9900:admin ; sys-cfg ;  ; /app/fms/?fm=sys-cfg&cmd=browse ; 176.123.27.8 @Req#48 0s
2025/07/26 01:16:56.255 ; Info ; 152 ; 176.123.27.8 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; 176.123.27.8 @Req#49 0s

2025/07/26 01:18:28.697 ; Trace ; 185 ; 176.123.27.8 ; 9900:guest ; Loading user fav menu:guest @Req#50 0s
2025/07/26 01:18:28.697 ; Log ; 185 ; 176.123.27.8 ; 9900:guest ; App Comm Login Success, using user=9900:guest @Req#50 0s
2025/07/26 01:18:45.869 ; Info ; 156 ; 176.123.27.8 ; 9900:admin ;  ; sys-menu ; /app/?menu&id=sys-menu&sind=y ; 176.123.27.8 @Req#51 0s
2025/07/26 01:18:50.277 ; Info ; 144 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; es-sys-stock ; /app/fms/?fm=sys-cfg&id=es-sys-stock&sind=y ; 176.123.27.8 @Req#52 0s

2025/07/26 01:19:03.575 ; Info ; 144 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; hs-sys-clt ; /app/fms/?fm=sys-cfg&id=hs-sys-clt ; 176.123.27.8 @Req#53 0s
2025/07/26 01:19:13.614 ; Info ; 135 ; 176.123.27.8 ; 9900:admin ;  ; sys-menu ; /app/?menu&id=sys-menu&sind=y ; 176.123.27.8 @Req#54 0s
2025/07/26 01:19:15.077 ; Info ; 128 ; 176.123.27.8 ; 9900:admin ; user ;  ; /app/fms/?fm=user&sind=y ; 176.123.27.8 @Req#55 0s
2025/07/26 01:19:16.865 ; Info ; 149 ; 176.123.27.8 ; 9900:admin ; user ;  ; /app/fms/?fm=user&sind=y ; 176.123.27.8 @Req#56 0s
2025/07/26 01:19:19.265 ; Info ; 137 ; 176.123.27.8 ; 9900:admin ; user ; admin ; /app/fms/?fm=user&cmd=view&id=admin ; 176.123.27.8 @Req#57 0s
2025/07/26 01:19:21.225 ; Info ; 138 ; 176.123.27.8 ; 9900:admin ; user ; admin ; /app/fms/?fm=user&cmd=edit&id=admin&sind=y ; 176.123.27.8 @Req#58 0s
2025/07/26 01:19:26.052 ; Info ; 142 ; 176.123.27.8 ; 9900:admin ; user ; admin ; /app/fms/?fm=user&cmd=edit&id=admin&sind=y ; 176.123.27.8 @Req#59 0s
2025/07/26 01:19:26.146 ; Info ; 137 ; 176.123.27.8 ; 9900:admin ; user ; admin ; /app/fms/?fm=user&cmd=view&id=admin ; 176.123.27.8 @Req#60 0s
2025/07/26 01:19:37.767 ; Info ; 143 ; 176.123.27.8 ; 9900:admin ;  ; sys-menu ; /app/?menu&id=sys-menu&sind=y ; 176.123.27.8 @Req#61 0s
2025/07/26 01:19:41.340 ; Info ; 152 ; 176.123.27.8 ; 9900:admin ;  ;  ; /app/?restart ; 176.123.27.8 @Req#62 0s
2025/07/26 01:19:41.340 ; Log ; 152 ; 176.123.27.8 ; 9900:admin ; System Restart:  @Req#62 0s
2025/07/26 01:19:41.375 ; Trace ; 142 ;  ; 9900: ; Session End: User logged out
2025/07/26 01:19:41.375 ; Trace ; 142 ;  ; 9900: ; Session End: User logged out
2025/07/26 01:19:41.375 ; Log ; 142 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads...
2025/07/26 01:19:41.375 ; Log ; 10 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue
2025/07/26 01:19:41.375 ; Log ; 11 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue
2025/07/26 01:19:41.375 ; Log ; 142 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ********

2025/07/26 01:19:41.453 ; Info ; 131 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/07/26 01:19:41.484 ; Trace ; 131 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/07/26 01:19:41.484 ; Log ; 131 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/07/26 01:19:41.484 ; Log ; 131 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/07/26 01:19:41.484 ; Log ; 131 ;  ; 0000: ; 26/7/2025 01:19:41 @Http Req#0
2025/07/26 01:19:41.500 ; Log ; 131 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/07/26 01:19:41.500 ; Log ; 131 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 673  @Http Req#0
2025/07/26 01:19:41.500 ; Log ; 131 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/07/26 01:19:41.500 ; Log ; 131 ;  ; 0000: ; Loading app modules @Http Req#0
2025/07/26 01:19:41.500 ; Log ; 131 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/07/26 01:19:41.547 ; Log ; 131 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/07/26 01:19:41.547 ; Log ; 131 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/07/26 01:19:41.578 ; Info ; 131 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/07/26 01:19:41.593 ; Log ; 131 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/07/26 01:19:41.593 ; Log ; 131 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/07/26 01:19:41.593 ; Trace ; 131 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/07/26 01:19:41.593 ; Trace ; 131 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/07/26 01:19:41.593 ; Trace ; 131 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/07/26 01:19:41.593 ; Trace ; 131 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/07/26 01:19:41.609 ; Trace ; 131 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/07/26 01:19:41.609 ; Info ; 131 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/07/26 01:19:41.625 ; Log ; 131 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/07/26 01:19:41.640 ; Log ; 131 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/07/26 01:19:41.656 ; Log ; 131 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/07/26 01:19:41.656 ; Log ; 131 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/07/26 01:19:41.656 ; Log ; 131 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/07/26 01:19:41.656 ; Log ; 131 ;  ; 0000: ; Loading app config @Http Req#0
2025/07/26 01:19:41.703 ; Trace ; 131 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/07/26 01:19:41.703 ; Log ; 131 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/07/26 01:19:41.703 ; Log ; 131 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/07/26 01:19:41.718 ; Log ; 131 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/07/26 01:19:41.718 ; Log ; 131 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/07/26 01:19:41.734 ; Log ; 131 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/07/26 01:19:41.734 ; Trace ; 131 ;  ; 0000: ; HsStoredObject cached: 0 @Http Req#0
2025/07/26 01:19:41.797 ; Log ; 131 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/07/26 01:19:41.797 ; Log ; 131 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/07/26 01:19:41.797 ; Log ; 131 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/07/26 01:19:41.797 ; Log ; 131 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/07/26 01:19:41.797 ; Log ; 131 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/07/26 01:19:41.797 ; Log ; 131 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/07/26 01:19:41.797 ; Log ; 131 ;  ; 0000: ; >>> App started @Http Req#0
2025/07/26 01:19:41.828 ; Trace ; 149 ; 176.123.27.8 ; 0000: ; Building forms auth roles @Http Req#1
2025/07/26 01:19:41.828 ; Trace ; 149 ; 176.123.27.8 ; 0000: ; Failed to load form:login @Http Req#1
2025/07/26 01:19:41.828 ; Trace ; 149 ; 176.123.27.8 ; 0000: ; Failed to load form:logout @Http Req#1
2025/07/26 01:19:41.843 ; Trace ; 149 ; 176.123.27.8 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/07/26 01:19:41.869 ; Trace ; 149 ; 176.123.27.8 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/07/26 01:19:41.869 ; Trace ; 149 ; 176.123.27.8 ; 0000: ; Failed to load form:relation @Http Req#1
2025/07/26 01:19:41.885 ; Log ; 149 ; 176.123.27.8 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/07/26 01:19:41.885 ; Trace ; 149 ; 176.123.27.8 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/07/26 01:19:46.818 ; Log ; 139 ;  ; 0000: ; Started threaded queue processor: DelayedSqlQueue @Http Req#1
2025/07/26 01:19:46.818 ; Log ; 140 ;  ; 0000: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#1
2025/07/26 01:19:46.818 ; Log ; 140 ;  ; 0000: ; Loading scheduled tasks.. @Http Req#1
2025/07/26 01:19:50.309 ; Trace ; 149 ; 176.123.27.8 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/07/26 01:19:50.356 ; Trace ; 149 ; 176.123.27.8 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/07/26 01:19:50.543 ; Trace ; 149 ; 176.123.27.8 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/07/26 01:19:50.590 ; Info ; 148 ; 176.123.27.8 ; 9900: ; Init Client: 9900 @Http Req#2 @Req#1 0s
2025/07/26 01:19:50.606 ; Log ; 148 ; 176.123.27.8 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/07/26 01:19:50.606 ; Log ; 148 ; 176.123.27.8 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/07/26 01:19:50.606 ; Trace ; 148 ; 176.123.27.8 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/07/26 01:19:50.606 ; Trace ; 148 ; 176.123.27.8 ; 9900: ; COA.RebuildAccountsTree @Http Req#2 @Req#1 0s
2025/07/26 01:19:50.606 ; Trace ; 148 ; 176.123.27.8 ; 9900: ; Accounts loaded:65 @Http Req#2 @Req#1 0s
2025/07/26 01:19:50.606 ; Trace ; 148 ; 176.123.27.8 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/07/26 01:19:50.606 ; Trace ; 148 ; 176.123.27.8 ; 9900: ; Accounts loaded:0 @Http Req#2 @Req#1 0s
2025/07/26 01:19:50.606 ; Trace ; 148 ; 176.123.27.8 ; 9900: ; Caching Sales Items:9900 @Http Req#2 @Req#1 0s
2025/07/26 01:19:50.606 ; Log ; 148 ; 176.123.27.8 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/07/26 01:19:50.621 ; Log ; 148 ; 176.123.27.8 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/07/26 01:19:50.621 ; Trace ; 148 ; 176.123.27.8 ; 9900: ; Caching all items stock data.. @Http Req#2 @Req#1 0s
2025/07/26 01:19:50.621 ; Trace ; 148 ; 176.123.27.8 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#2 @Req#1 0s
2025/07/26 01:19:50.621 ; Log ; 148 ; 176.123.27.8 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/07/26 01:19:50.621 ; Log ; 148 ; 176.123.27.8 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/07/26 01:19:50.637 ; Log ; 148 ; 176.123.27.8 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/07/26 01:19:50.637 ; Info ; 148 ; 176.123.27.8 ; 9900: ; Init Client Completed: 9900 @Http Req#2 @Req#1 0s
2025/07/26 01:19:54.809 ; Trace ; 137 ; 176.123.27.8 ; 9900:guest ; Loading user fav menu:guest @Http Req#5 @Req#4 0s
2025/07/26 01:19:54.809 ; Log ; 137 ; 176.123.27.8 ; 9900:guest ; App Comm Login Success, using user=9900:guest @Http Req#5 @Req#4 0s
2025/07/26 01:20:05.517 ; Trace ; 152 ; 176.123.27.8 ; 9900:guest ; Session End: User logged out @Http Req#7 @Req#6 0s
2025/07/26 01:20:05.517 ; Trace ; 152 ; 176.123.27.8 ; 9900:guest ; Removed from cache: vkyyr12g35qftt5oyeunog3e @Http Req#7 @Req#6 0s
2025/07/26 01:20:11.238 ; Trace ; 146 ; 176.123.27.8 ; 9900:770022600 ; Loading user fav menu:770022600 @Http Req#9 @Req#8 0s
2025/07/26 01:20:11.254 ; Log ; 146 ; 176.123.27.8 ; 9900:770022600 ; Successfull user login:770022600 @Http Req#9 @Req#8 0s
2025/07/26 01:20:11.254 ; Trace ; 146 ; 176.123.27.8 ; 9900:770022600 ; Redirecting user to: /app/fms/?fm=e-shop&cmd=profile&fm-mode=9900 @Http Req#9 @Req#8 0s
2025/07/26 01:20:30.446 ; Info ; 141 ; 176.123.27.8 ; 9900:770022600 ; user-tools ;  ; /app/fms/?fm=user-tools&fm-mode=user-defs&ux-mob-app=y ; 176.123.27.8 @Req#13 0s

2025/07/26 01:20:52.828 ; Trace ; 144 ; 176.123.27.8 ; 9900:770022600 ; AuthCheck Failed: biz(14,) @Req#15 0s
2025/07/26 01:20:52.828 ; Trace ; 144 ; 176.123.27.8 ; 9900:770022600 ; AuthCheck Failed: fm(vucost,es-item) @Req#15 0s
2025/07/26 01:20:52.828 ; Trace ; 144 ; 176.123.27.8 ; 9900:770022600 ; AuthCheck Failed: fm(vuamnts,es-so-cust) @Req#15 0s
2025/07/26 01:20:52.828 ; Trace ; 144 ; 176.123.27.8 ; 9900:770022600 ; AuthCheck Failed: biz(09,) @Req#15 0s
2025/07/26 01:21:00.484 ; Trace ; 135 ; 176.123.27.8 ; 9900:770022600 ; Session End: User logged out @Req#18 0s
2025/07/26 01:21:00.484 ; Trace ; 134 ;  ; 9900: ; Session End: User logged out
2025/07/26 01:21:00.531 ; Trace ; 146 ; 176.123.27.8 ; 9900:guest ; Loading user fav menu:guest @Req#19 0s
2025/07/26 01:21:00.531 ; Log ; 146 ; 176.123.27.8 ; 9900:guest ; App Comm Login Success, using user=9900:guest @Req#19 0s

2025/07/26 01:22:21.196 ; Trace ; 135 ; 176.123.27.8 ; 9900:guest ; Loading user fav menu:guest @Req#27 0s
2025/07/26 01:22:21.212 ; Log ; 135 ; 176.123.27.8 ; 9900:guest ; App Comm Login Success, using user=9900:guest @Req#27 0s

2025/07/26 01:25:34.125 ; Info ; 132 ; 176.123.27.8 ; 9900:guest ;  ;  ; /app/ ; 176.123.27.8 @Req#29 0s
2025/07/26 01:25:46.362 ; Info ; 150 ; 176.123.27.8 ; 9900:guest ;  ;  ; /app/ ; 176.123.27.8 @Req#31 0s

2025/07/26 01:26:01.220 ; Info ; 132 ; 176.123.27.8 ; 9900:guest ;  ;  ; /app ; 176.123.27.8 @Req#35 0s
2025/07/26 01:26:09.649 ; Info ; 137 ; 176.123.27.8 ; 9900:guest ;  ;  ; /app/ ; 176.123.27.8 @Req#37 0s
2025/07/26 01:26:44.333 ; Trace ; 154 ; 176.123.27.8 ; 9900:admin ; Loading user fav menu:admin @Req#45 0s
2025/07/26 01:26:44.333 ; Log ; 154 ; 176.123.27.8 ; 9900:admin ; Successfull user login:admin @Req#45 0s
2025/07/26 01:26:44.333 ; Trace ; 154 ; 176.123.27.8 ; 9900:admin ; Redirecting user to: /app @Req#45 0s
2025/07/26 01:26:44.380 ; Info ; 143 ; 176.123.27.8 ; 9900:admin ;  ;  ; /app ; 176.123.27.8 @Req#46 0s
2025/07/26 01:26:46.615 ; Info ; 42 ; 176.123.27.8 ; 9900:admin ;  ;  ; /app ; 176.123.27.8 @Req#47 0s

2025/07/26 01:28:07.531 ; Trace ; 151 ; 176.123.27.8 ; 9900:admin ; Session End: User logged out @Req#49 0s
2025/07/26 01:28:07.531 ; Trace ; 151 ; 176.123.27.8 ; 9900:admin ; Removed from cache: sdebeuhbv4cj1gbqzq3q1gv5 @Req#49 0s
2025/07/26 01:28:07.531 ; Trace ; 137 ;  ; 9900: ; Session End: User logged out
2025/07/26 01:28:25.752 ; Trace ; 132 ; 176.123.27.8 ; 9900:admin ; Loading user fav menu:admin @Req#54 0s
2025/07/26 01:28:25.752 ; Log ; 132 ; 176.123.27.8 ; 9900:admin ; Successfull user login:admin @Req#54 0s
2025/07/26 01:28:25.752 ; Trace ; 132 ; 176.123.27.8 ; 9900:admin ; Redirecting user to: /app/ @Req#54 0s
2025/07/26 01:28:25.799 ; Info ; 137 ; 176.123.27.8 ; 9900:admin ;  ;  ; /app/ ; 176.123.27.8 @Req#55 0s
2025/07/26 01:28:25.908 ; Info ; 149 ; 176.123.27.8 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; 176.123.27.8 @Req#56 0s
2025/07/26 01:28:43.125 ; Info ; 130 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; es-sys-stock ; /app/fms/?fm=sys-cfg&id=es-sys-stock ; 176.123.27.8 @Req#57 0s

2025/07/26 01:29:17.983 ; Info ; 155 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; es-sys-stock ; /app/fms/?fm=sys-cfg&id=es-sys-stock ; 176.123.27.8 @Req#58 0s

2025/07/26 01:30:02.093 ; Info ; 149 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; es-sys-sales ; /app/fms/?fm=sys-cfg&id=es-sys-sales ; 176.123.27.8 @Req#60 0s
2025/07/26 01:30:10.851 ; Info ; 146 ; 176.123.27.8 ; 9900:admin ; coding ;  ; /app/fms/?fm=coding&fk0=es-promo&sind=y ; 176.123.27.8 @Req#61 0s
2025/07/26 01:30:18.145 ; Info ; 138 ; 176.123.27.8 ; 9900:admin ; attrs ; es-promo ; /app/fms/?fm=attrs&obj_type=coding&id=es-promo ; 176.123.27.8 @Req#62 0s
2025/07/26 01:30:19.955 ; Info ; 144 ; 176.123.27.8 ; 9900:admin ; attr_def ;  ; /app/fms/?fm=attr_def&fk0=coding_attr&cmd=list&sind=y ; 176.123.27.8 @Req#63 0s

2025/07/26 01:30:49.955 ; Info ; 85 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; es-sys-sales ; /app/fms/?fm=sys-cfg&id=es-sys-sales ; 176.123.27.8 @Req#64 0s
2025/07/26 01:30:52.779 ; Info ; 148 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; es-sys-stock ; /app/fms/?fm=sys-cfg&id=es-sys-stock ; 176.123.27.8 @Req#65 0s
2025/07/26 01:31:36.004 ; Info ; 42 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; es-sys-stock ; /app/fms/?fm=sys-cfg&id=es-sys-stock ; 176.123.27.8 @Req#68 0s

2025/07/26 01:32:16.978 ; Info ; 152 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; es-sys-stock ; /app/fms/?fm=sys-cfg&id=es-sys-stock ; 176.123.27.8 @Req#70 0s
2025/07/26 01:32:39.032 ; Info ; 151 ; 176.123.27.8 ; 9900:guest ;  ;  ; /app ; 176.123.27.8 @Req#74 0s
2025/07/26 01:32:46.064 ; Info ; 149 ; 176.123.27.8 ; 9900:guest ;  ;  ; /app ; 176.123.27.8 @Req#79 0s

2025/07/26 01:32:54.020 ; Info ; 130 ; 176.123.27.8 ; 9900:guest ;  ;  ; /app ; 176.123.27.8 @Req#84 0s
2025/07/26 01:33:32.157 ; Info ; 130 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; es-sys-stock ; /app/fms/?fm=sys-cfg&id=es-sys-stock ; 176.123.27.8 @Req#87 0s

2025/07/26 01:34:08.419 ; Info ; 147 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; es-sys-stock ; /app/fms/?fm=sys-cfg&id=es-sys-stock ; 176.123.27.8 @Req#90 0s
2025/07/26 01:34:37.156 ; Info ; 130 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; es-sys-stock ; /app/fms/?fm=sys-cfg&id=es-sys-stock ; 176.123.27.8 @Req#92 0s

2025/07/26 01:35:47.209 ; Info ; 163 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; es-sys-stock ; /app/fms/?fm=sys-cfg&id=es-sys-stock ; 176.123.27.8 @Req#94 0s

2025/07/26 01:38:21.405 ; Info ; 163 ; 176.123.27.8 ; 9900:admin ; sys-cfg ; es-sys-stock ; /app/fms/?fm=sys-cfg&id=es-sys-stock ; 176.123.27.8 @Req#96 0s

2025/07/26 01:38:51.100 ; Trace ; 169 ; 176.123.27.8 ; 9900:guest ; AuthCheck Failed: biz(14,) @Req#99 0s
2025/07/26 01:38:51.100 ; Trace ; 169 ; 176.123.27.8 ; 9900:guest ; AuthCheck Failed: fm(vucost,es-item) @Req#99 0s
2025/07/26 01:38:51.100 ; Trace ; 169 ; 176.123.27.8 ; 9900:guest ; AuthCheck Failed: fm(vuamnts,es-so-cust) @Req#99 0s
2025/07/26 01:38:51.100 ; Trace ; 169 ; 176.123.27.8 ; 9900:guest ; AuthCheck Failed: fm(add,es-so-cust) @Req#99 0s
2025/07/26 01:38:55.682 ; Info ; 168 ; 176.123.27.8 ; 9900:guest ;  ;  ; /app ; 176.123.27.8 @Req#102 0s
2025/07/26 01:39:42.422 ; Trace ; 138 ; 176.123.24.183 ; 9900:guest ; Loading user fav menu:guest @Req#104 0s
2025/07/26 01:39:42.422 ; Log ; 138 ; 176.123.24.183 ; 9900:guest ; App Comm Login Success, using user=9900:guest @Req#104 0s

2025/07/26 01:40:04.707 ; Info ; 164 ; 176.123.24.183 ; 9900:guest ;  ;  ; /app ; 176.123.24.183 @Req#109 0s
2025/07/26 01:40:10.046 ; Info ; 132 ; 176.123.24.183 ; 9900:guest ;  ;  ; /app ; 176.123.24.183 @Req#113 0s
2025/07/26 01:40:26.743 ; Info ; 138 ; 176.123.24.183 ; 9900:guest ;  ;  ; /app ; 176.123.24.183 @Req#118 0s
2025/07/26 01:40:33.722 ; Trace ; 144 ; 176.123.24.183 ; 9900:guest ; AuthCheck Failed: biz(14,) @Req#121 0s
2025/07/26 01:40:33.722 ; Trace ; 144 ; 176.123.24.183 ; 9900:guest ; AuthCheck Failed: fm(vucost,es-item) @Req#121 0s
2025/07/26 01:40:33.722 ; Trace ; 144 ; 176.123.24.183 ; 9900:guest ; AuthCheck Failed: fm(vuamnts,es-so-cust) @Req#121 0s
2025/07/26 01:40:33.722 ; Trace ; 144 ; 176.123.24.183 ; 9900:guest ; AuthCheck Failed: fm(add,es-so-cust) @Req#121 0s

2025/07/26 01:41:44.922 ; Trace ; 170 ; 176.123.27.8 ; 9900:admin ; Session End: User logged out @Req#126 0s
2025/07/26 01:41:44.922 ; Trace ; 170 ; 176.123.27.8 ; 9900:admin ; Removed from cache: sdebeuhbv4cj1gbqzq3q1gv5 @Req#126 0s
2025/07/26 01:41:44.937 ; Trace ; 138 ;  ; 9900: ; Session End: User logged out

2025/07/26 02:01:50.750 ; Trace ; 172 ;  ; 9900: ; Session End: User logged out
2025/07/26 02:01:50.750 ; Trace ; 172 ;  ; 9900: ; Session End: User logged out
2025/07/26 02:01:50.750 ; Trace ; 172 ;  ; 9900: ; Session End: User logged out
2025/07/26 02:01:50.750 ; Log ; 172 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads...
2025/07/26 02:01:50.750 ; Log ; 140 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue
2025/07/26 02:01:50.750 ; Log ; 139 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue
2025/07/26 02:01:50.750 ; Log ; 172 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ********

2025/07/26 03:12:02.442 ; Info ; 1 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/07/26 03:12:03.489 ; Trace ; 1 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/07/26 03:12:03.489 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/07/26 03:12:03.504 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/07/26 03:12:03.504 ; Log ; 1 ;  ; 0000: ; 26/7/2025 03:12:03 @Http Req#0
2025/07/26 03:12:03.504 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/07/26 03:12:03.504 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 673  @Http Req#0
2025/07/26 03:12:03.504 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/07/26 03:12:03.582 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/07/26 03:12:03.582 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/07/26 03:12:03.802 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/07/26 03:12:03.802 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/07/26 03:12:03.927 ; Info ; 1 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/07/26 03:12:04.107 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/07/26 03:12:04.211 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/07/26 03:12:04.523 ; Trace ; 1 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/07/26 03:12:04.883 ; Trace ; 1 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/07/26 03:12:04.883 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/07/26 03:12:04.883 ; Trace ; 1 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/07/26 03:12:04.930 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/07/26 03:12:05.101 ; Info ; 1 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/07/26 03:12:05.336 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/07/26 03:12:05.351 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/07/26 03:12:05.523 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/07/26 03:12:05.523 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/07/26 03:12:05.539 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/07/26 03:12:05.539 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/07/26 03:12:05.633 ; Trace ; 1 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/07/26 03:12:05.648 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/07/26 03:12:05.648 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/07/26 03:12:05.648 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/07/26 03:12:05.648 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/07/26 03:12:05.648 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/07/26 03:12:05.726 ; Trace ; 1 ;  ; 0000: ; HsStoredObject cached: 0 @Http Req#0
2025/07/26 03:12:06.084 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/07/26 03:12:06.084 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/07/26 03:12:06.084 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/07/26 03:12:06.084 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/07/26 03:12:06.084 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/07/26 03:12:06.084 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/07/26 03:12:06.084 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/07/26 03:12:06.131 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Building forms auth roles @Http Req#1
2025/07/26 03:12:06.131 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Failed to load form:login @Http Req#1
2025/07/26 03:12:06.131 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Failed to load form:logout @Http Req#1
2025/07/26 03:12:06.225 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/07/26 03:12:06.240 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/07/26 03:12:06.240 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Failed to load form:relation @Http Req#1
2025/07/26 03:12:06.256 ; Log ; 9 ; 176.123.27.8 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/07/26 03:12:06.256 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/07/26 03:12:11.101 ; Log ; 7 ;  ; 0000: ; Started threaded queue processor: DelayedSqlQueue @Http Req#1
2025/07/26 03:12:11.101 ; Log ; 8 ;  ; 0000: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#1
2025/07/26 03:12:11.101 ; Log ; 8 ;  ; 0000: ; Loading scheduled tasks.. @Http Req#1
2025/07/26 03:12:14.728 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/07/26 03:12:14.946 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/07/26 03:12:15.618 ; Trace ; 9 ; 176.123.27.8 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/07/26 03:12:15.634 ; Info ; 9 ; 176.123.27.8 ; 9900: ; Init Client: 9900 @Http Req#1
2025/07/26 03:12:15.649 ; Log ; 9 ; 176.123.27.8 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#1
2025/07/26 03:12:15.649 ; Log ; 9 ; 176.123.27.8 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#1
2025/07/26 03:12:15.665 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#1
2025/07/26 03:12:15.665 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; COA.RebuildAccountsTree @Http Req#1
2025/07/26 03:12:15.665 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; Accounts loaded:65 @Http Req#1
2025/07/26 03:12:15.665 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#1
2025/07/26 03:12:15.665 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; Accounts loaded:0 @Http Req#1
2025/07/26 03:12:15.681 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; Caching Sales Items:9900 @Http Req#1
2025/07/26 03:12:15.681 ; Log ; 9 ; 176.123.27.8 ; 9900: ; Caching SalesItem.. @Http Req#1
2025/07/26 03:12:15.696 ; Log ; 9 ; 176.123.27.8 ; 9900: ; Sales Items Cached: 0 @Http Req#1
2025/07/26 03:12:15.696 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; Caching all items stock data.. @Http Req#1
2025/07/26 03:12:15.696 ; Trace ; 9 ; 176.123.27.8 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#1
2025/07/26 03:12:15.712 ; Log ; 9 ; 176.123.27.8 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#1
2025/07/26 03:12:15.712 ; Log ; 9 ; 176.123.27.8 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#1
2025/07/26 03:12:15.712 ; Log ; 9 ; 176.123.27.8 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#1
2025/07/26 03:12:15.712 ; Info ; 9 ; 176.123.27.8 ; 9900: ; Init Client Completed: 9900 @Http Req#1
2025/07/26 03:12:15.728 ; Trace ; 9 ; 176.123.27.8 ; 9900:guest ; Loading user fav menu:guest @Http Req#1
2025/07/26 03:12:15.743 ; Log ; 9 ; 176.123.27.8 ; 9900:guest ; App Comm Login Success, using user=9900:guest @Http Req#1

2025/07/26 03:34:03.315 ; Trace ; 146 ;  ; 9900: ; Session End: User logged out @Http Req#6
2025/07/26 03:34:03.315 ; Log ; 146 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads... @Http Req#6
2025/07/26 03:34:03.315 ; Log ; 8 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue @Http Req#6
2025/07/26 03:34:03.315 ; Log ; 7 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue @Http Req#6
2025/07/26 03:34:03.518 ; Log ; 146 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ******** @Http Req#6

2025/07/26 16:57:30.003 ; Info ; 1 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/07/26 16:57:30.128 ; Trace ; 1 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/07/26 16:57:30.128 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/07/26 16:57:30.128 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/07/26 16:57:30.128 ; Log ; 1 ;  ; 0000: ; 26/7/2025 16:57:30 @Http Req#0
2025/07/26 16:57:30.128 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/07/26 16:57:30.128 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 673  @Http Req#0
2025/07/26 16:57:30.128 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/07/26 16:57:30.128 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/07/26 16:57:30.128 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/07/26 16:57:30.183 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/07/26 16:57:30.183 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/07/26 16:57:30.245 ; Info ; 1 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/07/26 16:57:30.261 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/07/26 16:57:30.261 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/07/26 16:57:30.276 ; Trace ; 1 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/07/26 16:57:30.292 ; Trace ; 1 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/07/26 16:57:30.292 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/07/26 16:57:30.292 ; Trace ; 1 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/07/26 16:57:30.292 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/07/26 16:57:30.308 ; Info ; 1 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/07/26 16:57:30.354 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/07/26 16:57:30.354 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/07/26 16:57:30.386 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/07/26 16:57:30.386 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/07/26 16:57:30.386 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/07/26 16:57:30.386 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/07/26 16:57:30.417 ; Trace ; 1 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/07/26 16:57:30.417 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/07/26 16:57:30.417 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/07/26 16:57:30.417 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/07/26 16:57:30.417 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/07/26 16:57:30.417 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/07/26 16:57:30.417 ; Trace ; 1 ;  ; 0000: ; HsStoredObject cached: 0 @Http Req#0
2025/07/26 16:57:30.526 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/07/26 16:57:30.526 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/07/26 16:57:30.526 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/07/26 16:57:30.526 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/07/26 16:57:30.526 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/07/26 16:57:30.526 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/07/26 16:57:30.526 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/07/26 16:57:35.543 ; Log ; 7 ;  ; 0000: ; Started threaded queue processor: DelayedSqlQueue @Http Req#0
2025/07/26 16:57:35.543 ; Log ; 8 ;  ; 0000: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#0
2025/07/26 16:57:35.543 ; Log ; 8 ;  ; 0000: ; Loading scheduled tasks.. @Http Req#0

2025/07/26 17:18:30.878 ; Trace ; 183 ;  ; 0000: ; Session End: User logged out @Http Req#0
2025/07/26 17:18:30.878 ; Log ; 183 ;  ; 0000: ; Initiating app shutdown, waiting for worker threads... @Http Req#0
2025/07/26 17:18:30.878 ; Log ; 8 ;  ; 0000: ; Ending threaded queue: ScheduledTasksQueue @Http Req#0
2025/07/26 17:18:30.878 ; Log ; 7 ;  ; 0000: ; Ending threaded queue: DelayedSqlQueue @Http Req#0
2025/07/26 17:18:30.894 ; Log ; 183 ;  ; 0000: ; ***** App shutdown [HsApp] completed. Bye ******** @Http Req#0

