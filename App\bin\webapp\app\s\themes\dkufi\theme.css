﻿

/*Themes: <PERSON><PERSON>*/


@media screen {

    * {

        font-family: droid_<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
        color: #357;
    }

    input, select, textarea {
        color: #246;
        
    }



    .page_header, .page_footer {
        letter-spacing: normal;
    }



    .tree a.folder, .title, .head, b, .ui-bold, span.ui-hl, .ui-hl, .ui-cont h2, i.req, .col-total, .fm .head, .fm TD.title, .group-by-box,
    .rep_tab .title,
    .rep_tab .head, .rep_tab .total, .rep_tab tr.total td, .rep_tab .foot, .rep_tab tr.foot td, .rep_tab .seq, .rep_tab thead, .rep_tab tfoot, tr.h0,
    tr.h2 td,
    .rep_tab tr.hilight, .rep_tab td.hilight, div.hilight,
    .ui-tit,
    th {
       
    }


 
     div.tree a:not(.folder), a.tree-l {
       font-weight: normal;
       color: #235;
    }


    .tit {
        font-weight: bold;
    }



} /*Media screen*/