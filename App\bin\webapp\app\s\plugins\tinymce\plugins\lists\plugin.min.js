tinymce.PluginManager.add("lists",function(a){function b(b){return a.$.contains(a.getBody(),b)}function c(a){return a&&"BR"==a.nodeName}function d(a){return a&&/^(OL|UL|DL)$/.test(a.nodeName)&&b(a)}function e(a){return a.parentNode.firstChild==a}function f(a){return a.parentNode.lastChild==a}function g(b){return b&&!!a.schema.getTextBlockElements()[b.nodeName]}function h(b){return b===a.getBody()}var i=this;a.on("init",function(){function j(a,b){var c=A.isEmpty(a);return b&&A.select("span[data-mce-type=bookmark]").length>0?!1:c}function k(a){function b(b){var d,e,f;e=a[b?"startContainer":"endContainer"],f=a[b?"startOffset":"endOffset"],1==e.nodeType&&(d=A.create("span",{"data-mce-type":"bookmark"}),e.hasChildNodes()?(f=Math.min(f,e.childNodes.length-1),b?e.insertBefore(d,e.childNodes[f]):A.insertAfter(d,e.childNodes[f])):e.appendChild(d),e=d,f=0),c[b?"startContainer":"endContainer"]=e,c[b?"startOffset":"endOffset"]=f}var c={};return b(!0),a.collapsed||b(),c}function l(a){function b(b){function c(a){for(var b=a.parentNode.firstChild,c=0;b;){if(b==a)return c;1==b.nodeType&&"bookmark"==b.getAttribute("data-mce-type")||c++,b=b.nextSibling}return-1}var d,e,f;d=f=a[b?"startContainer":"endContainer"],e=a[b?"startOffset":"endOffset"],d&&(1==d.nodeType&&(e=c(d),d=d.parentNode,A.remove(f)),a[b?"startContainer":"endContainer"]=d,a[b?"startOffset":"endOffset"]=e)}b(!0),b();var c=A.createRng();c.setStart(a.startContainer,a.startOffset),a.endContainer&&c.setEnd(a.endContainer,a.endOffset),B.setRng(c)}function m(b,c){var d,e,f,g=A.createFragment(),h=a.schema.getBlockElements();if(a.settings.forced_root_block&&(c=c||a.settings.forced_root_block),c&&(e=A.create(c),e.tagName===a.settings.forced_root_block&&A.setAttribs(e,a.settings.forced_root_block_attrs),g.appendChild(e)),b)for(;d=b.firstChild;){var i=d.nodeName;f||"SPAN"==i&&"bookmark"==d.getAttribute("data-mce-type")||(f=!0),h[i]?(g.appendChild(d),e=null):c?(e||(e=A.create(c),g.appendChild(e)),e.appendChild(d)):g.appendChild(d)}return a.settings.forced_root_block?f||tinymce.Env.ie&&!(tinymce.Env.ie>10)||e.appendChild(A.create("br",{"data-mce-bogus":"1"})):g.appendChild(A.create("br")),g}function n(){return tinymce.grep(B.getSelectedBlocks(),function(a){return/^(LI|DT|DD)$/.test(a.nodeName)})}function o(a,b,c){function d(a){tinymce.each(g,function(c){a.parentNode.insertBefore(c,b.parentNode)}),A.remove(a)}var e,f,g,h;for(g=A.select('span[data-mce-type="bookmark"]',a),c=c||m(b),e=A.createRng(),e.setStartAfter(b),e.setEndAfter(a),f=e.extractContents(),h=f.firstChild;h;h=h.firstChild)if("LI"==h.nodeName&&A.isEmpty(h)){A.remove(h);break}A.isEmpty(f)||A.insertAfter(f,a),A.insertAfter(c,a),j(b.parentNode)&&d(b.parentNode),A.remove(b),j(a)&&A.remove(a)}function p(a){var b,c;if(b=a.nextSibling,b&&d(b)&&b.nodeName==a.nodeName){for(;c=b.firstChild;)a.appendChild(c);A.remove(b)}if(b=a.previousSibling,b&&d(b)&&b.nodeName==a.nodeName){for(;c=b.firstChild;)a.insertBefore(c,a.firstChild);A.remove(b)}}function q(a){tinymce.each(tinymce.grep(A.select("ol,ul",a)),function(a){var b,c=a.parentNode;"LI"==c.nodeName&&c.firstChild==a&&(b=c.previousSibling,b&&"LI"==b.nodeName&&(b.appendChild(a),j(c)&&A.remove(c))),d(c)&&(b=c.previousSibling,b&&"LI"==b.nodeName&&b.appendChild(a))})}function r(a){function b(a){j(a)&&A.remove(a)}var c,g=a.parentNode,i=g.parentNode;return h(g)?!0:"DD"==a.nodeName?(A.rename(a,"DT"),!0):e(a)&&f(a)?("LI"==i.nodeName?(A.insertAfter(a,i),b(i),A.remove(g)):d(i)?A.remove(g,!0):(i.insertBefore(m(a),g),A.remove(g)),!0):e(a)?("LI"==i.nodeName?(A.insertAfter(a,i),a.appendChild(g),b(i)):d(i)?i.insertBefore(a,g):(i.insertBefore(m(a),g),A.remove(a)),!0):f(a)?("LI"==i.nodeName?A.insertAfter(a,i):d(i)?A.insertAfter(a,g):(A.insertAfter(m(a),g),A.remove(a)),!0):("LI"==i.nodeName?(g=i,c=m(a,"LI")):c=d(i)?m(a,"LI"):m(a),o(g,a,c),q(g.parentNode),!0)}function s(a){function b(b,c){var e;if(d(b)){for(;e=a.lastChild.firstChild;)c.appendChild(e);A.remove(b)}}var c,e;return"DT"==a.nodeName?(A.rename(a,"DD"),!0):(c=a.previousSibling,c&&d(c)?(c.appendChild(a),!0):c&&"LI"==c.nodeName&&d(c.lastChild)?(c.lastChild.appendChild(a),b(a.lastChild,c.lastChild),!0):(c=a.nextSibling,c&&d(c)?(c.insertBefore(a,c.firstChild),!0):(c=a.previousSibling,c&&"LI"==c.nodeName?(e=A.create(a.parentNode.nodeName),c.appendChild(e),e.appendChild(a),b(a.lastChild,e),!0):!1)))}function t(){var b=n();if(b.length){for(var c=k(B.getRng(!0)),d=0;d<b.length&&(s(b[d])||0!==d);d++);return l(c),a.nodeChanged(),!0}}function u(){var b=n();if(b.length){var c,d,e=k(B.getRng(!0)),f=a.getBody();for(c=b.length;c--;)for(var g=b[c].parentNode;g&&g!=f;){for(d=b.length;d--;)if(b[d]===g){b.splice(c,1);break}g=g.parentNode}for(c=0;c<b.length&&(r(b[c])||0!==c);c++);return l(e),a.nodeChanged(),!0}}function v(b){function e(){function b(a){var b,c;for(b=h[a?"startContainer":"endContainer"],c=h[a?"startOffset":"endOffset"],1==b.nodeType&&(b=b.childNodes[Math.min(c,b.childNodes.length-1)]||b);b.parentNode!=f;){if(g(b))return b;if(/^(TD|TH)$/.test(b.parentNode.nodeName))return b;b=b.parentNode}return b}for(var d,e=[],f=a.getBody(),i=b(!0),j=b(),k=[],l=i;l&&(k.push(l),l!=j);l=l.nextSibling);return tinymce.each(k,function(a){if(g(a))return e.push(a),void(d=null);if(A.isBlock(a)||c(a))return c(a)&&A.remove(a),void(d=null);var b=a.nextSibling;return tinymce.dom.BookmarkManager.isBookmarkNode(a)&&(g(b)||!b&&a.parentNode==f)?void(d=null):(d||(d=A.create("p"),a.parentNode.insertBefore(d,a),e.push(d)),void d.appendChild(a))}),e}var f,h=B.getRng(!0),i="LI";"false"!==A.getContentEditable(B.getNode())&&(b=b.toUpperCase(),"DL"==b&&(i="DT"),f=k(h),tinymce.each(e(),function(a){var c,e;e=a.previousSibling,e&&d(e)&&e.nodeName==b?(c=e,a=A.rename(a,i),e.appendChild(a)):(c=A.create(b),a.parentNode.insertBefore(c,a),c.appendChild(a),a=A.rename(a,i)),p(c)}),l(f))}function w(){var b=k(B.getRng(!0)),c=a.getBody();tinymce.each(n(),function(a){var b,e;if(!h(a.parentNode)){if(j(a))return void r(a);for(b=a;b&&b!=c;b=b.parentNode)d(b)&&(e=b);o(e,a)}}),l(b)}function x(a){var b=A.getParent(B.getStart(),"OL,UL,DL");if(!h(b))if(b)if(b.nodeName==a)w(a);else{var c=k(B.getRng(!0));p(A.rename(b,a)),l(c)}else v(a)}function y(b){return function(){var c=A.getParent(a.selection.getStart(),"UL,OL,DL");return c&&c.nodeName==b}}function z(a){return c(a)?!(!A.isBlock(a.nextSibling)||c(a.previousSibling)):!1}var A=a.dom,B=a.selection;i.backspaceDelete=function(e){function f(b,c){var d,e,f=b.startContainer,g=b.startOffset;if(3==f.nodeType&&(c?g<f.data.length:g>0))return f;for(d=a.schema.getNonEmptyElements(),1==f.nodeType&&(f=tinymce.dom.RangeUtils.getNode(f,g)),e=new tinymce.dom.TreeWalker(f,a.getBody()),c&&z(f)&&e.next();f=e[c?"next":"prev2"]();){if("LI"==f.nodeName&&!f.hasChildNodes())return f;if(d[f.nodeName])return f;if(3==f.nodeType&&f.data.length>0)return f}}function g(a,e){var f,g,i=a.parentNode;if(b(a)&&b(e)){if(d(e.lastChild)&&(g=e.lastChild),i==e.lastChild&&c(i.previousSibling)&&A.remove(i.previousSibling),f=e.lastChild,f&&c(f)&&a.hasChildNodes()&&A.remove(f),j(e,!0)&&A.$(e).empty(),!j(a,!0))for(;f=a.firstChild;)e.appendChild(f);g&&e.appendChild(g),A.remove(a),j(i)&&!h(i)&&A.remove(i)}}if(B.isCollapsed()){var i,m,n,o=A.getParent(B.getStart(),"LI");if(o){if(i=o.parentNode,h(i)&&A.isEmpty(i))return!0;if(m=B.getRng(!0),n=A.getParent(f(m,e),"LI"),n&&n!=o){var p=k(m);return e?g(n,o):g(o,n),l(p),!0}if(!n&&!e&&w(i.nodeName))return!0}}},a.on("BeforeExecCommand",function(b){var c,d=b.command.toLowerCase();return"indent"==d?t()&&(c=!0):"outdent"==d&&u()&&(c=!0),c?(a.fire("ExecCommand",{command:b.command}),b.preventDefault(),!0):void 0}),a.addCommand("InsertUnorderedList",function(){x("UL")}),a.addCommand("InsertOrderedList",function(){x("OL")}),a.addCommand("InsertDefinitionList",function(){x("DL")}),a.addQueryStateHandler("InsertUnorderedList",y("UL")),a.addQueryStateHandler("InsertOrderedList",y("OL")),a.addQueryStateHandler("InsertDefinitionList",y("DL")),a.on("keydown",function(b){9!=b.keyCode||tinymce.util.VK.metaKeyPressed(b)||a.dom.getParent(a.selection.getStart(),"LI,DT,DD")&&(b.preventDefault(),b.shiftKey?u():t())})}),a.addButton("indent",{icon:"indent",title:"Increase indent",cmd:"Indent",onPostRender:function(){var b=this;a.on("nodechange",function(){for(var c=a.selection.getSelectedBlocks(),d=!1,f=0,g=c.length;!d&&g>f;f++){var h=c[f].nodeName;d="LI"==h&&e(c[f])||"UL"==h||"OL"==h||"DD"==h}b.disabled(d)})}}),a.on("keydown",function(a){a.keyCode==tinymce.util.VK.BACKSPACE?i.backspaceDelete()&&a.preventDefault():a.keyCode==tinymce.util.VK.DELETE&&i.backspaceDelete(!0)&&a.preventDefault()})});