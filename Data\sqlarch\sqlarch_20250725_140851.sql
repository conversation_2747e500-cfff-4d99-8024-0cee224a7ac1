0001 ********140851 0001 => UPDATE hs_config SET cfg_value='N' WHERE cfg_key='app-last-shutdown-clean'
0002 ********140851 0000 => ALTER TABLE hs_ext_notifs ADD crtd_by varchar(16) DEFAULT NULL
0003 ********140851 0000 => UPDATE hs_config SET cfg_value='1' WHERE cfg_key='HSC_patch_level'
0004 ********140851 0001 => INSERT INTO hs_config (cfg_value,cfg_key) VALUES ('1','HSC_patch_level')
0005 ********140851 0000 => ALTER TABLE hs_ext_notifs ADD ref_notif_id varchar(16) DEFAULT NULL
0006 ********140851 0001 => UPDATE hs_config SET cfg_value='2' WHERE cfg_key='HSC_patch_level'
0007 ********140851 0001 => UPDATE hs_config SET cfg_value='la+sqJa4tbnkmZ+iqqY=' WHERE cfg_key='_$sys_fid_'
0008 ********140851 0001 => UPDATE hs_config SET cfg_value='kb+hv6OUv7Oz4p+Zt6Q=' WHERE cfg_key='_$sys_lsdt_'
0009 ********140851 0001 => UPDATE hs_config SET cfg_value='maE=' WHERE cfg_key='_$sys_stcn_'
0010 ********140851 0000 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_theme'
0011 ********140851 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_theme',NULL,'C','User Theme',NULL,0,32,NULL,'user-themes',*********,'6')
0012 ********140851 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_menus'
0013 ********140851 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_menus','app-menus-base','C','قائمة المستخدم',NULL,0,65536,NULL,NULL,*********,'4')
0014 ********140851 0000 => UPDATE hs_config SET cfg_value='Y' WHERE cfg_key='0000:setup'
0015 ********140851 0001 => INSERT INTO hs_config (cfg_value,cfg_key) VALUES ('Y','0000:setup')
0016 ********140851 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_cash_id'
0017 ********140851 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_cash_id','cash_id','F','الصندوق الإفتراضي',NULL,0,2147483647,'0','fi-cl-cash-c',*********,'0')
0018 ********140851 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
0019 ********140851 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,2147483647,'0','fi-brnch',*********,'0')
0020 ********140851 0000 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_rep'
0021 ********140851 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_rep','sales_rep','F','المندوب الإفتراضي',NULL,0,2147483647,'0','fi-cl-reps',*********,'0')
0022 ********140851 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_branch'
0023 ********140851 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_branch','auth_usr_branch','C','الفروع',NULL,0,65536,NULL,'fi-brnch',*********,'4')
0024 ********140851 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_teller'
0025 ********140851 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_teller','auth_usr_teller','C','الصناديق',NULL,0,65536,NULL,'fi-cl-cash-c',*********,'4')
0026 ********140851 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_banks'
0027 ********140851 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_banks','auth_usr_banks','C','البنوك',NULL,0,65536,NULL,'fi-cl-banks',*********,'4')
0028 ********140851 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_cc'
0029 ********140851 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_cc','auth_usr_cc','C','المراكز',NULL,0,65536,NULL,'fi-cl-cc',*********,'4')
0030 ********140851 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_proj'
0031 ********140851 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_proj','auth_usr_proj','C','المشاريع',NULL,0,65536,NULL,'fi-proj',*********,'4')
0032 ********140851 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_actv'
0033 ********140851 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_actv','auth_usr_actv','C','النشاط',NULL,0,65536,NULL,'fi-actv',*********,'4')
0034 ********140851 0000 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_accs'
0035 ********140851 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_accs','auth_usr_accs','C','المجموعات المحاسبية',NULL,0,65536,NULL,'fi-accgr',*********,'4')
0036 ********140851 0000 => ALTER TABLE es_sales_docs ALTER COLUMN cov_name nvarchar(80) NULL
0037 ********140851 0001 => UPDATE hs_config SET cfg_value='42' WHERE cfg_key='ES_patch_level'
0038 ********140851 0000 => ALTER TABLE es_fin_docs ALTER COLUMN cov_name nvarchar(80) NULL
0039 ********140851 0001 => UPDATE hs_config SET cfg_value='43' WHERE cfg_key='ES_patch_level'
0040 ********140851 0000 => ALTER TABLE fi_accounts ALTER COLUMN acc_name nvarchar(64) NULL
0041 ********140851 0001 => UPDATE hs_config SET cfg_value='44' WHERE cfg_key='ES_patch_level'
0042 ********140851 0000 => ALTER TABLE es_sales_docs ADD ex_data nvarchar(1000) NULL
0043 ********140851 0001 => UPDATE hs_config SET cfg_value='45' WHERE cfg_key='ES_patch_level'
0044 ********140851 0000 => ALTER TABLE es_sales_docs ADD doc_charges MONEY NULL
0045 ********140851 0001 => UPDATE hs_config SET cfg_value='46' WHERE cfg_key='ES_patch_level'
0046 ********140851 0000 => ALTER TABLE es_sales_docs ADD due_date varchar(8) NULL
0047 ********140851 0001 => UPDATE hs_config SET cfg_value='47' WHERE cfg_key='ES_patch_level'
0048 ********140851 0000 => ALTER TABLE es_cust ADD is_cav CHAR(1) NULL
0049 ********140851 0001 => UPDATE hs_config SET cfg_value='48' WHERE cfg_key='ES_patch_level'
0050 ********140851 0000 => ALTER TABLE es_vendors ADD is_cav CHAR(1) NULL
0051 ********140851 0001 => UPDATE hs_config SET cfg_value='49' WHERE cfg_key='ES_patch_level'
0052 ********140851 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='max_discount_pct'
0053 ********140851 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','max_discount_pct',NULL,'F','نسبة التخفيض المسموحة للمستخدم %',NULL,0,100,'0',NULL,*********,'0')
0054 ********140851 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_store_id'
0055 ********140851 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_store_id','store_id','F','المخزن الإفتراضي',NULL,0,2147483647,'0','es-cl-stores',*********,'0')
0056 ********140851 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
0057 ********140851 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,2147483647,'0','fi-brnch',*********,'0')
0058 ********140851 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_sales'
0059 ********140851 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_sales','auth_usr_sales','C','صلاحيات إضافية',NULL,0,65536,NULL,'es-sales-auth',*********,'4')
0060 ********140851 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_store'
0061 ********140851 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_store','auth_usr_store','C','المخازن',NULL,0,65536,NULL,'es-cl-stores',*********,'4')
0062 ********140905 0001 => INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('0001','24BNSK1PS1E1',NULL,'SYS','admin','Successfull login','********','140905','W',0,0,NULL,'sys',NULL)
0063 ********140905 0001 => INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.91.0 ( 23/11/2020 )  Standardhttp IP=::1:50058 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','********140905','0001')
0064 ********140905 0001 => UPDATE hs_logindata SET last_activity_dt='********140905' WHERE sys_client_id='0001' AND user_id='admin'
0065 ********140950 0001 => INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('0001','24BNSRFTCGW',NULL,'SYS','admin','Successfull login','********','140950','W',0,0,NULL,'sys',NULL)
0066 ********140950 0001 => INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.91.0 ( 23/11/2020 )  Standardhttp IP=127.0.0.1:50101 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0','********140950','0001')
0067 ********140950 0001 => UPDATE hs_logindata SET last_activity_dt='********140950' WHERE sys_client_id='0001' AND user_id='admin'
0068 ********140956 0001 => INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('sys',NULL,'except','alert','2 times @Sys client init: FI-GL Module - 0000: Invalid object name ''hs_branches''.','********140956','1089')
0069 ********140956 0001 => INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('sys',NULL,'except','alert','2 times @LoadCodeList_UseSql:SELECT fi_acc_no, acc_name AS acc_title FROM fi_accounts WHERE sys_client_id=''0000'' AND (acc_hier_cat=''2'' AND linked_acc_no IS NULL) AND (ma_acc_no=fi_acc_no) ORDER BY acc_root, acc_parent, fi_acc_no: Invalid column name ''ma_','********140956','1089')
0070 ********140956 0001 => INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('sys',NULL,'except','alert',' @LoadCodeList_UseSql:SELECT group_id,group_name FROM es_stock_groups WHERE sys_client_id=''0000'': Invalid object name ''es_stock_groups''.','********140956','1089')
0071 ************** 0001 => INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('0001','24BNTF87XBZ',NULL,'SYS','admin','Successfull login','********','141213','W',0,0,NULL,'sys',NULL)
0072 ************** 0001 => INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.91.0 ( 23/11/2020 )  Standardhttp IP=::1:50213 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','**************','0001')
0073 ************** 0001 => UPDATE hs_logindata SET last_activity_dt='**************' WHERE sys_client_id='0001' AND user_id='admin'
0074 ********141256 0001 => INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('0001','24BNTM8GXVC',NULL,'SYS','admin','Successfull login','********','141256','W',0,0,NULL,'sys',NULL)
0075 ********141256 0001 => INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.91.0 ( 23/11/2020 )  Standardhttp IP=::1:50251 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','********141256','0001')
0076 ********141256 0001 => UPDATE hs_logindata SET last_activity_dt='********141256' WHERE sys_client_id='0001' AND user_id='admin'
0077 ********141542 0001 => INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('0001','24BNUDRQX1J',NULL,'SYS','admin','Successfull login','********','141542','W',0,0,NULL,'sys',NULL)
0078 ********141542 0001 => INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.91.0 ( 23/11/2020 )  Standardhttp IP=127.0.0.1:50280 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0','********141542','0001')
0079 ********141542 0001 => UPDATE hs_logindata SET last_activity_dt='********141542' WHERE sys_client_id='0001' AND user_id='admin'
0080 ********141929 0000 => ALTER TABLE hs_logindata ADD user_flags int
0081 ********141929 0000 => ALTER TABLE hs_logindata ADD last_pwd_chg varchar(10)
0082 ********141929 0000 => CREATE TABLE hs_client_ddic (parent_id varchar(16),fld_name varchar(16),fld_base varchar(16),fld_type nvarchar(16),fld_title nvarchar(64),fld_desc nvarchar(255),fld_min int,fld_max int,fld_def_val nvarchar(32),fld_code_list varchar(16),fld_ctrl_type nvarchar(16),fld_flags bigint,sys_client_id varchar(4)  , CONSTRAINT [PK_hs_client_ddic] PRIMARY KEY CLUSTERED ([sys_client_id] ASC, [fld_name] ASC))
0083 ********141937 0000 => ALTER TABLE hs_sys_clients ADD client_type CHAR(1)
0084 ********141937 0000 => ALTER TABLE hs_sys_clients ADD read_only_access_from varchar(10)
0085 ********141937 0000 => ALTER TABLE hs_sys_clients ADD suspend_access_from varchar(10)
0086 ********141937 0000 => ALTER TABLE hs_sys_clients ADD options bigint
0087 ********141937 0000 => ALTER TABLE hs_sys_clients ADD sms_units_bal money
0088 ********141937 0000 => ALTER TABLE hs_sys_clients ADD short_name nvarchar(16)
0089 ********141937 0000 => ALTER TABLE hs_sys_clients ADD msg_text nvarchar(4000)
0090 ********141937 0000 => ALTER TABLE hs_sys_clients ADD msg_start varchar(10)
0091 ********141937 0000 => ALTER TABLE hs_sys_clients ADD msg_expiry varchar(10)
0092 ********141938 0000 => ALTER TABLE hs_cms_images ADD sys_client_id varchar(4)
0093 ********141938 0000 => CREATE TABLE hs_branches (br_id varchar(3),br_name nvarchar(200),br_status CHAR(1),address nvarchar(250),phone varchar(16),fax varchar(16),mobile varchar(16),email varchar(64),website nvarchar(200),logo varchar(8),use_org_print CHAR(1),print_hdr_beg nvarchar(250),print_hdr_mid nvarchar(250),print_hdr_end nvarchar(250),sys_client_id varchar(4)  , CONSTRAINT [PK_hs_branches] PRIMARY KEY CLUSTERED ([sys_client_id] ASC, [br_id] ASC))
0094 ********141938 0000 => CREATE TABLE fi_ib_accs (ib_id varchar(8),sys_client_id varchar(4)  , CONSTRAINT [PK_fi_ib_accs] PRIMARY KEY CLUSTERED ([sys_client_id] ASC, [ib_id] ASC))
0095 ********141938 0000 => CREATE TABLE fi_rep_design (fi_rep_id varchar(16),rep_name nvarchar(100),sys_client_id varchar(4)  , CONSTRAINT [PK_fi_rep_design] PRIMARY KEY CLUSTERED ([sys_client_id] ASC, [fi_rep_id] ASC))
0096 ********141938 0000 => ALTER TABLE hs_crncy ADD cur_fracts int
0097 ********141938 0000 => CREATE TABLE es_doc_subtypes (doc_type nvarchar(3),doc_subtype nvarchar(2),title nvarchar(200),acc_root nvarchar(24),acc_default nvarchar(24),acc_fix CHAR(1),acc_title nvarchar(100),st_help nvarchar(4000),st_status CHAR(1),st_uid varchar(6),sys_client_id varchar(4)  , CONSTRAINT [PK_es_doc_subtypes] PRIMARY KEY CLUSTERED ([sys_client_id] ASC, [st_uid] ASC))
0098 ********141938 0000 => ALTER TABLE fi_accounts ADD ma_acc_no nvarchar(24)
0099 ********141938 0000 => ALTER TABLE fi_accounts ADD acc_grp varchar(4)
0100 ********141938 0000 => ALTER TABLE fi_accounts ADD acc_path nvarchar(200)
0101 ********141938 0000 => ALTER TABLE fi_accounts ADD recons_date varchar(10)
0102 ********141938 0000 => ALTER TABLE fi_accounts ADD branches varchar(200)
0103 ********141938 0000 => ALTER TABLE fi_accounts ADD projs varchar(200)
0104 ********141938 0000 => ALTER TABLE fi_accounts ADD actvs varchar(200)
0105 ********141938 0000 => ALTER TABLE fi_accounts ADD cc_nos varchar(200)
0106 ********141938 0000 => ALTER TABLE es_fin_docs ADD doc_subtype nvarchar(2)
0107 ********141938 0000 => ALTER TABLE es_fin_docs ADD draft CHAR(1)
0108 ********141938 0000 => ALTER TABLE es_fin_docs ADD coll_comm money
0109 ********141938 0000 => ALTER TABLE es_fin_docs ADD ex_data nvarchar(1000)
0110 ********141938 0000 => ALTER TABLE es_fin_docs ADD doc_stage varchar(16)
0111 ********141938 0000 => ALTER TABLE fi_gl_entries ADD draft CHAR(1)
0112 ********141938 0000 => ALTER TABLE es_items ADD item_url nvarchar(200)
0113 ********141938 0000 => ALTER TABLE es_stock_stores ADD stk_grp_id varchar(8)
0114 ********141938 0000 => ALTER TABLE es_sales_docs ADD draft CHAR(1)
0115 ********141938 0000 => ALTER TABLE es_sales_docs ADD charges_acc_no varchar(24)
0116 ********141938 0000 => ALTER TABLE es_stock_qty ADD item_name nvarchar(64)
0117 ********141938 0000 => ALTER TABLE es_stock_qty ADD item_min_stock int
0118 ********141938 0000 => ALTER TABLE es_stock_qty ADD item_max_stock int
0119 ********141938 0000 => ALTER TABLE es_stock_qty ADD diff_qty int
0120 ********141938 0000 => ALTER TABLE es_stock_qty ADD cached_qty int
0121 ********141938 0000 => ALTER TABLE es_cust ADD units_bal money
0122 ********141938 0000 => ALTER TABLE es_sales_docs ADD phone nvarchar(20)
0123 ********141938 0000 => ALTER TABLE es_sales_docs ADD address nvarchar(100)
0124 ********141938 0000 => ALTER TABLE es_sales_docs ADD doc_stage varchar(16)
0125 ********141938 0000 => ALTER TABLE es_sales_docs ADD from_date varchar(10)
0126 ********141938 0000 => ALTER TABLE es_sales_docs ADD to_date varchar(10)
0127 ********141938 0000 => ALTER TABLE es_sales_docs ADD warranty nvarchar(100)
0128 ********141938 0000 => CREATE TABLE hr_emp_data (emp_no varchar(24),emp_name nvarchar(100),emp_title nvarchar(100),emp_status CHAR(1),emp_grp nvarchar(4),emp_acc_no nvarchar(24),emp_basic_sal money,sal_crncy nvarchar(2),sys_client_id varchar(4)  , CONSTRAINT [PK_hr_emp_data] PRIMARY KEY CLUSTERED ([sys_client_id] ASC, [emp_no] ASC))
0129 ********141938 0000 => [&lf][&cr]UPDATE hs_act_log SET act_code='change' WHERE act_code='edit' AND act_desc IS NOT NULL[&lf]
0130 ********141938 0000 => [&lf][&cr][&lf][&cr][&lf][&cr]UPDATE hs_crncy SET max_rate=9999 WHERE sys_client_id is not null and max_rate > 9999[&lf]
0131 ********141938 0000 => [&lf][&cr][&lf][&cr]UPDATE fi_accounts SET ma_acc_no=fi_acc_no WHERE ma_acc_no IS NULL[&lf][&cr][&lf]
0132 ********141938 0000 => [&lf][&cr][&lf][&cr]CREATE TABLE [dbo].[es_booking_sched]([&lf][&cr]	[sys_client_id] [char](4) NOT NULL,[&lf][&cr]	[item_id] [varchar](24) NOT NULL,[&lf][&cr]	[item_unit] [char](3) NULL,[&lf][&cr]	[batch_no] [nvarchar](50) NULL,[&lf][&cr]	[store_id] [varchar](8) NULL,[&lf][&cr]	[doc_type] [char](3) NOT NULL,[&lf][&cr]	[doc_no] [varchar](16) NOT NULL,[&lf][&cr]	[book_date] [char](8) NOT NULL,[&lf][&cr]	[book_status] [char](1) NOT NULL,[&lf][&cr]	[item_qty] [decimal](9, 3) NULL[&lf][&cr])[&lf][&cr][&lf]
0133 ********141938 0000 => [&lf][&cr][&lf][&cr]CREATE TABLE [dbo].[es_stock_groups]([&lf][&cr]	[sys_client_id] [char](4) NOT NULL,[&lf][&cr]	[group_id] [char](3) NOT NULL,[&lf][&cr]	[group_name] [nvarchar](64) NULL,[&lf][&cr]	[group_status] [char](1) NULL,[&lf][&cr]	[group_desc] [nvarchar](250) NULL,[&lf][&cr]	[group_acc_no] [varchar](24) NULL,[&lf][&cr]	[acc_sales] [varchar](24) NULL,[&lf][&cr]	[acc_sold_cost] [varchar](24) NULL,[&lf][&cr]	[acc_sales_discounts] [varchar](24) NULL,[&lf][&cr]	[acc_sales_free_cost] [varchar](24) NULL,[&lf][&cr]	[acc_sales_ret] [varchar](24) NULL,[&lf][&cr]	[acc_sales_ret_prev] [varchar](24) NULL,[&lf][&cr]	[acc_purch_discount] [varchar](24) NULL,[&lf][&cr]	[acc_purch_ret] [varchar](24) NULL,[&lf][&cr]	[acc_purch_ret_prev] [varchar](24) NULL,[&lf][&cr]	[acc_cost_diff] [varchar](24) NULL,[&lf][&cr] CONSTRAINT [PK_es_item_groups] PRIMARY KEY CLUSTERED [&lf][&cr]([&lf][&cr]	[sys_client_id] ASC,[&lf][&cr]	[group_id] ASC[&lf][&cr])WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY][&lf][&cr]) ON [PRIMARY][&lf][&cr][&lf]
0134 ********141938 0000 => [&lf][&cr][&lf][&cr]DROP VIEW fi_vu_trans_entry[&lf]
0135 ********141938 0000 => [&lf][&cr][&lf][&cr][&lf][&cr][&lf][&cr][&lf][&cr]DROP VIEW vu_biz_docs[&lf]
0136 ********141938 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW vu_biz_docs[&lf][&cr]AS[&lf][&cr]SELECT        sys_client_id, doc_type, doc_no, cov_no, price AS doc_amount, doc_crncy, doc_note, doc_status, suspended, draft, doc_date, ref_doc_type, ref_doc_no, [&lf][&cr]                         crtd_by, crtd_date, crtd_time, ref_no, doc_type + '-' + doc_no AS doc_uno, branch, cc_no, proj_id, actvty_id, doc_crncy_exrate, [&lf][&cr]                         price * ISNULL(doc_crncy_exrate, 1) AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) AS doc_month[&lf][&cr]FROM            es_sales_docs[&lf][&cr]UNION[&lf][&cr]SELECT        sys_client_id, doc_type, doc_no, cov_no, amount AS doc_amount, doc_crncy, doc_note, doc_status, suspended, draft, doc_date, ref_doc_type, ref_doc_no, [&lf][&cr]                         crtd_by, crtd_date, crtd_time, ref_no, doc_type + '-' + doc_no AS doc_uno, branch, cc_no, proj_id, actvty_id, doc_crncy_exrate, [&lf][&cr]                         amount * ISNULL(doc_crncy_exrate, 1) AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) AS doc_month[&lf][&cr]FROM            es_fin_docs[&lf][&cr]UNION[&lf][&cr]SELECT        sys_client_id, '110' AS doc_type, entry_no AS doc_no, NULL AS cov_no, amount AS doc_amount, doc_crncy, entry_memo AS doc_note, doc_status, [&lf][&cr]                         suspended, draft, doc_date, ref_doc_type, ref_doc_no, crtd_by, crtd_date, crtd_time, ref_no, '110-' + entry_no AS doc_uno, branch, cc_no, proj_id, actvty_id, [&lf][&cr]                        1 AS doc_crncy_exrate, amount  AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) [&lf][&cr]                         AS doc_month[&lf][&cr]FROM            fi_gl_entries[&lf][&cr][&lf]
0137 ********141938 0000 => [&lf][&cr][&lf][&cr]DROP VIEW vu_es_doc_line_items[&lf]
0138 ********141938 0000 => [&lf][&cr][&lf][&cr][&lf][&cr]DROP VIEW vu_es_stock_qty[&lf]
0139 ********141938 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW vu_es_stock_qty[&lf][&cr]AS[&lf][&cr]SELECT        A.sys_client_id, A.store_id, A.item_id, B.item_name, A.item_unit, A.batch_no, A.unit_cost, A.item_qty, A.rsrvd_qty, B.item_group, B.for_name, B.sci_name, [&lf][&cr]                         B.item_code, B.item_agent, B.item_make[&lf][&cr]FROM            dbo.es_stock_qty AS A LEFT OUTER JOIN[&lf][&cr]                         dbo.es_items AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id[&lf][&cr][&lf]
0140 ********141938 0000 => [&lf][&cr][&lf][&cr]DROP VIEW vu_es_item_units[&lf]
0141 ********141938 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW vu_es_item_units[&lf][&cr]AS[&lf][&cr]SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u1_id] AS u_id, [u1_price] AS u_price, [&lf][&cr]                         [u1_cost] AS u_cost, 1.0 AS u_in_u1, 1 AS u_x[&lf][&cr]FROM            [es_items][&lf][&cr]WHERE        u1_id IS NOT NULL[&lf][&cr]UNION[&lf][&cr]SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u2_id] AS u_id, [u2_price] AS u_price, [&lf][&cr]                         [u2_cost] AS u_cost, 1.0 / u1_to_u2 AS u_in_u1, 2 AS u_x[&lf][&cr]FROM            [es_items][&lf][&cr]WHERE        u1_to_u2 > 0[&lf][&cr]UNION[&lf][&cr]SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u3_id] AS u_id, [u3_price] AS u_price, [&lf][&cr]                         [u3_cost] AS u_cost, 1.0 / u1_to_u2 / u2_to_u3 AS u_in_u1, 3 AS u_x[&lf][&cr]FROM            [es_items][&lf][&cr]WHERE         u1_to_u2 > 0 AND  u2_to_u3 > 0[&lf][&cr][&lf]
0142 ********141938 0000 => [&lf][&cr][&lf][&cr]DROP VIEW vu_es_stock_stats[&lf]
0143 ********141938 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW vu_es_stock_stats[&lf][&cr]AS[&lf][&cr]SELECT        A.sys_client_id, A.store_id, A.item_id, A.item_unit, A.batch_no, A.item_qty, A.rsrvd_qty, B.item_type, B.item_group, B.item_subgr, B.item_form, B.item_make, [&lf][&cr]                         B.item_agent, B.u_price, B.u_cost, B.u1_id, B.u_in_u1, B.u_x, A.item_qty * B.u_in_u1 AS item_qty_u1[&lf][&cr]FROM            dbo.es_stock_qty AS A LEFT OUTER JOIN[&lf][&cr]                         dbo.vu_es_item_units AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id AND A.item_unit = B.u_id[&lf][&cr]WHERE        (A.item_qty <> 0)[&lf][&cr][&lf]
0144 ********141938 0000 => [&lf][&cr][&lf][&cr]ALTER TABLE es_sales_docs ALTER COLUMN phone varchar(20) NULL[&lf]
0145 ********141938 0000 => [&lf][&cr][&lf][&cr]ALTER TABLE es_sales_docs ALTER COLUMN address nvarchar(100) NULL[&lf]
0146 ********141938 0000 => [&lf][&cr][&lf][&cr]ALTER TABLE es_sales_docs ALTER COLUMN to_date varchar(10) NULL[&lf]
0147 ********141938 0000 => [&lf][&cr][&lf][&cr]ALTER TABLE es_sales_docs ALTER COLUMN from_date varchar(10) NULL[&lf]
0148 ********141938 0000 => [&lf][&cr][&lf][&cr]ALTER TABLE es_sales_docs ALTER COLUMN warranty nvarchar(100) NULL[&lf]
0149 ********141938 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW [dbo].[vu_es_stock_qty_pvt_units][&lf][&cr]AS[&lf][&cr]select sys_client_id, store_id,item_id,batch_no, [1] as u1_qty,[2] as u2_qty,[3] as u3_qty[&lf][&cr]from[&lf][&cr]([&lf][&cr]  select sys_client_id, store_id,item_id,batch_no, item_qty, u_x[&lf][&cr]  from vu_es_stock_stats[&lf][&cr]) t[&lf][&cr]pivot[&lf][&cr]([&lf][&cr]  SUM(item_qty) for u_x in ([1],[2],[3])[&lf][&cr]) pvt[&lf][&cr][&lf]
0150 ********141938 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW [dbo].[vu_es_stock_qty_pvt_units_details][&lf][&cr]AS[&lf][&cr]SELECT  A.sys_client_id, A.store_id, A.item_id, A.batch_no, A.u1_qty, A.u2_qty, A.u3_qty, B.u1_id, B.u1_price, B.u1_cost, B.u2_id, B.u2_cost, B.u2_price, B.u3_id, [&lf][&cr]                         B.u3_cost, B.u3_price, B.item_group, B.item_name[&lf][&cr]FROM            dbo.vu_es_stock_qty_pvt_units AS A LEFT OUTER JOIN[&lf][&cr]                         dbo.es_items AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id[&lf][&cr][&lf]
0151 ********141938 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW vu_es_items_cache[&lf][&cr]AS[&lf][&cr]SELECT        A.item_id, A.item_name, A.sci_name, A.item_code, A.for_name, A.item_status, A.item_type, A.batch_type, A.item_group, A.def_purch_unit, A.def_sale_unit, [&lf][&cr]                         A.item_crncy, A.u1_id, A.u1_price, A.u1_cost, A.u1_min_price, A.u2_id, A.u2_price, A.u2_cost, A.u2_min_price, A.u3_id, A.u3_price, A.u3_cost, A.u3_min_price, [&lf][&cr]                         A.item_spec, A.item_tax_pct, A.sys_client_id, A.item_flags, B.item_qty_u1[&lf][&cr]FROM            dbo.es_items AS A LEFT OUTER JOIN[&lf][&cr]                             (SELECT sys_client_id, item_id, u1_id, SUM(item_qty_u1) AS item_qty_u1, SUM(item_qty * u_cost) AS u_cost[&lf][&cr]                                FROM dbo.vu_es_stock_stats[&lf][&cr]                                GROUP BY sys_client_id, item_id, u1_id) AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id[&lf]
0152 ********141938 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW vu_es_pr_po[&lf][&cr]AS[&lf][&cr]SELECT        sys_client_id, doc_type AS pr_doc_type, doc_no AS pr_no, item_id, item_unit, ref_doc_type AS po_doc_type, ref_doc_no AS po_no, item_qty AS po_qty[&lf][&cr]FROM            dbo.es_docs_link[&lf][&cr]WHERE        (doc_type = '301') AND (ref_doc_type = '305')[&lf][&cr][&lf]
0153 ********141938 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW vu_es_pr_po_gr[&lf][&cr]AS[&lf][&cr]SELECT        A.sys_client_id, A.pr_doc_type, A.pr_no, A.item_id, A.item_unit, A.po_doc_type, A.po_no, A.po_qty, B.ref_doc_type AS gr_doc_type, B.ref_doc_no AS gr_no, [&lf][&cr]                         B.item_qty AS gr_qty[&lf][&cr]FROM            dbo.vu_es_pr_po AS A LEFT OUTER JOIN[&lf][&cr]                             (SELECT        sys_client_id, doc_type, doc_no, item_id, item_unit, count_type, ref_doc_type, ref_doc_no, item_qty[&lf][&cr]                                FROM            dbo.es_docs_link[&lf][&cr]                                WHERE        (doc_type = '305') AND (ref_doc_type = '307' OR[&lf][&cr]                                                         ref_doc_type = '270')) AS B ON A.sys_client_id = B.sys_client_id AND A.po_doc_type = B.doc_type AND A.po_no = B.doc_no AND [&lf][&cr]                         A.item_id = B.item_id AND A.item_unit = B.item_unit[&lf][&cr][&lf]
0154 ********141938 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW vu_es_pr_po_gr_vi[&lf][&cr]AS[&lf][&cr]SELECT        A.sys_client_id, A.pr_no, A.item_id, A.item_unit, A.po_doc_type, A.po_no, A.po_qty, A.gr_doc_type, A.gr_no, A.gr_qty, B.ref_doc_type AS vi_doc_type, [&lf][&cr]                         B.ref_doc_no AS vi_no, B.item_qty AS vi_qty[&lf][&cr]FROM            dbo.vu_es_pr_po_gr AS A LEFT OUTER JOIN[&lf][&cr]                             (SELECT        sys_client_id, doc_type, doc_no, item_id, item_unit, count_type, ref_doc_type, ref_doc_no, item_qty[&lf][&cr]                                FROM            dbo.es_docs_link[&lf][&cr]                                WHERE        (ref_doc_type = '309') AND (doc_type = '307' OR[&lf][&cr]                                                         doc_type = '270')) AS B ON A.sys_client_id = B.sys_client_id AND A.gr_doc_type = B.doc_type AND A.gr_no = B.doc_no AND [&lf][&cr]                         A.item_id = B.item_id AND A.item_unit = B.item_unit[&lf][&cr][&lf]
0155 ************** 0001 => INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('sys',NULL,'except','alert',' @LoadCodeList_UseSql:SELECT group_id,group_name FROM es_stock_groups WHERE sys_client_id=''0000'': Invalid object name ''es_stock_groups''.','**************','1089')
0156 ************** 0000 => [&lf][&cr]UPDATE hs_act_log SET act_code='change' WHERE act_code='edit' AND act_desc IS NOT NULL[&lf]
0157 ************** 0000 => [&lf][&cr][&lf][&cr][&lf][&cr]UPDATE hs_crncy SET max_rate=9999 WHERE sys_client_id is not null and max_rate > 9999[&lf]
0158 ************** 0000 => [&lf][&cr][&lf][&cr]UPDATE fi_accounts SET ma_acc_no=fi_acc_no WHERE ma_acc_no IS NULL[&lf][&cr][&lf]
0159 ************** 0000 => [&lf][&cr][&lf][&cr][&lf][&cr][&lf][&cr][&lf][&cr]DROP VIEW vu_biz_docs[&lf]
0160 ************** 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW vu_biz_docs[&lf][&cr]AS[&lf][&cr]SELECT        sys_client_id, doc_type, doc_no, cov_no, price AS doc_amount, doc_crncy, doc_note, doc_status, suspended, draft, doc_date, ref_doc_type, ref_doc_no, [&lf][&cr]                         crtd_by, crtd_date, crtd_time, ref_no, doc_type + '-' + doc_no AS doc_uno, branch, cc_no, proj_id, actvty_id, doc_crncy_exrate, [&lf][&cr]                         price * ISNULL(doc_crncy_exrate, 1) AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) AS doc_month[&lf][&cr]FROM            es_sales_docs[&lf][&cr]UNION[&lf][&cr]SELECT        sys_client_id, doc_type, doc_no, cov_no, amount AS doc_amount, doc_crncy, doc_note, doc_status, suspended, draft, doc_date, ref_doc_type, ref_doc_no, [&lf][&cr]                         crtd_by, crtd_date, crtd_time, ref_no, doc_type + '-' + doc_no AS doc_uno, branch, cc_no, proj_id, actvty_id, doc_crncy_exrate, [&lf][&cr]                         amount * ISNULL(doc_crncy_exrate, 1) AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) AS doc_month[&lf][&cr]FROM            es_fin_docs[&lf][&cr]UNION[&lf][&cr]SELECT        sys_client_id, '110' AS doc_type, entry_no AS doc_no, NULL AS cov_no, amount AS doc_amount, doc_crncy, entry_memo AS doc_note, doc_status, [&lf][&cr]                         suspended, draft, doc_date, ref_doc_type, ref_doc_no, crtd_by, crtd_date, crtd_time, ref_no, '110-' + entry_no AS doc_uno, branch, cc_no, proj_id, actvty_id, [&lf][&cr]                        1 AS doc_crncy_exrate, amount  AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) [&lf][&cr]                         AS doc_month[&lf][&cr]FROM            fi_gl_entries[&lf][&cr][&lf]
0161 ************** 0000 => [&lf][&cr][&lf][&cr][&lf][&cr]DROP VIEW vu_es_stock_qty[&lf]
0162 ************** 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW vu_es_stock_qty[&lf][&cr]AS[&lf][&cr]SELECT        A.sys_client_id, A.store_id, A.item_id, B.item_name, A.item_unit, A.batch_no, A.unit_cost, A.item_qty, A.rsrvd_qty, B.item_group, B.for_name, B.sci_name, [&lf][&cr]                         B.item_code, B.item_agent, B.item_make[&lf][&cr]FROM            dbo.es_stock_qty AS A LEFT OUTER JOIN[&lf][&cr]                         dbo.es_items AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id[&lf][&cr][&lf]
0163 ************** 0000 => [&lf][&cr][&lf][&cr]DROP VIEW vu_es_item_units[&lf]
0164 ************** 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW vu_es_item_units[&lf][&cr]AS[&lf][&cr]SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u1_id] AS u_id, [u1_price] AS u_price, [&lf][&cr]                         [u1_cost] AS u_cost, 1.0 AS u_in_u1, 1 AS u_x[&lf][&cr]FROM            [es_items][&lf][&cr]WHERE        u1_id IS NOT NULL[&lf][&cr]UNION[&lf][&cr]SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u2_id] AS u_id, [u2_price] AS u_price, [&lf][&cr]                         [u2_cost] AS u_cost, 1.0 / u1_to_u2 AS u_in_u1, 2 AS u_x[&lf][&cr]FROM            [es_items][&lf][&cr]WHERE        u1_to_u2 > 0[&lf][&cr]UNION[&lf][&cr]SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u3_id] AS u_id, [u3_price] AS u_price, [&lf][&cr]                         [u3_cost] AS u_cost, 1.0 / u1_to_u2 / u2_to_u3 AS u_in_u1, 3 AS u_x[&lf][&cr]FROM            [es_items][&lf][&cr]WHERE         u1_to_u2 > 0 AND  u2_to_u3 > 0[&lf][&cr][&lf]
0165 ************** 0000 => [&lf][&cr][&lf][&cr]DROP VIEW vu_es_stock_stats[&lf]
0166 ************** 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW vu_es_stock_stats[&lf][&cr]AS[&lf][&cr]SELECT        A.sys_client_id, A.store_id, A.item_id, A.item_unit, A.batch_no, A.item_qty, A.rsrvd_qty, B.item_type, B.item_group, B.item_subgr, B.item_form, B.item_make, [&lf][&cr]                         B.item_agent, B.u_price, B.u_cost, B.u1_id, B.u_in_u1, B.u_x, A.item_qty * B.u_in_u1 AS item_qty_u1[&lf][&cr]FROM            dbo.es_stock_qty AS A LEFT OUTER JOIN[&lf][&cr]                         dbo.vu_es_item_units AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id AND A.item_unit = B.u_id[&lf][&cr]WHERE        (A.item_qty <> 0)[&lf][&cr][&lf]
0167 ************** 0000 => [&lf][&cr][&lf][&cr]ALTER TABLE es_sales_docs ALTER COLUMN phone varchar(20) NULL[&lf]
0168 ************** 0000 => [&lf][&cr][&lf][&cr]ALTER TABLE es_sales_docs ALTER COLUMN address nvarchar(100) NULL[&lf]
0169 ************** 0000 => [&lf][&cr][&lf][&cr]ALTER TABLE es_sales_docs ALTER COLUMN to_date varchar(10) NULL[&lf]
0170 ************** 0000 => [&lf][&cr][&lf][&cr]ALTER TABLE es_sales_docs ALTER COLUMN from_date varchar(10) NULL[&lf]
0171 ************** 0000 => [&lf][&cr][&lf][&cr]ALTER TABLE es_sales_docs ALTER COLUMN warranty nvarchar(100) NULL[&lf]
0172 ************** 0000 => [&lf][&cr][&lf][&cr]DROP VIEW [dbo].[vu_es_stock_qty_pvt_units][&lf]
0173 ************** 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW [dbo].[vu_es_stock_qty_pvt_units][&lf][&cr]AS[&lf][&cr]select sys_client_id, store_id,item_id,batch_no, [1] as u1_qty,[2] as u2_qty,[3] as u3_qty[&lf][&cr]from[&lf][&cr]([&lf][&cr]  select sys_client_id, store_id,item_id,batch_no, item_qty, u_x[&lf][&cr]  from vu_es_stock_stats[&lf][&cr]) t[&lf][&cr]pivot[&lf][&cr]([&lf][&cr]  SUM(item_qty) for u_x in ([1],[2],[3])[&lf][&cr]) pvt[&lf][&cr][&lf]
0174 ************** 0000 => [&lf][&cr][&lf][&cr]DROP VIEW [dbo].[vu_es_stock_qty_pvt_units_details][&lf]
0175 ************** 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW [dbo].[vu_es_stock_qty_pvt_units_details][&lf][&cr]AS[&lf][&cr]SELECT  A.sys_client_id, A.store_id, A.item_id, A.batch_no, A.u1_qty, A.u2_qty, A.u3_qty, B.u1_id, B.u1_price, B.u1_cost, B.u2_id, B.u2_cost, B.u2_price, B.u3_id, [&lf][&cr]                         B.u3_cost, B.u3_price, B.item_group, B.item_name[&lf][&cr]FROM            dbo.vu_es_stock_qty_pvt_units AS A LEFT OUTER JOIN[&lf][&cr]                         dbo.es_items AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id[&lf][&cr][&lf]
0176 ************** 0000 => [&lf][&cr][&lf][&cr]DROP VIEW vu_es_items_cache[&lf]
0177 ************** 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW vu_es_items_cache[&lf][&cr]AS[&lf][&cr]SELECT        A.item_id, A.item_name, A.sci_name, A.item_code, A.for_name, A.item_status, A.item_type, A.batch_type, A.item_group, A.def_purch_unit, A.def_sale_unit, [&lf][&cr]                         A.item_crncy, A.u1_id, A.u1_price, A.u1_cost, A.u1_min_price, A.u2_id, A.u2_price, A.u2_cost, A.u2_min_price, A.u3_id, A.u3_price, A.u3_cost, A.u3_min_price, [&lf][&cr]                         A.item_spec, A.item_tax_pct, A.sys_client_id, A.item_flags, B.item_qty_u1[&lf][&cr]FROM            dbo.es_items AS A LEFT OUTER JOIN[&lf][&cr]                             (SELECT sys_client_id, item_id, u1_id, SUM(item_qty_u1) AS item_qty_u1, SUM(item_qty * u_cost) AS u_cost[&lf][&cr]                                FROM dbo.vu_es_stock_stats[&lf][&cr]                                GROUP BY sys_client_id, item_id, u1_id) AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id[&lf]
0178 ********142304 0001 => UPDATE hs_config SET cfg_value='Y' WHERE cfg_key='app-last-shutdown-clean'
