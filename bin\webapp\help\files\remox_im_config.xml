﻿<?xml version="1.0" encoding="utf-8"?>
<SystemHelp xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <items>



	  <Entry>
		  <id>es.link_stock_to_gl</id>
		  <title>ربط نظام المخزون مع نظام الأستاذ العام</title>
		  <content>
			  <![CDATA[
عند تفعيل هذا الخيار سيتم تطبيق التأثيرات المالية للعمليات التي تخص مستندات المخزون (مثل التوريد والصرف المخزني) وذلك بعمل ترحيل للقيود المحاسبية الخاصة بالمخزون السلعي عند ترحيل المستندات إلى نظام الأستاذ العام.

عند تعطيل هذا الخيار
- سوف يتم تعطيل  إنشاء أو ترحيل (القيود المالية الناتجة عن عمليات المخزون) للأستاذ العام و يجب على المحاسب عمل قيود يدوية
- سوف يتم إستخدام مستندات المخزون لعمل التأثيرات المخزنية بالكميات فقط بدون تأثير مالي
- سوف يتم تعطيل نظام الجرد المستمر
- سوف يتم إخفاء حساب الكلفة في مستندات المخزون

]]>
		  </content>
	  </Entry>


	  <Entry>
		  <id>es.perpetual_inventory</id>
		  <title>التقييد وفق نظام الجرد المستمر</title>
		  <content>
			  <![CDATA[
هذا الخيار يتطلب تفعيل خيار ربط المخزون بنظام الأستاذ العام وسوف يتم تعطيله آليا عند تعطيل خيار الربط مع الأستاذ العام.
عند تعطيل هذا الخيار سوف يقوم بإستخدام نظام الجرد الدوري و بالتالي لن يكون هناك قيود مالية خاصة بالمخزون السلعي 

في نظام الجرد المستمر:
يتم عمل أي قيود يومية محاسبية عند الإضافة أو الصرف من المخزن. 
ويتم تسجيل حركة المخزون بالتكلفة، وليس بسعر البيع، بحيث يكون حساب المخزون آخر الفترة المحاسبية يعكس بوضوح قيمة المواد الموجودة بالمخزن بدقة، ليعبر عن مخزون آخر المدة برقم فعلي.
يمكن الحصول على تقرير الأرباح والخسائر في أي وقت خلال المدة.

في نظام الجرد الدوري: 
لا يتم عمل أي قيود يومية محاسبية عند الإضافة أو الصرف من المخازن. فمثلا عند شراء أو بيع البضاعة لا تسجل قيود سوى قيود المشتريات والمبيعات والسداد للموردين والتحصيل من العملاء فقط.
و لا يمكن الحصول على تقرير الأرباح والخسائر إلا بعد  جرد المخزون
حيث يجب عمل جرد للمخزون في نهاية الفترة وعمل قيد محاسبي للإعتراف بقيمة مخزون آخر المدة 


]]>
		  </content>
	  </Entry>
	  

	  <Entry>
		  <id>es.no_stock_cost</id>
		  <title>عند تعطيل الربط مع النظام المالي يتم تعطيل إحتساب متوسط كلفة المخزون</title>
		  <content>
			  <![CDATA[
بشكل عام يقوم النظام بحساب متوسط كلفة الأصناف عند ترحيل مستندات فاتورة الشراء أو التوريد المخزني، ويقوم بحفظ متوسط الكلفة في بيانات الصنف.
لكن عند تفعيل هذا الخيار مع تعطيل (ربط نظام المخزون مع نظام الأستاذ العام) سوف يوجه النظام لعدم حساب متوسط التكلفة للأصناف
يفضل عدم تفعيل هذا الخيار

]]>
		  </content>
	  </Entry>
	  
    

    <!--   
   <Entry>
     <id></id>
     <title></title>
     <content>
       <![CDATA[

]]>
     </content>
   </Entry>
-->
  </items>
</SystemHelp>

