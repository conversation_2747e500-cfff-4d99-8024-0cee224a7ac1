0001 ************** 0001 => UPDATE hs_config SET cfg_value='N' WHERE cfg_key='app-last-shutdown-clean'
0002 ************** 0001 => UPDATE hs_config SET cfg_value='la+sqJa4tbnkmZ+iqqY=' WHERE cfg_key='_$sys_fid_'
0003 ************** 0001 => UPDATE hs_config SET cfg_value='lL+qvaOUv7Oz4p+Zt6Q=' WHERE cfg_key='_$sys_lsdt_'
0004 ************** 0001 => UPDATE hs_config SET cfg_value='kaI=' WHERE cfg_key='_$sys_stcn_'
0005 ************** 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_theme'
0006 ************** 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_theme',NULL,'C','User Theme',NULL,0,32,NULL,'user-themes',*********,'6')
0007 ************** 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_menus'
0008 ************** 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_menus','app-menus-base','C','قائمة المستخدم',NULL,0,65536,NULL,NULL,*********,'4')
0009 ************** 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_cash_id'
0010 ************** 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_cash_id','cash_id','F','الصندوق الإفتراضي',NULL,0,2147483647,'0','fi-cl-cash-c',*********,'0')
0011 ************** 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
0012 ************** 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,2147483647,'0','fi-brnch',*********,'0')
0013 ************** 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_rep'
0014 ************** 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_rep','sales_rep','F','المندوب الإفتراضي',NULL,0,2147483647,'0','fi-cl-reps',*********,'0')
0015 ************** 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_branch'
0016 ************** 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_branch','auth_usr_branch','C','الفروع',NULL,0,65536,NULL,'fi-brnch',*********,'4')
0017 ************** 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_teller'
0018 ************** 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_teller','auth_usr_teller','C','الصناديق',NULL,0,65536,NULL,'fi-cl-cash-c',*********,'4')
0019 ************** 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_banks'
0020 ************** 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_banks','auth_usr_banks','C','البنوك',NULL,0,65536,NULL,'fi-cl-banks',*********,'4')
0021 ************** 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_cc'
0022 ************** 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_cc','auth_usr_cc','C','المراكز',NULL,0,65536,NULL,'fi-cl-cc',*********,'4')
0023 ************** 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_proj'
0024 ************** 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_proj','auth_usr_proj','C','المشاريع',NULL,0,65536,NULL,'fi-proj',*********,'4')
0025 ************** 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_actv'
0026 ************** 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_actv','auth_usr_actv','C','النشاط',NULL,0,65536,NULL,'fi-actv',*********,'4')
0027 ************** 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_accs'
0028 ************** 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_accs','auth_usr_accs','C','المجموعات المحاسبية',NULL,0,65536,NULL,'fi-accgr',*********,'4')
0029 ************** 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='max_discount_pct'
0030 ************** 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','max_discount_pct',NULL,'F','نسبة التخفيض المسموحة للمستخدم %',NULL,0,100,'0',NULL,*********,'0')
0031 ************** 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_store_id'
0032 ************** 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_store_id','store_id','F','المخزن الإفتراضي',NULL,0,2147483647,'0','es-cl-stores',*********,'0')
0033 ************** 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
0034 ************** 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,2147483647,'0','fi-brnch',*********,'0')
0035 ************** 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_sales'
0036 ************** 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_sales','auth_usr_sales','C','صلاحيات إضافية',NULL,0,65536,NULL,'es-sales-auth',*********,'4')
0037 ************** 0001 => DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_store'
0038 ************** 0001 => INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_store','auth_usr_store','C','المخازن',NULL,0,65536,NULL,'es-cl-stores',*********,'4')
0039 ************** 0001 => INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('0001','24BNWP730Z81',NULL,'SYS','admin','Successfull login','20250725','142407','W',0,0,NULL,'sys',NULL)
0040 ************** 0001 => INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.91.0 ( 23/11/2020 )  Standardhttp IP=::1:50301 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','**************','0001')
0041 ************** 0001 => UPDATE hs_logindata SET last_activity_dt='**************' WHERE sys_client_id='0001' AND user_id='admin'
0042 ************** 0000 => [&lf][&cr]UPDATE hs_act_log SET act_code='change' WHERE act_code='edit' AND act_desc IS NOT NULL[&lf]
0043 ************** 0000 => [&lf][&cr][&lf][&cr][&lf][&cr]UPDATE hs_crncy SET max_rate=9999 WHERE sys_client_id is not null and max_rate > 9999[&lf]
0044 ************** 0000 => [&lf][&cr][&lf][&cr]UPDATE fi_accounts SET ma_acc_no=fi_acc_no WHERE ma_acc_no IS NULL[&lf][&cr][&lf]
0045 ************** 0000 => [&lf][&cr][&lf][&cr]UPDATE fi_trans_entry SET ma_acc_no=fi_acc_no WHERE ma_acc_no IS NULL[&lf][&cr][&lf]
0046 ************** 0000 => [&lf][&cr][&lf][&cr]DROP VIEW fi_vu_trans_entry[&lf]
0047 ************** 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW fi_vu_trans_entry[&lf][&cr]AS[&lf][&cr]SELECT        A.sys_client_id, A.tr_id, A.doc_type, A.doc_no, A.tr_date, A.tr_time, A.tr_user, B.tr_entry_no, B.entry_line_no, B.amount_debit, B.amount_credit, [&lf][&cr]                         B.amount_credit - B.amount_debit AS amount_bal, B.entry_memo, B.entry_type, A.doc_crncy, B.doc_date, B.acc_crncy, B.ex_rate, B.amount_debit_cc, [&lf][&cr]                         B.amount_credit_cc, B.amount_credit_cc - B.amount_debit_cc AS amount_bal_cc, B.ma_acc_no, B.fi_acc_no, B.fi_year, B.fi_period, B.branch, B.cc_no, [&lf][&cr]                         B.proj_id, B.actvty_id, B.budget, B.cov_acc_no, B.cov_amount_debit, B.cov_amount_credit, A.doc_type + '-' + A.doc_no AS doc_uno, B.reversal, B.verify, B.cov_crncy, [&lf][&cr]                         B.cov_crncy_exrate, B.line_debit, B.line_credit, B.line_crncy, B.line_crncy_exrate, B.doc_month, B.doc_year, A.tr_memo AS doc_note, B.sub_gl_no,[&lf][&cr]                         B.tr_id + '-' + CONVERT(varchar,B.tr_entry_no) +'-'+ CONVERT(varchar, B.entry_line_no) AS line_uid[&lf][&cr]FROM            dbo.fi_trans AS A RIGHT OUTER JOIN[&lf][&cr]                         dbo.fi_trans_entry AS B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id[&lf]
0048 ************** 0000 => [&lf][&cr][&lf][&cr]DROP VIEW fi_vu_trans_entry_ex[&lf]
0049 ************** 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW fi_vu_trans_entry_ex[&lf][&cr]AS[&lf][&cr]SELECT        A.sys_client_id, A.tr_id, A.doc_type, A.doc_no, A.tr_date, A.tr_time, A.tr_user, B.tr_entry_no, B.entry_line_no, B.amount_debit, B.amount_credit, [&lf][&cr]                         B.amount_credit - B.amount_debit AS amount_bal, B.entry_memo, B.entry_type, A.doc_crncy, B.doc_date, B.acc_crncy, B.ex_rate, B.amount_debit_cc, [&lf][&cr]                         B.amount_credit_cc, B.amount_credit_cc - B.amount_debit_cc AS amount_bal_cc, B.ma_acc_no, B.fi_acc_no, B.fi_year, B.fi_period, B.branch, B.cc_no, [&lf][&cr]                         B.proj_id, B.actvty_id, B.budget, B.cov_acc_no, A.doc_type + '-' + A.doc_no AS doc_uno, B.reversal, B.verify, B.line_debit, B.line_credit, B.line_crncy, [&lf][&cr]                         B.line_crncy_exrate, B.doc_month, B.doc_year, B.sub_gl_no, A.tr_memo AS doc_note, B.tr_id + '-' + CONVERT(varchar,B.tr_entry_no) +'-'+ CONVERT(varchar, B.entry_line_no) AS line_uid,[&lf][&cr]                         C.acc_parent, C.acc_root, C.acc_report, C.acc_nat, C.linked_acc_no, C.acc_type [&lf][&cr]FROM            dbo.fi_trans AS A RIGHT OUTER JOIN[&lf][&cr]                         dbo.fi_trans_entry AS B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id INNER JOIN[&lf][&cr]                         dbo.fi_accounts AS C ON A.sys_client_id = C.sys_client_id AND B.ma_acc_no = C.ma_acc_no AND B.acc_crncy = C.acc_crncy[&lf]
0050 ************** 0000 => [&lf][&cr][&lf][&cr]DROP VIEW fi_vu_trans_entry_cov_ex[&lf]
0051 ************** 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW fi_vu_trans_entry_cov_ex[&lf][&cr]AS[&lf][&cr]SELECT        A.sys_client_id, A.tr_id, A.doc_type, A.doc_no, A.tr_date, A.tr_time, A.tr_user, B.tr_entry_no, B.entry_line_no, B.amount_debit, B.amount_credit, [&lf][&cr]                         B.amount_credit - B.amount_debit AS amount_bal, B.entry_memo, B.entry_type, A.doc_crncy, B.doc_date, B.acc_crncy, B.ex_rate, B.amount_debit_cc, [&lf][&cr]                         B.amount_credit_cc, B.amount_credit_cc - B.amount_debit_cc AS amount_bal_cc, B.ma_acc_no, B.fi_acc_no, B.fi_year, B.fi_period, B.branch, B.cc_no, [&lf][&cr]                         B.proj_id, B.actvty_id, B.budget, B.cov_acc_no, A.doc_type + '-' + A.doc_no AS doc_uno, B.reversal, B.verify, B.line_debit, B.line_credit, B.line_crncy, [&lf][&cr]                         B.line_crncy_exrate, B.doc_month, B.doc_year, A.tr_memo AS doc_note, B.tr_id + '-' + CONVERT(varchar,B.tr_entry_no) +'-'+ CONVERT(varchar, B.entry_line_no) AS line_uid,[&lf][&cr]                         C.acc_parent, C.acc_root, C.acc_report, C.acc_nat, C.linked_acc_no, C.acc_type [&lf][&cr]FROM            dbo.fi_trans AS A RIGHT OUTER JOIN[&lf][&cr]                         dbo.fi_trans_entry AS B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id INNER JOIN[&lf][&cr]                         dbo.fi_accounts AS C ON A.sys_client_id = C.sys_client_id AND B.cov_acc_no = C.ma_acc_no AND B.acc_crncy = C.acc_crncy[&lf]
0052 ************** 0000 => [&lf][&cr][&lf][&cr][&lf][&cr][&lf][&cr][&lf][&cr]DROP VIEW vu_biz_docs[&lf]
0053 ************** 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW vu_biz_docs[&lf][&cr]AS[&lf][&cr]SELECT        sys_client_id, doc_type, doc_no, cov_no, price AS doc_amount, doc_crncy, doc_note, doc_status, suspended, draft, doc_date, ref_doc_type, ref_doc_no, [&lf][&cr]                         crtd_by, crtd_date, crtd_time, ref_no, doc_type + '-' + doc_no AS doc_uno, branch, cc_no, proj_id, actvty_id, doc_crncy_exrate, [&lf][&cr]                         price * ISNULL(doc_crncy_exrate, 1) AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) AS doc_month[&lf][&cr]FROM            es_sales_docs[&lf][&cr]UNION[&lf][&cr]SELECT        sys_client_id, doc_type, doc_no, cov_no, amount AS doc_amount, doc_crncy, doc_note, doc_status, suspended, draft, doc_date, ref_doc_type, ref_doc_no, [&lf][&cr]                         crtd_by, crtd_date, crtd_time, ref_no, doc_type + '-' + doc_no AS doc_uno, branch, cc_no, proj_id, actvty_id, doc_crncy_exrate, [&lf][&cr]                         amount * ISNULL(doc_crncy_exrate, 1) AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) AS doc_month[&lf][&cr]FROM            es_fin_docs[&lf][&cr]UNION[&lf][&cr]SELECT        sys_client_id, '110' AS doc_type, entry_no AS doc_no, NULL AS cov_no, amount AS doc_amount, doc_crncy, entry_memo AS doc_note, doc_status, [&lf][&cr]                         suspended, draft, doc_date, ref_doc_type, ref_doc_no, crtd_by, crtd_date, crtd_time, ref_no, '110-' + entry_no AS doc_uno, branch, cc_no, proj_id, actvty_id, [&lf][&cr]                        1 AS doc_crncy_exrate, amount  AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) [&lf][&cr]                         AS doc_month[&lf][&cr]FROM            fi_gl_entries[&lf][&cr][&lf]
0054 ************** 0000 => [&lf][&cr][&lf][&cr]DROP VIEW vu_es_doc_line_items[&lf]
0055 ************** 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW vu_es_doc_line_items[&lf][&cr]AS[&lf][&cr]SELECT        A.sys_client_id, A.doc_type, A.doc_no, A.doc_subtype, A.cov_no, A.doc_status, A.doc_date, LEFT(A.doc_date, 4) AS doc_year, SUBSTRING(A.doc_date, 5, 2) [&lf][&cr]                         AS doc_month, A.doc_crncy, ISNULL(A.doc_crncy_exrate, 1) AS doc_crncy_exrate, A.store_id, A.cash_id, A.region, A.sales_rep, A.crtd_by,A.doc_stage, A.ref_doc_type, A.ref_doc_no,[&lf][&cr]                        C.item_id, C.item_type, C.item_group, C.item_subgr, C.item_form, C.item_make, C.item_agent, B.item_unit, B.batch_no, B.unit_price, CAST(ISNULL(A.doc_crncy_exrate, [&lf][&cr]                         1) * B.unit_price AS decimal) AS unit_price_cc, B.item_qty1, B.item_qty2, B.item_discount, B.sub_total, CAST(ISNULL(A.doc_crncy_exrate, 1) [&lf][&cr]                         * B.sub_total AS decimal) AS sub_total_cc, B.item_qty_u1, B.free_qty_u1, C.u1_id, B.profit_cc, B.from_date, B.to_date, B.line_status, B.item_note, [&lf][&cr]                         B.ex_data30, B.line_id, ISNULL(B.branch, A.branch) AS branch, ISNULL(B.cc_no, A.cc_no) AS cc_no, ISNULL(B.proj_id, A.proj_id) AS proj_id, ISNULL(B.actvty_id, A.actvty_id) AS actvty_id[&lf][&cr]FROM            dbo.es_sales_docs AS A INNER JOIN[&lf][&cr]                         dbo.es_doc_details AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no INNER JOIN[&lf][&cr]                         dbo.es_items AS C ON B.sys_client_id = C.sys_client_id AND B.item_id = C.item_id[&lf][&cr][&lf]
0056 ************** 0000 => [&lf][&cr][&lf][&cr][&lf][&cr]DROP VIEW vu_es_stock_qty[&lf]
0057 ************** 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW vu_es_stock_qty[&lf][&cr]AS[&lf][&cr]SELECT        A.sys_client_id, A.store_id, A.item_id, B.item_name, A.item_unit, A.batch_no, A.unit_cost, A.item_qty, A.rsrvd_qty, B.item_group, B.for_name, B.sci_name, [&lf][&cr]                         B.item_code, B.item_agent, B.item_make[&lf][&cr]FROM            dbo.es_stock_qty AS A LEFT OUTER JOIN[&lf][&cr]                         dbo.es_items AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id[&lf][&cr][&lf]
0058 ************** 0000 => [&lf][&cr][&lf][&cr]DROP VIEW vu_es_item_units[&lf]
0059 ************** 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW vu_es_item_units[&lf][&cr]AS[&lf][&cr]SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u1_id] AS u_id, [u1_price] AS u_price, [&lf][&cr]                         [u1_cost] AS u_cost, 1.0 AS u_in_u1, 1 AS u_x[&lf][&cr]FROM            [es_items][&lf][&cr]WHERE        u1_id IS NOT NULL[&lf][&cr]UNION[&lf][&cr]SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u2_id] AS u_id, [u2_price] AS u_price, [&lf][&cr]                         [u2_cost] AS u_cost, 1.0 / u1_to_u2 AS u_in_u1, 2 AS u_x[&lf][&cr]FROM            [es_items][&lf][&cr]WHERE        u1_to_u2 > 0[&lf][&cr]UNION[&lf][&cr]SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u3_id] AS u_id, [u3_price] AS u_price, [&lf][&cr]                         [u3_cost] AS u_cost, 1.0 / u1_to_u2 / u2_to_u3 AS u_in_u1, 3 AS u_x[&lf][&cr]FROM            [es_items][&lf][&cr]WHERE         u1_to_u2 > 0 AND  u2_to_u3 > 0[&lf][&cr][&lf]
0060 ************** 0000 => [&lf][&cr][&lf][&cr]DROP VIEW vu_es_stock_stats[&lf]
0061 ************** 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW vu_es_stock_stats[&lf][&cr]AS[&lf][&cr]SELECT        A.sys_client_id, A.store_id, A.item_id, A.item_unit, A.batch_no, A.item_qty, A.rsrvd_qty, B.item_type, B.item_group, B.item_subgr, B.item_form, B.item_make, [&lf][&cr]                         B.item_agent, B.u_price, B.u_cost, B.u1_id, B.u_in_u1, B.u_x, A.item_qty * B.u_in_u1 AS item_qty_u1[&lf][&cr]FROM            dbo.es_stock_qty AS A LEFT OUTER JOIN[&lf][&cr]                         dbo.vu_es_item_units AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id AND A.item_unit = B.u_id[&lf][&cr]WHERE        (A.item_qty <> 0)[&lf][&cr][&lf]
0062 ************** 0000 => [&lf][&cr][&lf][&cr]DROP VIEW vu_es_doc_line_items_net_sales[&lf]
0063 ************** 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW vu_es_doc_line_items_net_sales[&lf][&cr]AS[&lf][&cr]SELECT [sys_client_id][&lf][&cr]      ,[doc_type],[doc_no],[doc_subtype],[cov_no],[doc_status],[doc_date],[doc_year][&lf][&cr]      ,[doc_month][&lf][&cr]      ,[doc_crncy][&lf][&cr]      ,[doc_crncy_exrate][&lf][&cr]      ,[store_id][&lf][&cr]      ,[cash_id][&lf][&cr]      ,[region][&lf][&cr]      ,[sales_rep][&lf][&cr]      ,[branch], cc_no, proj_id, actvty_id[&lf][&cr]      ,[crtd_by][&lf][&cr]      ,[item_id][&lf][&cr]      ,[item_type][&lf][&cr]      ,[item_group][&lf][&cr]      ,[item_subgr][&lf][&cr]      ,[item_form][&lf][&cr]      ,[item_make][&lf][&cr]      ,[item_agent][&lf][&cr]      ,[item_unit][&lf][&cr]      ,[batch_no][&lf][&cr]      ,[unit_price][&lf][&cr]      ,[unit_price_cc][&lf][&cr]      ,[item_qty1][&lf][&cr]      ,[item_qty2][&lf][&cr]      ,[item_discount][&lf][&cr]      ,[sub_total][&lf][&cr]      ,[sub_total_cc][&lf][&cr]      ,[item_qty_u1][&lf][&cr]      ,[free_qty_u1][&lf][&cr]      ,[u1_id][&lf][&cr]      ,[profit_cc][&lf][&cr]      ,[from_date][&lf][&cr]      ,[to_date][&lf][&cr]      ,[line_status][&lf][&cr]      , ref_doc_type[&lf][&cr]      , ref_doc_no[&lf][&cr][&lf][&cr]	   ,[item_qty1] AS item_qty1_sold[&lf][&cr]	  ,0 AS item_qty1_ret[&lf][&cr][&lf][&cr]	  ,[item_qty_u1] AS item_qty_u1_sold[&lf][&cr]	  ,0 AS item_qty_u1_ret[&lf][&cr][&lf][&cr]	  ,[sub_total_cc] AS sub_total_cc_sold[&lf][&cr]	  ,0 AS sub_total_cc_ret[&lf][&cr][&lf][&cr]	  ,ISNULL([profit_cc],0) AS profit_cc_sold[&lf][&cr]	  ,0 AS profit_cc_ret[&lf][&cr][&lf][&cr]  FROM [vu_es_doc_line_items][&lf][&cr]  WHERE[&lf][&cr]  doc_type='201' OR doc_type='203'[&lf][&cr]  UNION [&lf][&cr]  SELECT [sys_client_id][&lf][&cr]      ,[doc_type][&lf][&cr]      ,[doc_no][&lf][&cr]      ,[doc_subtype][&lf][&cr]      ,[cov_no][&lf][&cr]      ,[doc_status][&lf][&cr]      ,[doc_date][&lf][&cr]      ,[doc_year][&lf][&cr]      ,[doc_month][&lf][&cr]      ,[doc_crncy][&lf][&cr]      ,[doc_crncy_exrate][&lf][&cr]      ,[store_id][&lf][&cr]      ,[cash_id][&lf][&cr]      ,[region][&lf][&cr]      ,[sales_rep][&lf][&cr]       ,[branch], cc_no, proj_id, actvty_id[&lf][&cr]      ,[crtd_by][&lf][&cr]      ,[item_id][&lf][&cr]      ,[item_type][&lf][&cr]      ,[item_group][&lf][&cr]      ,[item_subgr][&lf][&cr]      ,[item_form][&lf][&cr]      ,[item_make][&lf][&cr]      ,[item_agent][&lf][&cr]      ,[item_unit][&lf][&cr]      ,[batch_no][&lf][&cr]      ,[unit_price][&lf][&cr]      ,[unit_price_cc][&lf][&cr]      ,-1*[item_qty1] AS item_qty1[&lf][&cr]      ,-1*[item_qty2] AS item_qty2[&lf][&cr]      ,[item_discount][&lf][&cr]      ,-1*[sub_total] AS sub_total[&lf][&cr]      ,-1*[sub_total_cc] AS sub_total_cc[&lf][&cr]      ,-1*[item_qty_u1] AS item_qty_u1[&lf][&cr]      ,-1*ISNULL([free_qty_u1],0) AS free_qty_u1[&lf][&cr]      ,[u1_id][&lf][&cr]      ,-1*ISNULL([profit_cc],0) AS profit_cc[&lf][&cr]      ,[from_date][&lf][&cr]      ,[to_date][&lf][&cr]      ,[line_status][&lf][&cr] , ref_doc_type[&lf][&cr]      , ref_doc_no[&lf][&cr]	  ,0 AS item_qty1_sold[&lf][&cr]	  ,[item_qty1] AS item_qty1_ret[&lf][&cr]	  ,0 AS item_qty_u1_sold[&lf][&cr]	  ,[item_qty_u1] AS item_qty_u1_ret[&lf][&cr][&lf][&cr]	  ,0 AS sub_total_cc_sold[&lf][&cr]	  ,[sub_total_cc] AS sub_total_cc_ret[&lf][&cr][&lf][&cr]	  ,0 AS profit_cc_sold[&lf][&cr]	  ,ISNULL([profit_cc],0) AS profit_cc_ret[&lf][&cr]	  [&lf][&cr][&lf][&cr]  FROM [vu_es_doc_line_items][&lf][&cr]  WHERE[&lf][&cr]  doc_type='202'[&lf][&cr][&lf]
0064 ************** 0000 => [&lf][&cr][&lf][&cr]DROP VIEW vu_es_stock_trans[&lf]
0065 ************** 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW vu_es_stock_trans AS[&lf][&cr]SELECT        A.sys_client_id, A.store_id, A.item_id, A.item_unit, A.batch_no, A.doc_type, A.doc_no, A.qty_in, A.qty_out, A.adjust, A.tr_date, A.tr_time, A.u1_id, A.u1_cost, A.u1_ave_cost, [&lf][&cr]                         A.u1_qty_out, A.u1_qty_in, B.cov_no, B.doc_date[&lf][&cr]FROM            es_stock_trans AS A LEFT OUTER JOIN[&lf][&cr]                         dbo.es_sales_docs AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no[&lf]
0066 ************** 0000 => [&lf][&cr][&lf][&cr]DROP VIEW vu_es_stock_trans_price[&lf]
0067 ************** 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW vu_es_stock_trans_price[&lf][&cr]AS[&lf][&cr]SELECT        B.unit_price, B.doc_crncy, A.sys_client_id, A.store_id, A.item_id, A.item_unit, A.batch_no, A.doc_type, A.doc_no, A.qty_in, A.qty_out, A.adjust, A.tr_date, [&lf][&cr]                         A.tr_time, A.u1_id, A.u1_cost,A.u1_ave_cost, A.u1_qty_out, A.u1_qty_in, B.cov_no, B.doc_date, B.doc_year, B.doc_month[&lf][&cr]FROM            dbo.es_stock_trans AS A LEFT OUTER JOIN[&lf][&cr]                         dbo.vu_es_doc_line_items AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no AND A.item_id = B.item_id AND[&lf][&cr]                          A.item_unit = B.item_unit AND A.store_id = B.store_id AND A.line_id = B.line_id AND A.batch_no = ISNULL(B.batch_no, N'0')[&lf]
0068 ************** 0000 => [&lf][&cr][&lf][&cr]ALTER TABLE es_sales_docs ALTER COLUMN phone varchar(20) NULL[&lf]
0069 ************** 0000 => [&lf][&cr][&lf][&cr]ALTER TABLE es_sales_docs ALTER COLUMN address nvarchar(100) NULL[&lf]
0070 ************** 0000 => [&lf][&cr][&lf][&cr]ALTER TABLE es_sales_docs ALTER COLUMN to_date varchar(10) NULL[&lf]
0071 ************** 0000 => [&lf][&cr][&lf][&cr]ALTER TABLE es_sales_docs ALTER COLUMN from_date varchar(10) NULL[&lf]
0072 ************** 0000 => [&lf][&cr][&lf][&cr]ALTER TABLE es_sales_docs ALTER COLUMN warranty nvarchar(100) NULL[&lf]
0073 ************** 0000 => [&lf][&cr][&lf][&cr]DROP VIEW [dbo].[vu_es_stock_qty_pvt_units][&lf]
0074 ************** 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW [dbo].[vu_es_stock_qty_pvt_units][&lf][&cr]AS[&lf][&cr]select sys_client_id, store_id,item_id,batch_no, [1] as u1_qty,[2] as u2_qty,[3] as u3_qty[&lf][&cr]from[&lf][&cr]([&lf][&cr]  select sys_client_id, store_id,item_id,batch_no, item_qty, u_x[&lf][&cr]  from vu_es_stock_stats[&lf][&cr]) t[&lf][&cr]pivot[&lf][&cr]([&lf][&cr]  SUM(item_qty) for u_x in ([1],[2],[3])[&lf][&cr]) pvt[&lf][&cr][&lf]
0075 ************** 0000 => [&lf][&cr][&lf][&cr]DROP VIEW [dbo].[vu_es_stock_qty_pvt_units_details][&lf]
0076 ************** 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW [dbo].[vu_es_stock_qty_pvt_units_details][&lf][&cr]AS[&lf][&cr]SELECT  A.sys_client_id, A.store_id, A.item_id, A.batch_no, A.u1_qty, A.u2_qty, A.u3_qty, B.u1_id, B.u1_price, B.u1_cost, B.u2_id, B.u2_cost, B.u2_price, B.u3_id, [&lf][&cr]                         B.u3_cost, B.u3_price, B.item_group, B.item_name[&lf][&cr]FROM            dbo.vu_es_stock_qty_pvt_units AS A LEFT OUTER JOIN[&lf][&cr]                         dbo.es_items AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id[&lf][&cr][&lf]
0077 ************** 0000 => [&lf][&cr][&lf][&cr]UPDATE A SET A.line_id=B.line_id FROM dbo.es_stock_trans A [&lf][&cr]INNER JOIN  es_doc_details B[&lf][&cr]ON A.line_id IS NULL AND A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no AND A.item_id = B.item_id AND[&lf][&cr]                          A.item_unit = B.item_unit AND A.store_id = B.store_id AND A.batch_no = ISNULL(B.batch_no, N'0')[&lf][&cr][&lf]
0078 ************** 0000 => [&lf][&cr][&lf][&cr]DROP VIEW vu_es_items_cache[&lf]
0079 ************** 0000 => [&lf][&cr][&lf][&cr]CREATE VIEW vu_es_items_cache[&lf][&cr]AS[&lf][&cr]SELECT        A.item_id, A.item_name, A.sci_name, A.item_code, A.for_name, A.item_status, A.item_type, A.batch_type, A.item_group, A.def_purch_unit, A.def_sale_unit, [&lf][&cr]                         A.item_crncy, A.u1_id, A.u1_price, A.u1_cost, A.u1_min_price, A.u2_id, A.u2_price, A.u2_cost, A.u2_min_price, A.u3_id, A.u3_price, A.u3_cost, A.u3_min_price, [&lf][&cr]                         A.item_spec, A.item_tax_pct, A.sys_client_id, A.item_flags, B.item_qty_u1[&lf][&cr]FROM            dbo.es_items AS A LEFT OUTER JOIN[&lf][&cr]                             (SELECT sys_client_id, item_id, u1_id, SUM(item_qty_u1) AS item_qty_u1, SUM(item_qty * u_cost) AS u_cost[&lf][&cr]                                FROM dbo.vu_es_stock_stats[&lf][&cr]                                GROUP BY sys_client_id, item_id, u1_id) AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id[&lf]
0080 20250725142429 0001 => UPDATE hs_config SET cfg_value='Y' WHERE cfg_key='app-last-shutdown-clean'
