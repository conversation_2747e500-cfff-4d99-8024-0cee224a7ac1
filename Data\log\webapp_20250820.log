2025/08/20 21:13:44.629 ; Info ; 1 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/08/20 21:13:44.784 ; <PERSON> ; 1 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/08/20 21:13:44.784 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/20 21:13:44.784 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/20 21:13:44.784 ; Log ; 1 ;  ; 0000: ; 20/8/2025 21:13:44 @Http Req#0
2025/08/20 21:13:44.785 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/20 21:13:44.785 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 648  @Http Req#0
2025/08/20 21:13:44.785 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/20 21:13:44.791 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/20 21:13:44.796 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/20 21:13:44.873 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/20 21:13:44.873 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/20 21:13:44.933 ; Info ; 1 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/08/20 21:13:44.952 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/20 21:13:44.956 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/20 21:13:44.968 ; Trace ; 1 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/20 21:13:44.989 ; Trace ; 1 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/08/20 21:13:44.990 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/20 21:13:44.990 ; Trace ; 1 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/20 21:13:44.994 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/20 21:13:45.004 ; Info ; 1 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/08/20 21:13:45.057 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/20 21:13:45.065 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/20 21:13:45.099 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/20 21:13:45.100 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/20 21:13:45.101 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/20 21:13:45.104 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/20 21:13:45.143 ; Trace ; 1 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/08/20 21:13:45.145 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/20 21:13:45.145 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/20 21:13:45.147 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/20 21:13:45.148 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/20 21:13:45.156 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/20 21:13:45.160 ; Trace ; 1 ;  ; 0000: ; HsStoredObject cached: 0 @Http Req#0
2025/08/20 21:13:45.273 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/20 21:13:45.274 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/20 21:13:45.274 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/20 21:13:45.274 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/20 21:13:45.274 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/20 21:13:45.275 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/20 21:13:45.276 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/20 21:13:45.409 ; Trace ; 9 ; 185.80.143.11 ; 0000: ; Building forms auth roles @Http Req#1
2025/08/20 21:13:45.416 ; Trace ; 9 ; 185.80.143.11 ; 0000: ; Failed to load form:login @Http Req#1
2025/08/20 21:13:45.416 ; Trace ; 9 ; 185.80.143.11 ; 0000: ; Failed to load form:logout @Http Req#1
2025/08/20 21:13:45.442 ; Trace ; 9 ; 185.80.143.11 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/08/20 21:13:45.481 ; Trace ; 9 ; 185.80.143.11 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/08/20 21:13:45.481 ; Trace ; 9 ; 185.80.143.11 ; 0000: ; Failed to load form:relation @Http Req#1
2025/08/20 21:13:45.486 ; Log ; 9 ; 185.80.143.11 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/20 21:13:45.496 ; Trace ; 9 ; 185.80.143.11 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/08/20 21:13:50.277 ; Log ; 7 ;  ; 0000: ; Started threaded queue processor: DelayedSqlQueue @Http Req#1
2025/08/20 21:13:50.277 ; Log ; 8 ;  ; 0000: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#1
2025/08/20 21:13:50.279 ; Log ; 8 ;  ; 0000: ; Loading scheduled tasks.. @Http Req#1
2025/08/20 21:13:54.060 ; Trace ; 9 ; 185.80.143.11 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/08/20 21:13:54.210 ; Trace ; 9 ; 185.80.143.11 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/08/20 21:13:54.684 ; Trace ; 9 ; 185.80.143.11 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/08/20 21:14:05.502 ; Info ; 11 ; 185.80.143.11 ; 9900: ; Init Client: 9900 @Http Req#3 @Req#2 0s
2025/08/20 21:14:05.529 ; Log ; 11 ; 185.80.143.11 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#3 @Req#2 0s
2025/08/20 21:14:05.532 ; Log ; 11 ; 185.80.143.11 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#3 @Req#2 0s
2025/08/20 21:14:05.537 ; Trace ; 11 ; 185.80.143.11 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#3 @Req#2 0s
2025/08/20 21:14:05.550 ; Trace ; 11 ; 185.80.143.11 ; 9900: ; COA.RebuildAccountsTree @Http Req#3 @Req#2 0s
2025/08/20 21:14:05.550 ; Trace ; 11 ; 185.80.143.11 ; 9900: ; Accounts loaded:65 @Http Req#3 @Req#2 0s
2025/08/20 21:14:05.550 ; Trace ; 11 ; 185.80.143.11 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#3 @Req#2 0s
2025/08/20 21:14:05.552 ; Trace ; 11 ; 185.80.143.11 ; 9900: ; Accounts loaded:0 @Http Req#3 @Req#2 0s
2025/08/20 21:14:05.555 ; Trace ; 11 ; 185.80.143.11 ; 9900: ; Caching Sales Items:9900 @Http Req#3 @Req#2 0s
2025/08/20 21:14:05.557 ; Log ; 11 ; 185.80.143.11 ; 9900: ; Caching SalesItem.. @Http Req#3 @Req#2 0s
2025/08/20 21:14:05.579 ; Log ; 11 ; 185.80.143.11 ; 9900: ; Sales Items Cached: 0 @Http Req#3 @Req#2 0s
2025/08/20 21:14:05.579 ; Trace ; 11 ; 185.80.143.11 ; 9900: ; Caching all items stock data.. @Http Req#3 @Req#2 0s
2025/08/20 21:14:05.595 ; Trace ; 11 ; 185.80.143.11 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#3 @Req#2 0s
2025/08/20 21:14:05.600 ; Log ; 11 ; 185.80.143.11 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#3 @Req#2 0s
2025/08/20 21:14:05.600 ; Log ; 11 ; 185.80.143.11 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#3 @Req#2 0s
2025/08/20 21:14:05.605 ; Log ; 11 ; 185.80.143.11 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#3 @Req#2 0s
2025/08/20 21:14:05.605 ; Info ; 11 ; 185.80.143.11 ; 9900: ; Init Client Completed: 9900 @Http Req#3 @Req#2 0s

2025/08/20 21:34:45.473 ; Trace ; 73 ;  ; 9900: ; Session End: User logged out @Http Req#3
2025/08/20 21:34:45.474 ; Log ; 73 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads... @Http Req#3
2025/08/20 21:34:45.474 ; Log ; 7 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue @Http Req#3
2025/08/20 21:34:45.474 ; Log ; 8 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue @Http Req#3
2025/08/20 21:34:45.486 ; Log ; 73 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ******** @Http Req#3

2025/08/20 22:38:37.553 ; Info ; 1 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/08/20 22:38:37.700 ; Trace ; 1 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/08/20 22:38:37.700 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/20 22:38:37.700 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/20 22:38:37.701 ; Log ; 1 ;  ; 0000: ; 20/8/2025 22:38:37 @Http Req#0
2025/08/20 22:38:37.701 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/20 22:38:37.701 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 648  @Http Req#0
2025/08/20 22:38:37.701 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/20 22:38:37.707 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/20 22:38:37.717 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/20 22:38:37.785 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/20 22:38:37.785 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/20 22:38:37.832 ; Info ; 1 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/08/20 22:38:37.861 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/20 22:38:37.867 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/20 22:38:37.878 ; Trace ; 1 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/20 22:38:37.897 ; Trace ; 1 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/08/20 22:38:37.897 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/20 22:38:37.897 ; Trace ; 1 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/20 22:38:37.903 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/20 22:38:37.912 ; Info ; 1 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/08/20 22:38:37.980 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/20 22:38:37.986 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/20 22:38:38.018 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/20 22:38:38.019 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/20 22:38:38.020 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/20 22:38:38.024 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/20 22:38:38.044 ; Trace ; 1 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/08/20 22:38:38.046 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/20 22:38:38.046 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/20 22:38:38.048 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/20 22:38:38.049 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/20 22:38:38.055 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/20 22:38:38.058 ; Trace ; 1 ;  ; 0000: ; HsStoredObject cached: 0 @Http Req#0
2025/08/20 22:38:38.175 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/20 22:38:38.175 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/20 22:38:38.175 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/20 22:38:38.175 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/20 22:38:38.175 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/20 22:38:38.176 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/20 22:38:38.176 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/20 22:38:38.246 ; Trace ; 9 ; ::1 ; 0000: ; Building forms auth roles @Http Req#1
2025/08/20 22:38:38.255 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:login @Http Req#1
2025/08/20 22:38:38.255 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:logout @Http Req#1
2025/08/20 22:38:38.285 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/08/20 22:38:38.332 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/08/20 22:38:38.332 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:relation @Http Req#1
2025/08/20 22:38:38.336 ; Log ; 9 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/20 22:38:38.342 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/08/20 22:38:43.177 ; Log ; 8 ;  ; 0000: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#1
2025/08/20 22:38:43.179 ; Log ; 8 ;  ; 0000: ; Loading scheduled tasks.. @Http Req#1
2025/08/20 22:38:43.177 ; Log ; 7 ;  ; 0000: ; Started threaded queue processor: DelayedSqlQueue @Http Req#1
2025/08/20 22:38:43.197 ; Log ; 8 ;  ; 0000: ; Starting: database backup (20250820_223843_Auto), path=D:\RemoX\Backup\20250820_223843_Auto.bak @Http Req#1
2025/08/20 22:38:45.331 ; Log ; 8 ;  ; 0000: ; Database backup (20250820_223843_Auto) is created, path=D:\RemoX\Backup\20250820_223843_Auto.zip @Http Req#1
2025/08/20 22:38:46.726 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/08/20 22:38:46.833 ; Trace ; 9 ; ::1 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/08/20 22:38:47.193 ; Trace ; 9 ; ::1 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/08/20 22:38:47.204 ; Info ; 14 ; ::1 ; 9900: ; Init Client: 9900 @Http Req#2 @Req#1 0s
2025/08/20 22:38:47.226 ; Log ; 14 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/20 22:38:47.227 ; Log ; 14 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/20 22:38:47.230 ; Trace ; 14 ; ::1 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/20 22:38:47.245 ; Trace ; 14 ; ::1 ; 9900: ; COA.RebuildAccountsTree @Http Req#2 @Req#1 0s
2025/08/20 22:38:47.245 ; Trace ; 14 ; ::1 ; 9900: ; Accounts loaded:65 @Http Req#2 @Req#1 0s
2025/08/20 22:38:47.245 ; Trace ; 14 ; ::1 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/20 22:38:47.247 ; Trace ; 14 ; ::1 ; 9900: ; Accounts loaded:0 @Http Req#2 @Req#1 0s
2025/08/20 22:38:47.250 ; Trace ; 14 ; ::1 ; 9900: ; Caching Sales Items:9900 @Http Req#2 @Req#1 0s
2025/08/20 22:38:47.251 ; Log ; 14 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/20 22:38:47.272 ; Log ; 14 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/20 22:38:47.273 ; Trace ; 14 ; ::1 ; 9900: ; Caching all items stock data.. @Http Req#2 @Req#1 0s
2025/08/20 22:38:47.285 ; Trace ; 14 ; ::1 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#2 @Req#1 0s
2025/08/20 22:38:47.287 ; Log ; 14 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/20 22:38:47.287 ; Log ; 14 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/20 22:38:47.291 ; Log ; 14 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/20 22:38:47.291 ; Info ; 14 ; ::1 ; 9900: ; Init Client Completed: 9900 @Http Req#2 @Req#1 0s
2025/08/20 22:39:34.369 ; Trace ; 5 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Http Req#3 @Req#2 0s
2025/08/20 22:39:34.397 ; Log ; 5 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#3 @Req#2 0s
2025/08/20 22:39:34.397 ; Trace ; 5 ; ::1 ; 9900:admin ; Redirecting user to: /app @Http Req#3 @Req#2 0s
2025/08/20 22:39:34.434 ; Info ; 20 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Http Req#4 @Req#3 0s
2025/08/20 22:39:34.505 ; Info ; 12 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Http Req#5 @Req#4 0s
2025/08/20 22:39:40.098 ; Info ; 22 ; ::1 ; 9900:admin ;  ;  ; /app/?menu ; ::1 @Http Req#6 @Req#5 0s

2025/08/20 22:39:45.699 ; Info ; 13 ; ::1 ; 9900:admin ;  ; sys-menu ; /app/?menu&id=sys-menu&sind=y ; ::1 @Http Req#7 @Req#6 0s
2025/08/20 22:39:48.960 ; Info ; 12 ; ::1 ; 9900:admin ;  ;  ; /app/?about&sind=y ; ::1 @Http Req#8 @Req#7 0s
2025/08/20 22:40:04.129 ; Info ; 10 ; ::1 ; 9900:admin ;  ; sys-menu ; /app/?menu&id=sys-menu&sind=y ; ::1 @Http Req#9 @Req#8 0s
2025/08/20 22:40:12.242 ; Info ; 13 ; ::1 ; 9900:admin ; sys-cfg ;  ; /app/fms/?fm=sys-cfg&sind=y ; ::1 @Req#9 0s
2025/08/20 22:40:41.354 ; Info ; 30 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#10 0s

2025/08/20 22:40:52.354 ; Log ; 22 ; ::1 ; 9900:admin ; Loading help file: D:\RemoX\App\bin\webapp\help\files\remox_sp_accs.xml @Req#11 0s
2025/08/20 22:40:52.370 ; Log ; 22 ; ::1 ; 9900:admin ; Loading help file: D:\RemoX\App\bin\webapp\help\files\remox_im_config.xml @Req#11 0s
2025/08/20 22:40:52.372 ; Log ; 22 ; ::1 ; 9900:admin ; Loading help file: D:\RemoX\App\bin\webapp\help\files\remox_hcm.xml @Req#11 0s
2025/08/20 22:40:52.372 ; Log ; 22 ; ::1 ; 9900:admin ; Loading help file: D:\RemoX\App\bin\webapp\help\files\remox_gl.xml @Req#11 0s
2025/08/20 22:40:52.372 ; Log ; 22 ; ::1 ; 9900:admin ; Loading help file: D:\RemoX\App\bin\webapp\help\files\remox_es_items.xml @Req#11 0s
2025/08/20 22:40:52.372 ; Log ; 22 ; ::1 ; 9900:admin ; Loading help file: D:\RemoX\App\bin\webapp\help\files\remox_es_docs.xml @Req#11 0s
2025/08/20 22:40:52.372 ; Log ; 22 ; ::1 ; 9900:admin ; Loading help file: D:\RemoX\App\bin\webapp\help\files\remox_doc_common.xml @Req#11 0s
2025/08/20 22:40:52.374 ; Log ; 22 ; ::1 ; 9900:admin ; Loading help file: D:\RemoX\App\bin\webapp\help\files\remox_config.xml @Req#11 0s
2025/08/20 22:40:52.375 ; Log ; 22 ; ::1 ; 9900:admin ; Loading help file: D:\RemoX\App\bin\webapp\help\files\hs_core.xml @Req#11 0s
2025/08/20 22:40:52.376 ; Trace ; 22 ; ::1 ; 9900:admin ; System help loaded.. @Req#11 0s
2025/08/20 22:41:01.765 ; Info ; 27 ; ::1 ; 9900:admin ;  ; sys-menu ; /app/?menu&id=sys-menu&sind=y ; ::1 @Req#12 0s
2025/08/20 22:41:07.863 ; Info ; 38 ; ::1 ; 9900:admin ; sys-cfg ;  ; /app/fms/?fm=sys-cfg&sind=y ; ::1 @Req#13 0s

