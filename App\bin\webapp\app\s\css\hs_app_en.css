﻿BODY {
    direction: ltr;  
	text-align: left;
}

#container {
    text-align: left;
}

.header {
    text-align: left;
}

.lst {
    padding-right: 0px;
}



.pp_link, .pp_item {
     background-position: left 4px;
     padding: 0px 2px 0px 20px;
}

.cmd_icon {
        padding: 5px 5px 5px 25px;  
	    background-position: left center;
}

.link_icon, .link_cmd {
    padding: 5px 5px 5px 25px;  
	background-position: 3px center;
}

.rep_tab a.link_icon {
    padding-top: 0px;
    padding-bottom: 0px;
}

/*
.link_icon:hover, .link_cmd:hover {
        padding: 5px 10px 5px 20px;
    }
*/



.has-icon {
       
        padding-left: 25px;
	    background-position: left center;
}

input.has-icon, span.has-icon {
    padding-left: 5px;
    padding-right: 16px;
    background-position: right center;
}




.qv_show_id {
    margin-right: 10px;
}


div.doc-inline-item a.minus {
    left: initial;
    right: 0;
}


.tree a.l1 {
        margin-left: 1px;
        margin-right: 0;
    }

    .tree a.l2 {
        margin-left: 20px;
        margin-right: 0;
    }

    .tree a.l3 {
        margin-left: 40px;
        margin-right: 0;
    }

    .tree a.l4 {
        margin-left: 60px;
        margin-right: 0;
    }

    .tree a.l5 {
        margin-left: 80px;
        margin-right: 0;
    }

     .tree a.l6 {
        margin-left: 100px;
        margin-right: 0;
    }

    .tree a.l7 {
        margin-left: 120px;
        margin-right: 0;
    }

    .tree a.l8 {
        margin-left: 140px;
        margin-right: 0;
    }


     div.tree-sub-item {
   
    padding-right: 0;
    
}

.popup-box DIV.sub-menu-box {
    background-position: right center !important;
}

DIV.sub-menu-box:hover SPAN.popup-sub-menu {
    left: 25%;
    left: 50%;
}

DIV.sub-menu-box.flip:hover SPAN.popup-sub-menu {
    left: initial !important;
    right: 95%;
}

.inline-menu-container DIV.popup-box.sub-menu-box span.popup-sub-menu {
    left: 80px;
    left: 50%;
}

.sub-menu-box.clkd > SPAN.popup-sub-menu {
    /*left: 50% !important;*/
}

SPAN.popup-menu A, SPAN.popup-menu b.title, popup-box DIV.sub-menu-box {
    padding: 5px 5px 5px 25px;
    background-position: left center;
}

/*sticky support*/

@media screen {
    table.rep_tab th:first-child,
    table.rep_tab td:first-child {
        border-left: 1px solid #ccc !important;
    }
}