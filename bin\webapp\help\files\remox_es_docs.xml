﻿<?xml version="1.0" encoding="utf-8"?>
<SystemHelp xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
 <items>

   <Entry>
     <id>es-sale-frd</id>
     <title>فاتورة المبيعات</title>
     <content>
       <![CDATA[
       من خلال فاتورة المبيعات 
-	يمكن بيع الأصناف المخزنية أو الخدمات.
-	يمكن البيع نقدي أو آجل، ويمكن أيضا التعامل بالشيكات البنكية أو شبكات الدفع كشركات الصرافة أو شركات التامين، ويمكن لاحقا ربط عدة سندات قبض على نفس الفاتورة، أو عمل عدة تخفيضات أو عمولات على نفس الفاتورة.
-	يمكن البيع بالتجزئة أو الجملة (حيث يمكن تحديد نوع الفاتورة و على ضوء ذلك يتم اليا تحديد اسعار الاصناف بالجملة أو التجزئة ).
- يمكن البيع بأي عملة موجودة في النظام، وبغض النظر عن عملة تسعير الأصناف، أو عملة المخزون
-	يمكن الصرف من عدة مخازن في نفس الفاتورة
-	يمكن تقييد مبلغ إضافي على العميل كأعباء، مثل النقل أو التحميل وخلافه.
-	يمكن تقييد أعباء إضافية على المبيعات  لعدة حسابات في نفس الفاتورة، مثلا الصندوق عند دفع مبلغ نقدا من الصندوق مقابل تحميل البضاعة. أو الموردين مثلا عند قيام أحد الموردين (مقدمي خدمات النقل) بعملية نقل البضاعة مثلا.. مع إمكانية تقييد هذه الأعباء على العميل أو أي حساب آخر
-	يمكن التعامل مع المناطق التجارية للحصول على إحصاءات ومتابعة المبيعات.
-	يمكن التعامل مع المندوبين، وكذلك تحديد عمولة المندوب على مستوى الفاتورة، يدويا أو آليا من خلال حساب نسبة المندوب.
-	يمكن متابعة الأقساط على فاتورة المبيعات.
-	يمكن تحديد تخفيض على مستوى الصنف أو الفاتورة أو كلاهما – و يمكن تعطيل ميزة التخفيض تماما أوحصرها على بعض المستخدمين فقط. 
-	يمكن إعداد النظام  لإظهار رصيد العميل آليا بمجرد إختياره في الفاتورة.
-	يمكن إعداد النظام لإظهار (أعلى/أقل/آخر سعر) بيع الصنف للعميل عند إختيار الصنف في الفاتورة.
-	يمكن إعداد النظام لإظهار الكميات المتوفرة من الصنف في المخازن بمجرد إختيار الصنف في الفاتورة.
-	يمكن معرفة هامش الربح الناتج عن الفاتورة.
-	يمكن إعداد النظام لرفض البيع بأقل من سعر التكلفة ، أو يمكن السماح بذلك لأصناف معينه لغرض التصفية، مع تنبيه المستخدم، أو حصر هذه الصلاحية لمستخدم معين.
-	تنبيه المستخدم عندما تكون نسبة هامش الربح من بيع الصنف أقل من المحددة في بيانات الصنف.


       ]]>
     </content>
   </Entry>


   <Entry>
     <id>es-stock-adjust</id>
     <title>مستند تسوية كلفة المخزون</title>
     <content>
       <![CDATA[
يستخدم هذا المستند لتغيير كلفة الأصناف، وبالتالي سوف يؤثر على إجمالي كلفة المخزون. و يجب الإنتباه أن هذا المستند لا يؤثر على سعر البيع للصنف (فقط سعرالكلفة ).

يمكن إستخدام هذا المستند لغرض تصحيح الخطأ في إدخال كلفة الصنف عند إدخال بيانات الصنف أو المستندات، و أيضا لغرض تعديل كلفة الصنف بالزيادة أو النقصان نتيجة تغير الأسعار،  حيث يمكن عمل هذه التسوية في أي وقت خلال الفترة المالية.

حول هذا المستند:
- هذا المستند يكون دوما بعملة المخزون
- لا يمكن إلغاء ترحيل هذا المستند، وإذا كان هناك خطأ في المستند فيجب عمل مستند تسوية آخر وتصحيح الخطأ.
- هذا المستند يتطلب الموافقة قبل الترحيل.
- هذا المستند يتطلب الترحيل اليدوي عن طريق المستخدم، ولا يمكن ترحيله بشكل آلي .
- يجب دوما استخدام الوحدة الرئيسية (الأكبر) للصنف - سيرفض النظام ترحيل الصنف عند إستخدام الوحدات الأصغر.
- لا يمكن تكرار الصنف على مستوى المستند.
- لا يمكن تسوية صنف ليس لدية كمية ، و بالنسبة لتغيير كلفة الصنف فسوف يتم تغييرها عند أول مخزون إفتتاحي، مشتروات، أو توريد مخزني
- يجب أن يتم إدخال كلفة تختلف عن الكلفة الحالية - في حال إدخال نفس الكلفة لن يكون هناك تسوية


التأثيرات:
- يؤثر هذا المستند على نظام المخزون بالكلفة فقط (و لايؤثر على الكميات).
- يقوم بعملية صرف بكامل الكمية الموجودة من الصنف بالكلفة السابقة، ثم يقوم بعملية توريد لكامل الكمية بالكلفة الجديدة.
- يتم إضافة بيان في سجل التغييرات الخاص بالصنف المتأثر ويوضح فيه عملية تغيير الكلفة مع رقم المستند والكلفة السابقة والجديدة.
- ماليا: يؤثر هذا المستند على حسابات المخزون السلعي (بالزيادة عند رفع الكلفة، والنقصان عند تخفيض الكلفة) ويوثر على الحساب المحدد في حقل حساب التسوية، حيث سيكون هذا الحساب دائنا للمخزون في حال الزيادة للمخزون ومدينا للمخزون حال نقص المخزون.

ويجب الإنتباه أنه إذا كان الحساب المحدد في حقل التسوية هو حساب أرباح وخسائر فهذا سيؤدي إلى تحقيق ربح في حال الزيادة في الأسعار أو خسارة في حالة النقص - مما يؤثر على تقرير الأرباح والخسائر للفترة.
لذا دوما يفضل إستشارة المحاسب  قبل تنفيذ هذا الإجراء

]]>
     </content>
   </Entry>


   


   
 

<!--   
   <Entry>
     <id></id>
     <title></title>
     <content>
       <![CDATA[

]]>
     </content>
   </Entry>
-->


  
 </items>
</SystemHelp>

