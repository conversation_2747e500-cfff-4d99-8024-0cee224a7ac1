/* Should not published, but created in client machine - and never overwritten by system updates */

/*
bank colors: dark to ligh    
  
    // green
    #00664F  
    #007B6D
    #61A296
    #8CBAB0

    #A29061
    #B2A37A
    #CABFA3
    #DAD3BD

    Gold
    #E8CEAA
    #F5E5BF
    #F7E7C0
    #F9ECD3

    */


/* Sabafrican theme  */

/*

body, #page {
}

.inline-menu a {
    font-size: 11pt !important;
}

.popup-menu a {
    font-size: 11pt !important;
}


.page_header, .page_footer {
    background-color: #a29061;
}

.page_header {
    border-bottom-color: #E8CEAA;
}


.page_header_pg {
}

.fm-title {
    border-color: #DAD3BD;
    color: #000;
}


.ui-widget-header {
    border: 1px solid #00664F;
    background: #00664F !important;
    color: #fff;
    font-weight: bold;
}

.ui-state-active a, .ui-state-active a:link, .ui-state-active a:visited {
    color: #00664F;
}

.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active {
    border-color: #00664F;
}

.inline-cmd {
    background: #00664F !important;
    box-shadow: 0 8px 16px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19);
}


.fm-ui-plain-tab {
    background: #f0fff8 !important;
}

.fm-ui-plain-title {
    color: #00664F;
    background-color: #ced;
}

.ui-datepicker-month, .ui-datepicker-year {
    color: #fff !important;
}



@media screen and (max-width: 700px) {

 


    .in-dlg, .pg-inet-bank-svcs, .pg-pay-req, .pg-net-login-fm, .pg-net-login-page {
        background-color: #fff !important;
    }



    input.flds, .lst, .select-menu, input[type='text'] {
        background-color: #F4FAFA;
        height: 50px !important;
        padding-top: 11px !important;
        padding-bottom: 11px !important;
        border-radius: 8px;
        border-color: transparent !important;
        border-left-width: 1em;
        border-right-width: 1em;
    }

        td.val input[type='text']:focus, input[type='text']:focus, .focus, input:focus {
            background-color: #F4FAFA !important;
           
        }

    .fld-tools {
        border-color: transparent !important;
        width: 100% !important;
        max-width:90vw !important;
        background-color: #F4FAFA !important;
        display: block !important;
    }

        .fld-tools input {
            width:80% !important;
        }

.dlg_box {
    border-color:transparent;
    border: none;
    padding: 0;
    margin: 0;

}

    .dlg_box {
        position: fixed;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%);
        -webkit-transform: translate(-50%, -50%);
        -ms-transform: translate(-50%, -50%);
        -moz-transform: translate(-50%, -50%);
        max-width: 90vw;
        max-height: 90vh;
        border-radius: 12px;
    }


    .dlg_box .head {
        background-image: none;
        border: none;
        
    }

    #dlg_title {
        display: block;
        color: #017B80;
        text-align: center;
        border-bottom: 1px solid #ccc;
        padding-bottom:1em;
        font-size:1.1em;
        z-index:1020;

        position:absolute;
        top:1.5em;
        left:2em;
        right:2em;
       
        
    }

    .dlg_box .head .icon_reopen-in-new-tab {
        display: none;
    }



    #dlg_frame {
        border:none;
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: calc(100% - 60px) !important;
        padding:0;
        margin: 0;
        padding-top: 1em;
        
    }

    .in-dlg .inline-cmd {

    }

    .pg-net-login-page {
        box-shadow: none;
    }




}

*/

    /* End: SabaAfrican theme */






    /*Media screen*/

/*
    Aqwat theme

    colors:
     bg #773897 - crimson
      #EC008C - pink

     #F2FBFE - very light blue

    // sent by Rashad (not used)
e1b1ff
fe95eb

  */



@font-face {
    font-family: almarai;
    src: url('/client/aqwat/fonts/Almarai-Regular.ttf') format('woff');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: almarai;
    src: url('/client/aqwat/fonts/Almarai-Bold.ttf') format('woff');
    font-weight: bold;
    font-style: normal;
}

* {
    font-family: almarai, droid_kufi, Tahoma, Arial, sans-serif;
    font-size: 9pt;
    color: #444;
    box-sizing: border-box;
    line-height: 20px;
}

.ss-fm div.fm-controls {
    background-color: transparent;
    padding: 0;
    margin: 0;
}


:root {
    /*Aqwat*/
    --hs-prmry: #F6BF29;
    --hs-prmry-10: #F6BF2910;
    --hs-prmry-20: #F6BF2920;
    --hs-prmry-30: #F6BF2930;
    --hs-prmry-40: #F6BF2940;
    --hs-prmry-50: #F6BF2950;
    --hs-prmry-80: #F6BF2980;
    --hs-prmry-A0: #F6BF29A0;
    --hs-prmry-C0: #F6BF29C0;
    --hs-prmry-bg: #F6BF29;
    /**/
    --hs-prmry-1: #fef8e7;
    --hs-prmry-2: #fce9b6;
    --hs-prmry-3: #fada85;
    --hs-prmry-4: #f8cc54;
    --hs-prmry-5: #f6bd23;
    --hs-prmry-6: #dca409;
    --hs-prmry-7: #ab7f07;
    --hs-prmry-8: #7a5b05;
    --hs-prmry-9: #493703;
    /**/
    --hs-prmry-dark1: #c59400;
    --hs-prmry-dark2: #956c00;
    --hs-prmry-dark3: #694600;
    --hs-prmry-dark4: #452100;
    /**/
    --hs-scndry: #345E3D;
    --hs-scndry-10: #345E3D10;
    --hs-scndry-20: #345E3D20;
    --hs-scndry-30: #345E3D30;
    --hs-scndry-40: #345E3D40;
    --hs-scndry-50: #345E3D50;
    --hs-scndry-80: #345E3D80;
    --hs-scndry-aa: #345E3DAA;
    --hs-scndry-cc: #345E3DCC;
    --hs-scndry-bg: #345E3D05;
}




/*
.ss-item-img img.wide {
    width: 100%;
    object-fit: cover;
}
*/

@media screen and (max-width: 700px) {





    body {
        background-image: none;
        padding-left: 0;
        padding-right: 0;
        background-color: #fafafa !important;
        margin: 0 !important;
        padding: 0 !important;
        max-width: 100vw !important;
        height: 100vh;
        /*
        xbackground-image: linear-gradient(to left bottom,#fff 30%,var(--hs-prmry-30) 80%, var(--hs-prmry-50)) !important;
        xbackground-image: linear-gradient(to left bottom, var(--hs-prmry) 5%, #fff 80%, var(--hs-prmry)) !important;
        xbackground-color: antiquewhite !important;
        box-shadow: 0 150px 15px 500px #F6BF2905 inset;
        xbackground: #F6BF2910 !important;
            */
    }

  


    .dlg_box {
        border: none;
        outline: 0;
        border-radius: 0;
        
    }

        .dlg_box .head {
            background-image: none !important;
            background-color: #345E3D10 !important;
            border: none;
            /**/
            position: relative;
            
        }

            .dlg_box .head a {
                visibility: hidden;
            }

                .dlg_box .head a.icon_closex {
                    visibility: visible;
                    color: #555;
                    cursor: pointer;
                    width: 40px;
                    text-align: center;
                    display: inline-block;
                    font-weight: bold;
                    font-size: 18px;
                    border-radius: 0 3px 3px 0;
                    user-select: none;
                    /**/
                    
                    font-size:1.5rem;
                    width:3rem;
                    height:3rem;
                    display:flex;
                    justify-content:center;
                    align-items:center;
                    position:fixed;
                    top:5px;
                    right:5px;
                    z-index:3000;
                    border-radius:50%;
                    background-color:var(--hs-prmry);
                }


    #dlg_frame {
        position:fixed;
        top:0;left:0;
        height:100vh !important;
        width:100vw !important;
        
    }



    a {
        color: var(--hs-scndry);
        color: var(--hs-prmry);
    }


    hr.line {
        border-top: solid 1px #ddd !important;
    }

    .inline-cmd {
        border-radius: 1em !important;
    }

    .inline-cmd, a.inline_title.tree-i:hover, .ui-tb.box-at-cont-bottom a {
        background-color: var(--hs-scndry) !important;
        color: white !important;
    }

   
    

    a.inline_title.tree-i {
        background-color: transparent !important;
        color: #000 !important;
        background-color: rgba(250,250,250,0.5) !important;
    }


    .ss-fm .inline_title, .inline_title, .fm-title, .popup-title {
        line-height: 20px;
        margin: 0 auto;
        text-align: initial;
        /**/
        background-color: transparent !important;
        color: var(--hs-scndry) !important;
        border: 1px solid rgba(52, 94, 61, 0.1);
        border-left-width: 0;
        border-right-width: 0;
        border-radius: 0;
        font-size: 1.1rem;
    }

        .ss-fm .inline_title a, .fm-inline-tab-title {
            color: var(--hs-scndry) !important;
        }

    .fm-inline-tab-title {
        font-size: 1.3rem;
        background-color: transparent !important;
        xmargin: 5px;
        padding: 5px;
        text-align: initial;
        margin: 0.5rem;
        margin-top: 0;
        margin-bottom: 0;
        /**/
      
        
    }

        


    table.rep_tab tr.head th, table.rep_tab thead tr.head th {
        background-color: rgba(52, 94, 61, 0.1) !important;
        color: var(--hs-scndry) !important;
    }



.desc {
    margin:1rem;
    color:#888;
    text-align:justify;
    line-height:1.8rem;
}


    div.tit-val {
        border: none;
    }

    DIV.content-box {
        margin: 0.25em 1em 2em 1em !important;
        border-radius: 16px;
        box-shadow: var(--light-shadow);
        xbackground-color: #fff !important;
        xborder: 1px solid #eee;
        xbox-shadow: -1px 1px 2px rgba(0,0,0,0.15), 2px 0px 4px rgba(0,0,0,.2) !important;
        box-shadow: none !important;
        xborder: 1px solid rgba(0,0,0,0.1) !important;
        padding: 0;
    }

        DIV.content-box.h-scrol {
            margin-top: 0 !important;
            margin-bottom: 0 !important;
        }

        div.tit {
            color: var(--hs-prmry);
            font-weight: normal;
            margin-top: 0;
            margin-bottom: 0;
            line-height: 0.8em;
        }


    DIV.content-box B.title {
        line-height: 30px;
        background-color: rgba(52, 94, 61, 0.1) !important;
        color: var(--hs-prmry) !important;
        background-color: transparent !important;
        background-color: rgba(0,0,0,0.02) !important;
    }

    .popup-title {
        background-color: #ddd;
        color: #666;
    }

    .popup-box:hover DIV.popup-content, .popup-box DIV.popup-content-no-hover {
        background-color: #fff;
        color: #666;
    }

    DIV.content-box DIV.content {
        background-color: transparent;
    }

  




    #bskt-itm-cnt, .ss-item-basic .hl {
        background-color: var(--hs-prmry) !important;
        color: #fff;
    }
        
    


   

    .ss-item-data {
        line-height: 2em;
    }

        .ss-item-data b {
            color: var(--hs-scndry);
            font-size: 1.2em;
        }

        .ss-item-data .ss-price {
            font-size: 30px;
            color: var(--hs-scndry);
            float: left;
        }

    .link_icon.ui-cmd {
        padding-top: 3px;
        padding-bottom: 3px;
    }

    .ss-fm .fm-ui-plain-tab {
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.16), 0px 2px 10px 0px rgba(0, 0, 0, 0.12);
    }


  


    .toast-box {
        bottom: 50%;
       
    }

      

    .side-panel {
        background-color: #f0f4f8 !important;
    }

    input.flds, .lst, .select-menu, input[type='text'], .icon-text-input-div input {
        height: 40px !important;
        padding-top: 6px !important;
        padding-bottom: 6px !important;
        border-radius: 25px;
        border-color: transparent !important;
        padding: 6px 2em;
        border: none;
        outline: 0;
        color: #888;
    }

        td.val input[type='text']:focus, input[type='text']:focus, .focus, input:focus, .fld-tools, input.flds, .lst, .select-menu, input[type='text'], .icon-text-input-div {
            background-color: #f0f2f4 !important;
        }

    .fld-tools, .icon-text-input-div {
        border-color: transparent !important;
        width: 100% !important;
        max-width: 90vw !important;
        display: block !important;
        border-radius: 25px;
        padding: 0 !important;
        position: relative;
    }

        .fld-tools input, .icon-text-input-div input {
            width: 80% !important;
            margin: 0;
            padding-left: 50px !important;
        }

        .icon-text-input-div a {
            position: absolute;
            margin: 0 !important;
            left: 0.5em;
            top: 8px;
            color: rgba(0, 0, 0, 0.25) !important;
            border-radius: 50%;
        }

    xtr#r_inp_text .icon-text-input-div input {
        padding: 0 2rem !important;
        margin: 0 !important;
        xheight: 24px !important;
    }

    xtr#r_inp_text .icon-text-input-div a {
        top: 2px;
        left: 5px;
    }

    .side-panel input[type='text'] {
        height: 30px !important;
        border-radius: 8px !important;
        padding: 2px !important;
        border-width: 1px !important;
        background-color: #fff !important;
    }

    input:read-only, select:disabled, textarea:read-only {
        background-color: #345E3D03 !important;
        font-weight: bold !important;
        font-size: 1.2em !important;
        color: var(--hs-scndry) !important;
        padding-top: 2px !important;
        padding-bottom: 2px !important;
        border: none;
        margin-bottom: 0;
        text-align: right;
    }


    /*.ss-fm .inline_title*/

    .inline_title .mir-flt a, .fm-inline-tab-title .mir-flt a {
        color: var(--hs-prmry) !important;
        font-size: 0.8em ;
        font-weight: normal !important;
    }

    .ss-fm .ui-inline-box, .ui-cont.ui-cont-box {
        margin-top: 5px;
        margin-bottom: 10px;
        padding: 5px 10px;
        background-color: transparent !important;
        border-radius: 0;
        box-shadow: none;
        border: none;
    }


    .ui-cont.ui-cont-box {
        padding-bottom: 3rem;
        border-bottom: 1px solid #eee;
        background-color: #fff !important;
        margin: 2rem 1rem !important;
        border-radius: 4px;
        border: none;
        box-shadow: rgba(0, 0, 0, 0.1) -4px 9px 25px -6px;
    }

    .ui-cont-box h2 {
        background-color: var(--hs-scndry-bg);
        color: var(--hs-scndry);
        background-color: transparent;
        border: none;
    }





    .ss-item-img.wide {
        margin: -3px;
        padding: 0;
        border: none;
        max-height: 50vh !important;
        max-height: min(400px,50vh) !important;
        overflow: scroll;
        border-radius: 0 0 0 15%;
        border-radius: 0% 100% 0% 100% / 80% 1% 99% 20%;
        border-radius: 0% 100% 0% 100% / 88% 1% 99% 12%;
    }


        .ss-item-img.wide img {
            margin: 0;
            padding: 0;
            border: none;
            width: 100%;
            object-fit: cover;
            xborder-radius: 0 0 0 15%;
            
        }


  

      
        
  
}



.toast-box, .toast-box a, .toast-box .kv .k, .toast-box .kv .v {
    xbackground-color: #D9F6FF !important;
    xcolor: #2894BF !important;
    background-color: #ffffaa !important;
    color: #666 !important;
    background-color: #FCECBE !important;
}




/*item-basic data*/
.ss-item-basic {
    margin: 5px !important;
    border-radius: 8px;
    /*flex*/
    display: flex;
    flex-direction: row;
    border: 1px solid #ddd;
    grid-gap: 0.5rem;
}



    .ss-item-basic div.image {
        height: 100px;
        width: 100px;
        overflow: hidden;
        margin: 1px 5px;
        /*circle icon*/
        border-radius: 50%;
        background-color: transparent;
    }


/* rect icon*/
div.rect-icons .ss-item-basic div.image {
    border-radius: 16px;
    background-color: transparent;
}

.ss-item-basic div.image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.ss-item-basic div.text b, .ss-item-basic div.text {
    font-weight: normal !important;
    font-size: 0.9rem !important;
}

    .ss-item-basic div.text .md-18 {
        font-size:14px;
    }

    

    

/*h-scrol items*/
div.h-scrol .ss-item-basic {
    display: inline-block !important;
    border: none;
}

    div.h-scrol .ss-item-basic div.image {
        margin: auto;
        overflow: hidden;
    }

div.ss-item-basic.usr {
    padding: .5rem !important;
    margin: 1rem .5rem !important;
    border: 1px solid #eee !important;
    background: #fff !important;
    box-shadow: 4px 4px 4px 0 #fff8, 1px 1px 18px rgba(0,0,0,.02), 0 0 15px #fff inset;
}

   

/*grid*/

div.ui-grid {
    display:flex;
  
    flex-wrap:wrap;
    justify-content:center;
    flex-grow: 1;
}

    div.ui-grid hr {
        display:none;
    }

    /*grid/icon sizes*/
    div.ui-grid .ss-item-basic div.text .mdi-18 {
        font-size: 1rem !important;
    }

    div.ui-grid .ss-item-basic.usr {
        display: inline-block !important;
        border: none;
        width: 120px !important;
        margin: .5rem .25rem !important;
        padding: .5rem .25rem !important;
        /**/
        width: 150px !important;
        margin: .5rem .25rem !important;
        padding: .5rem .25rem !important;
        margin: .5rem !important;
        /*
        border: none !important;
        background: transparent !important;
        box-shadow: none !important;
            */
    }


    div.ui-grid .ss-item-basic div.image {
        margin: auto;
        overflow: hidden;
        display: block !important;
    }

        div.ui-grid .ss-item-basic.usr div.image {
            width:100px;
            height:100px;
        }
        /*
div.ui-grid.sm-icon .ss-item-basic {
    width: 100px !important;
}

div.ui-grid.md-icon .ss-item-basic {
    width: 100px !important;
}

div.ui-grid.lg-icon .ss-item-basic {
    width: 100px !important;
}

div.ui-grid.lgx-icon .ss-item-basic {
    width: 150px !important;
}
  */
        /*icons*/
        

div.sm-icon .ss-item-basic div.image {
    height: 50px;
    width: 50px;
}




div.md-icon .ss-item-basic div.image {
    height: 70px;
    width: 70px;
}



div.lg-icon .ss-item-basic div.image {
    height: 100px;
    width: 100px;
}




div.lgx-icon .ss-item-basic div.image {
    height: 150px;
    width: 150px;
}

/**/









div.ui-grid.md-icon .ss-item-basic .rate-val {
    font-size: 9pt;
    width: 16px;
    border-radius: 4px;
    line-height: inherit;
    margin: 0;
    background-color: transparent;
    padding: 0;
}


/*Toolbar*/

.ss-tb {
    background-color: #f0f0f0;
    text-align: center;
    padding: .6rem;
    box-shadow: inset 0 0 0 #eee, 0 0 4rem #aaa;
    height: 50px !important;
}

    .ss-tb .mdi {
        color: #b6b7b7 !important;
        margin: auto 1rem;
        display: inline-block;
        /**/
        position: relative;
        
    }

        .ss-tb .mdi::after {
            content: attr(data-title);
            position: absolute;
            bottom: -1.5rem;
            left: -6px;
            right: -6px;
            font-size: .7rem;
            color: #777;
        }

        .ss-tb .mdi:hover, .ss-tb .mdi:hover::after {
            color: var(--hs-prmry) !important;
        }


    .ss-tb a:nth-child(4) {
        margin-right:50px;
        
    }

    .ss-tb a:nth-child(2) {
        margin-left: 50px;
        
    }

    .ss-tb span.home {
        display: inline-block;
        position: absolute;
        right: calc(50% - 50px);
        left: calc(50% + 50px);
        
        z-index: 800;
        background-color: transparent;
     
        top:-32px;
       
  
        width: 100px;
       
        xborder:1px solid red;
        user-select:none;
    }



    .ss-tb a.home {
        display: inline-block;
        border-radius: 50%;
        overflow: hidden;
        color: var(--hs-scndry) !important;
        background-color: var(--hs-prmry-bg);
        font-size: 32px;
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.16), 0px 2px 10px 0px rgba(0, 0, 0, 0.12);
        xbox-shadow: 0px 27px 42px 19px rgba(0,0,0,0.1);
        xpadding: 1rem;
        xline-height:32px;
        xtext-align:center;
        /**/
        border: 5px solid #fafafa;
        /**/

        font-size:2.5rem;
        width:5rem;
        height:5rem;
        aspect-ratio: 1/ 1;
        display: grid;
        align-content:center;
        margin:auto;
    


    }



        div.ss-itm-qty .mdi {
            border: 1px solid var(--hs-prmry-bg);
            border-radius: 50%;
            background-color: var(--hs-prmry-bg) !important;
            color: var(--hs-scndry) !important;
        }

div.ss-itm-qty .qty {
    font-weight:bold;
    font-size:2rem;
    width:5rem;
    display:inline-block;
    text-align:center;
}

br {
    display: block !important;
    line-height: 0.5rem !important;
    background-color: red;
    border: 1px solid red;
    line-height: 0 !important;
}


.bskt-icon a.mdi {
    color: var(--hs-scndry) !important;
    font-size: 20px !important;
}




.info-msg, .warn-msg, .ui-warn, .ui-info, .err-msg, .toast-box, .fm-top-cont {
    background-color: #FFFBD6 !important;
}


.msg-box, .msg-fixed, #wait-status-box {
    background-color: #FFF !important;
    border-color: #fff;
    box-shadow: 0 8px 16px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19), 0 0 100px 50rem #0002 !important;
}


a.link_cmd.icon_yes, a.link_cmd.icon_no {
    background-color: var(--hs-scndry);
    color: #fff;
    border-radius: 8px;
    box-shadow: var(--light-shadow);
    background-image: none !important;
}

a.link_cmd.icon_no {
    background-color: rgba(0,0,0,.2);
    color: #333;
}


/***** End: Aquat theme ************/


/*Transition Test*/

.link_icon_only {
    xbackground-color: transparent !important;
    xborder-color: transparent !important;
    transition: transform 1000ms ease, border-radius 1000ms ease;
    xtransform: rotate(315deg);
}

    .link_icon_only:not(.disabled):hover {
        
        xtransform: rotate(360deg);
        xxborder-radius: 50%;
        
        /**/
        -webkit-transform-origin: top center;
        -ms-transform-origin: top center;
        transform-origin: top center;
        -webkit-animation-name: swing;
        animation-name: swing;
        -webkit-animation-duration: 1s;
        animation-duration: 1s;
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
    }



.swing {
    -webkit-transform-origin: top center;
    -ms-transform-origin: top center;
    transform-origin: top center;
    -webkit-animation-name: swing;
    animation-name: swing;
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
}


@keyframes swing {
    20% {
        -webkit-transform: rotate3d(0, 0, 1, 15deg);
        transform: rotate3d(0, 0, 1, 15deg);
    }

    40% {
        -webkit-transform: rotate3d(0, 0, 1, -10deg);
        transform: rotate3d(0, 0, 1, -10deg);
    }

    60% {
        -webkit-transform: rotate3d(0, 0, 1, 5deg);
        transform: rotate3d(0, 0, 1, 5deg);
    }

    80% {
        -webkit-transform: rotate3d(0, 0, 1, -5deg);
        transform: rotate3d(0, 0, 1, -5deg);
    }

    100% {
        -webkit-transform: rotate3d(0, 0, 1, 0deg);
        transform: rotate3d(0, 0, 1, 0deg);
    }
}





.sys-4040 {
    --hs-prmry: #F6BF29;
    --hs-prmry-bg: #f3e2c5 !important;
    --hs-scndry: #88BD6C;
    --hs-scndry-bg: #C2B49A;
}


div.sys-4040x .ss-tb a.home {
    color: red !important;
    background-color: #F6BF29;
}

/*Grid Menu*/

.grid-menu-box.l0, .grid-menu-box.l1 {
    position: relative;
    padding-bottom: 1rem !important;
}

.grid-menu-box div {
    position:relative;
    transition: .5s;
    
}


.grid-menu-box .active div.text {
    background: #F6BF29;
    transform: translateY(1rem);
    border-radius: 4px;
}


.grid-menu-box div.image {
    border: 2px solid transparent;
    outline: 1px solid #eee;
    box-shadow: -3px -3px 3px 0px #fff, 5px 5px 5px rgba(0,0,0,.05), 1px 2px 2px rgba(0,0,0,.07);
    
}


.grid-menu-box .active .image {
    animation: beat 1s ease-in-out 1;
    xbox-shadow: 0 5px 15px 2px var(--hs-prmry);
    box-shadow: 0 0 5px 5px #fff, 0 10px 15px 1px var(--hs-prmry);
}



    @keyframes beat {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.5);
        }
    }



.grid-menu-box.l1 {
    xbackground: linear-gradient(to left bottom, var(--hs-prmry-10),#fff 50%, var(--hs-prmry-20) );
    /**/
    xborder: 1px solid var(--hs-prmry);
    border-top: 1px solid #ddd;
    xborder-radius: 1rem;
    xbox-shadow: 0px 2px 4px 0px var(--hs-prmry-10), 0px 2px 10px 0px var(--hs-prmry-20) !important;
    xpadding: 1rem !important;
    box-shadow: 0 10px 10px rgba(0,0,0,.2);
    /**/
    xbox-shadow: 0 30px 30px rgba(0,0,0,0.5) !important;
    xbackground: linear-gradient(0deg, #ddd, #fff);
    xbox-shadow: 0 2px 5px 0 rgba(0,0,0,0.3) !important;
    xbox-shadow: 0 0 2px #aaa !important;
    xwidth: 100vw !important;
    xmargin: 0 !important;
    xborder: none;
}

#ss_content {
    transition: opacity 2s;
    position:relative;
}

  

    #ss_content.blur {
        xfilter: blur(1px);
        opacity: 0.5;
    }

    #ss_content.loading {
        background-image:none;
    }

    #ss_content.loading::before {
        content: url(/app/s/images/icons/loading_.gif);
        position: absolute;
        top: 0;
        left: calc(50% - 20px);
        height: 40px;
        width: 40px;
        background: #fff8 ;
        background:var(--hs-prmry-A0);
        border-radius: 1rem;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        box-shadow: 0 0 1rem 1em #0001;
    } 


    a.red, span.red {
        color:red;
        background-color:transparent;
    }

    .gray {
        color:gray;
    }


/*theme - test - to be committed*/

/*

.ss-fm .inline_title {
    margin: 1rem auto !important;
    xbackground: linear-gradient(to left,#fff, var(--hs-prmry-10) 20%, var(--hs-prmry-50) 40%, #fff );
    xbackground: linear-gradient(to left,#fff 1%, var(--hs-prmry-50) 10%, var(--hs-prmry-50) 50%, #fff 70%, #fff );
    background: linear-gradient(to left,#fff0 1%, var(--hs-prmry-50) 10%, var(--hs-prmry-50) 50%, #fff0 70%, #fff0 );
    xbox-shadow: 2rem 0 1rem var(--hs-prmry-50) !important;
    xtransform: translateX(2rem);
    padding-right: 1rem;
    xborder: none !important;
    xcolor: #888 !important;
    
}






#page {
    background: linear-gradient(-90deg,var(--hs-prmry-20), transparent);
}

.inline_title .mir-flt a {
    color: var(--hs-prmry-dark2) !important;
}


    .ss-fm .icon-text-input-div {
        transform: translateY(-4px);
    }


    .ss-fm .icon-text-input-div input {
        height: 32px !important;
    }

    .ss-fm .icon-text-input-div a {
        top:4px;
    }


    */





.page_header, .page_footer {
    background-color: #a29061;
}

.page_header {
    border-bottom-color: #E8CEAA;
}


.fm-title {
    border-color: #DAD3BD;
    color: #000;
}


.ui-widget-header {
    border: 1px solid #00664F;
    background: #00664F !important;
    color: #fff;
    font-weight: bold;
}

.ui-state-active a, .ui-state-active a:link, .ui-state-active a:visited {
    color: #00664F;
}

.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active {
    border-color: #00664F;
}



.fm-ui-plain-tab {
    background: #f0fff8 !important;
}

.fm-ui-plain-title {
    color: #00664F;
    background-color: #ced;
}

.ui-datepicker-month, .ui-datepicker-year {
    color: #fff !important;
}


/**/



.popup-content-no-hover.inp-dlg hr {
    visibility:hidden !important;
}

.ui-tb.box-at-cont-bottom {
    background-image: none !important;
    background-color: transparent !important;
    text-align: center;

}

.ui-tb.box-at-cont-bottom a {
    background-image: none !important;
    padding: .5rem 5rem !important;
    border-radius: .5rem !important;
}

.msg-box {
    border:1px solid rgba(0,0,0,.2) !important;
    padding-top:4rem;
}

    .msg-box:before {
        content: '';
        position: absolute;
        top: -2.5rem;
        left: calc(50% - 2.5rem);
        height: 5rem;
        width: 5rem;
        background-color: var(--hs-prmry);
        border: 5px solid #fff;
        border-radius: 50%;
        /**/
        color:var(--hs-scndry);
        content: 'save';
        font-size:24px;
        font-family: 'Material Icons';
        display: flex;
        justify-content: center;
        align-items: center;
    }



    .loaded {
        transition: height linear 1s;
    }