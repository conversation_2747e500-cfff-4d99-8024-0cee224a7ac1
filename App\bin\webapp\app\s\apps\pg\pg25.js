﻿/* 
HowbaniSoft Payment Gateway: Online Channels (Net + Counter)
2016-2020
*/



var sabafon_units_arr = null;
var sabafon_units_counter_arr = null;


function on_pg_service_changed(e, isCounter) {
    var cur_val = $('#svc_id').val();

    var reset_amnt = hs.e.getId(e) == 'svc_id';
        
    if (reset_amnt)
        $('#amnt').val('');

    show_elements('r_amnt;r_units;r_main_id', false);
    if (cur_val == null || cur_val.length == 0)
        return;

    var subsno_tit = "رقم المشترك";
    // for dj
    if (cur_val == "edd01" || cur_val == "djw01")
        subsno_tit = hs.L("Bill No", "رقم الفاتورة");
           

    hs.msg.tip("subs_no_name", subsno_tit, 8);
    $("#subs_no_name").attr("placeholder", subsno_tit);
    $("#subno-tit").text(subsno_tit);

    // MTN units

    if (cur_val == 'mtn01' || cur_val == 'mtn02') {
        show_element('r_units', true);
        if (isCounter)
            fill_dropdown_list('units',
                {
                    '155': '200',
                    '240': '300',
                    '410': '500',
                    '575': '700',
                    '830': '1000',
                    '1250': '1500',
                    '2500': '2950',
                    '5000': '5850'
                }, reset_amnt);
        else
            fill_dropdown_list('units',
                {
                    '155': '155',
                    '240': '240',
                    '410': '410',
                    '575': '575',
                    '830': '830',
                    '1250': '1250',
                    '2500': '2500',
                    '5000': '5000'
                }, reset_amnt);

        if (reset_amnt) {
            $('#units').prepend($('<option />').val('').html(' ---- اختر المبلغ ----- '));
            $('#units').val('');
        };

        return;
    };

    // Sabafon units

    if (cur_val == 'sf02') {

        show_element('r_units', true);

        if (isCounter) {
            fill_dropdown_list('units', sabafon_units_counter_arr, reset_amnt);
        }
        else {
            fill_dropdown_list('units', sabafon_units_arr, reset_amnt);
        }

        if (reset_amnt) {
            $('#units').prepend($('<option />').val('').html(' ---- اختر عدد الوحدات ----- '));
            $('#units').val('');
        };

        return;
    };

    show_element('r_amnt', true);

    if (reset_amnt) {
        $('#amnt').val('');
        $('#units').val('');
    };

    // -----------

    

    if (cur_val == 'yp003') // elect
    {
        fill_dropdown_list('main_id',
            {
                '11': 'الامانة_المنطقة_الاولى',
                '20': 'الأمانة_المنطقة_الثانية',
                '13': 'الأ مانة_المنطقة_الثالثة',
                '14': 'الأ مانة_المنطقة_الرابعة',
                '30': 'منطقة تعز',
                '12': 'منطقة ذمار',
                '16': 'منطقة وادي حضرموت',
                '17': 'منطقة ساحل حضرموت',
                '18': 'منطقة ابين',
                '19': 'منطقة لودر',
                '21': 'منطقة يريم',
                '22': 'منطقة شبوة',
                '24': 'منطقة المهرة',
                '26': 'منطقة اب',
                '35': 'منطقة الضالع',
                '36': 'منطقة الحديدة',
                '40': 'منطقة حجة',
                '46': 'منطقة صعدة',
                '50': 'منطقة مأرب',
                '54': 'منطقة رداع',
                '56': 'منطقة المحويت',
                '58': 'منطقة البيضاء',
                '60': 'منطقة محافظة صنعاء الثالثة',
                '61': 'منطقة عمران',
                '62': 'منطقة الجوف',
                '63': 'منطقة محافظة صنعاء الا ولى',
                '64': 'منطقة محافظة صنعاء الثانية',
                '15': 'عدن المنطقة الا ولى',
                '23': 'عدن المنطقة الثانية',
                '25': 'عدن المنطقة الثالثة',
                '27': 'منطقة لحج',
                '33': 'محافظة ريمة',
                '1': 'كهرباء المحطة',
                '4': 'كهرباء الزاوية',
                '3': 'كهرباء السهيلة',
                '2': 'كهرباء الزيلعي',
                '34': 'منطقة كهرباء الحداء'
            });

        show_element('r_main_id', true);
        $('#main_id').prepend($('<option />').val('').html(' ---- اختر المنطقة ----- '));
        $('#main_id').val('');
       
        return;
    };



    if (cur_val == 'yp004') // water
    {
        fill_dropdown_list('main_id',
            {
                '5': 'مؤسسة المياه (الأمانة)',
                '6': 'محافظة تعز',
                '7': 'محافظة الحديدة',
                '31': 'محافظة عدن',
                '35': 'منطقة مياه المكلا',
                '45': 'منطقة مياه الشحر',
                '55': 'منطقة مياه اغيل باوزير',
                '65': 'منطقة مياه الديس الشرقية',
                '68': 'منطقة مياه سقطرة',
                '69': 'منطقة مياه السريدة وقصيعر',
                '70': 'منطقة مياه ميفع',
                '28': 'مؤسسة المياه (عمران )',
                '14': 'محافظة الحديدة (الصليف )',
                '22': 'محافظة الحديدة(المراوعة)',
                '64': 'مياه مدينة التربه',
                '53': 'مؤسسة المياه (القطن -سيئون)',
                '8': 'مؤسسة المياه (محافظة اب)',
                '38': 'مؤسسة المياه (عتق -شبوة )',
                '43': 'مؤسسة المياه (شبام -سيئون )',
                '9': 'مؤسسة المياه (حجة)',
                '41': 'مؤسسة المياه ( سيئون )',
                '42': 'مؤسسة المياه ( ساه )',
                '66': 'مياه مدينة ذبحان',
                '34': 'مؤسسة المياه ( تريم )',
                '10': 'مياه منطقة ذمار'
            });

        show_element('r_main_id', true);
        $('#main_id').prepend($('<option />').val('').html(' ---- اختر المنطقة ----- '));
        $('#main_id').val('');

       

        return;
    };


    

};


// ----  //

function retrieve_cust_name() {
    var _url = "/app/fms/?fm=pg-cust&sind=y&cmd=ret-cust-name&cust-id=" + $("#cust_id").val();

    if (getUrlParameter("cmd") != "add")
        return;
    

    $.ajax({
        url: _url,
        success: function (result) {
            hs.eval(result);
           // window.alert(result);
        }
    });
}
