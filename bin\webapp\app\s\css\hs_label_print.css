﻿body {
    text-align: center;
    padding: 0;
    margin: 0;
    width: 100%;
    height: 100%;
  
}

@font-face
{
font-family: hs_barcode_font;
src: url('../barcode/Code39Azalea.ttf') format('woff');

font-weight: normal;
font-style: normal;
}

@font-face
{
font-family: hs_barcode_font_ean13;
src: url('../barcode/EAN-13.TTF') format('woff');

font-weight: normal;
font-style: normal;
}




.label-box {
    display:block;
  
    border: 1px solid #aaa; 
    font-size: 9pt;
    color: black;
    text-align: center;
    height: 100%;
    font-family:Arial;
    font-family: 'Times New Roman';
    font-weight: bold;
 
    overflow: hidden;    
   
}

.barcode {
    font-family: hs_barcode_font;
    font-size: 28pt !important;
    font-weight: normal;
    background-color: white;
    display:block;
    margin: 2px;
       
}

.barcode-ean13 {
    font-family: hs_barcode_font_ean13;
    font-size: 32pt !important;
    font-weight: normal;
    background-color: white;
    display:block;
    margin: 2px;
   

}

.comp-title {
    display: block;
    
}
 
.item-name {
    display: block;
    direction:rtl;
    font-weight: normal;
   
}

.item-price {
    display: inline-block;
    padding-right: 5pt;
   direction:rtl;
}

.item-no {
  
}

.item-code {
   
}

.item-spec {
    display: block;
    direction:rtl;
    font-weight: normal;
   
}

.item-block {
    display: block;

    
}

.page-break {
        display: block;
        page-break-after: always;
        border: none;
        margin: 0;
        height: 2px;
        overflow: hidden;
    }

@media screen {
    body {
        width: 3.4cm;
        height: 2.4cm;
        border: 1px dotted #888;

        width: 8.3cm;
        height: 3.7cm;

        
    }

    
}