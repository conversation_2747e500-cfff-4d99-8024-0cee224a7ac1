﻿
* { clear: both; vertical-align: top;  }

body {
    border: dotted 1px #eee;
	
	vertical-align: middle;  
	border-collapse:collapse;  
	background-color: #fff;
	width: 17cm;
	margin: auto auto 10px auto;
		   
    direction: rtl;  
	text-align: right;

    font-family: Tahoma;
    font-size: 9pt;
}

.cards_page {
    width: 100%;
}

.card_box {
        border: dotted 1px #eee;
        padding: 10px 10px 10px 10px; 
    }

.card {
    
    width: 100%;
	border: solid 1px #cde;
	
	vertical-align: middle;  
	border-collapse :collapse;  
	background-color: #fff;
    
}

.cards_line {
    
}

.cards_line_new_page {
    page-break-after: always;
}


/* card fields*/

.card-net-title {
    background-color: #eee;
    text-align:  center;
    visibility:  visible;
}

.card-reseller {
    text-align:  center;
    font-size: 7pt;
}


.card-print-date {
    font-size: 7pt;
  
}

.card-note {
    border-top: solid 1px #eee;
    font-size: 7pt;
    color: #888;
    text-align: center;
}