2025/07/28 00:01:55.733 ; Info ; 1 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/07/28 00:01:55.867 ; Trace ; 1 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/07/28 00:01:55.867 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/07/28 00:01:55.867 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/07/28 00:01:55.867 ; Log ; 1 ;  ; 0000: ; 28/7/2025 00:01:55 @Http Req#0
2025/07/28 00:01:55.867 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/07/28 00:01:55.867 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 671  @Http Req#0
2025/07/28 00:01:55.867 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/07/28 00:01:55.875 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/07/28 00:01:55.879 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/07/28 00:01:55.929 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/07/28 00:01:55.929 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/07/28 00:01:55.990 ; Info ; 1 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/07/28 00:01:56.017 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/07/28 00:01:56.022 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/07/28 00:01:56.032 ; Trace ; 1 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/07/28 00:01:56.055 ; Trace ; 1 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/07/28 00:01:56.055 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/07/28 00:01:56.055 ; Trace ; 1 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/07/28 00:01:56.062 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/07/28 00:01:56.071 ; Info ; 1 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/07/28 00:01:56.131 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/07/28 00:01:56.136 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/07/28 00:01:56.164 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/07/28 00:01:56.165 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/07/28 00:01:56.166 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/07/28 00:01:56.168 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/07/28 00:01:56.203 ; Trace ; 1 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/07/28 00:01:56.205 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/07/28 00:01:56.205 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/07/28 00:01:56.208 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/07/28 00:01:56.210 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/07/28 00:01:56.220 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/07/28 00:01:56.224 ; Trace ; 1 ;  ; 0000: ; HsStoredObject cached: 0 @Http Req#0
2025/07/28 00:01:56.328 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/07/28 00:01:56.328 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/07/28 00:01:56.328 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/07/28 00:01:56.329 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/07/28 00:01:56.329 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/07/28 00:01:56.329 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/07/28 00:01:56.330 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/07/28 00:01:56.394 ; Trace ; 9 ; ::1 ; 0000: ; Building forms auth roles @Http Req#1
2025/07/28 00:01:56.397 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:login @Http Req#1
2025/07/28 00:01:56.397 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:logout @Http Req#1
2025/07/28 00:01:56.429 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/07/28 00:01:56.468 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/07/28 00:01:56.468 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:relation @Http Req#1
2025/07/28 00:01:56.472 ; Log ; 9 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/07/28 00:01:56.482 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/07/28 00:02:01.331 ; Log ; 8 ;  ; 0000: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#1
2025/07/28 00:02:01.331 ; Log ; 7 ;  ; 0000: ; Started threaded queue processor: DelayedSqlQueue @Http Req#1
2025/07/28 00:02:01.333 ; Log ; 8 ;  ; 0000: ; Loading scheduled tasks.. @Http Req#1
2025/07/28 00:02:01.340 ; Log ; 8 ;  ; 0000: ; Task (24BPU7FHR1W:BackUp) cancelled due to long delay @Http Req#1
2025/07/28 00:02:04.830 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/07/28 00:02:04.940 ; Trace ; 9 ; ::1 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/07/28 00:02:05.271 ; Trace ; 9 ; ::1 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/07/28 00:02:05.294 ; Info ; 15 ; ::1 ; 9900: ; Init Client: 9900 @Http Req#2 @Req#1 0s
2025/07/28 00:02:05.308 ; Log ; 15 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/07/28 00:02:05.309 ; Log ; 15 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/07/28 00:02:05.310 ; Trace ; 15 ; ::1 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/07/28 00:02:05.329 ; Trace ; 15 ; ::1 ; 9900: ; COA.RebuildAccountsTree @Http Req#2 @Req#1 0s
2025/07/28 00:02:05.329 ; Trace ; 15 ; ::1 ; 9900: ; Accounts loaded:65 @Http Req#2 @Req#1 0s
2025/07/28 00:02:05.329 ; Trace ; 15 ; ::1 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/07/28 00:02:05.331 ; Trace ; 15 ; ::1 ; 9900: ; Accounts loaded:0 @Http Req#2 @Req#1 0s
2025/07/28 00:02:05.333 ; Trace ; 15 ; ::1 ; 9900: ; Caching Sales Items:9900 @Http Req#2 @Req#1 0s
2025/07/28 00:02:05.334 ; Log ; 15 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/07/28 00:02:05.355 ; Log ; 15 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/07/28 00:02:05.355 ; Trace ; 15 ; ::1 ; 9900: ; Caching all items stock data.. @Http Req#2 @Req#1 0s
2025/07/28 00:02:05.368 ; Trace ; 15 ; ::1 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#2 @Req#1 0s
2025/07/28 00:02:05.370 ; Log ; 15 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/07/28 00:02:05.370 ; Log ; 15 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/07/28 00:02:05.373 ; Log ; 15 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/07/28 00:02:05.373 ; Info ; 15 ; ::1 ; 9900: ; Init Client Completed: 9900 @Http Req#2 @Req#1 0s

2025/07/28 00:22:56.597 ; Trace ; 38 ;  ; 9900: ; Session End: User logged out @Http Req#3
2025/07/28 00:22:56.597 ; Log ; 38 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads... @Http Req#3
2025/07/28 00:22:56.597 ; Log ; 8 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue @Http Req#3
2025/07/28 00:22:56.597 ; Log ; 7 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue @Http Req#3
2025/07/28 00:22:56.613 ; Log ; 38 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ******** @Http Req#3

2025/07/28 12:30:21.768 ; Info ; 1 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/07/28 12:30:21.893 ; Trace ; 1 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/07/28 12:30:21.908 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/07/28 12:30:21.908 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/07/28 12:30:21.908 ; Log ; 1 ;  ; 0000: ; 28/7/2025 12:30:21 @Http Req#0
2025/07/28 12:30:21.908 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/07/28 12:30:21.908 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 671  @Http Req#0
2025/07/28 12:30:21.908 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/07/28 12:30:21.908 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/07/28 12:30:21.924 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/07/28 12:30:21.971 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/07/28 12:30:21.971 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/07/28 12:30:22.002 ; Info ; 1 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/07/28 12:30:22.033 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/07/28 12:30:22.033 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/07/28 12:30:22.033 ; Trace ; 1 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/07/28 12:30:22.049 ; Trace ; 1 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/07/28 12:30:22.049 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/07/28 12:30:22.049 ; Trace ; 1 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/07/28 12:30:22.065 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/07/28 12:30:22.065 ; Info ; 1 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/07/28 12:30:22.096 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/07/28 12:30:22.096 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/07/28 12:30:22.112 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/07/28 12:30:22.112 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/07/28 12:30:22.112 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/07/28 12:30:22.127 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/07/28 12:30:22.158 ; Trace ; 1 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/07/28 12:30:22.158 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/07/28 12:30:22.158 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/07/28 12:30:22.158 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/07/28 12:30:22.158 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/07/28 12:30:22.158 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/07/28 12:30:22.158 ; Trace ; 1 ;  ; 0000: ; HsStoredObject cached: 0 @Http Req#0
2025/07/28 12:30:22.268 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/07/28 12:30:22.268 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/07/28 12:30:22.268 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/07/28 12:30:22.268 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/07/28 12:30:22.268 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/07/28 12:30:22.268 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/07/28 12:30:22.268 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/07/28 12:30:22.455 ; Trace ; 9 ; 176.123.31.71 ; 0000: ; Building forms auth roles @Http Req#1
2025/07/28 12:30:22.471 ; Trace ; 9 ; 176.123.31.71 ; 0000: ; Failed to load form:login @Http Req#1
2025/07/28 12:30:22.471 ; Trace ; 9 ; 176.123.31.71 ; 0000: ; Failed to load form:logout @Http Req#1
2025/07/28 12:30:22.487 ; Trace ; 9 ; 176.123.31.71 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/07/28 12:30:22.518 ; Trace ; 9 ; 176.123.31.71 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/07/28 12:30:22.533 ; Trace ; 9 ; 176.123.31.71 ; 0000: ; Failed to load form:relation @Http Req#1
2025/07/28 12:30:22.533 ; Log ; 9 ; 176.123.31.71 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/07/28 12:30:22.533 ; Trace ; 9 ; 176.123.31.71 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/07/28 12:30:27.279 ; Log ; 7 ;  ; 0000: ; Started threaded queue processor: DelayedSqlQueue @Http Req#1
2025/07/28 12:30:27.279 ; Log ; 8 ;  ; 0000: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#1
2025/07/28 12:30:27.279 ; Log ; 8 ;  ; 0000: ; Loading scheduled tasks.. @Http Req#1
2025/07/28 12:30:30.934 ; Trace ; 9 ; 176.123.31.71 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/07/28 12:30:31.028 ; Trace ; 9 ; 176.123.31.71 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/07/28 12:30:31.291 ; Trace ; 9 ; 176.123.31.71 ; 0000: ; Building forms auth roles completed @Http Req#1

2025/07/28 12:51:22.627 ; Trace ; 79 ;  ; 0000: ; Session End: User logged out @Http Req#1
2025/07/28 12:51:22.627 ; Log ; 79 ;  ; 0000: ; Initiating app shutdown, waiting for worker threads... @Http Req#1
2025/07/28 12:51:22.627 ; Log ; 7 ;  ; 0000: ; Ending threaded queue: DelayedSqlQueue @Http Req#1
2025/07/28 12:51:22.627 ; Log ; 8 ;  ; 0000: ; Ending threaded queue: ScheduledTasksQueue @Http Req#1
2025/07/28 12:51:22.644 ; Log ; 79 ;  ; 0000: ; ***** App shutdown [HsApp] completed. Bye ******** @Http Req#1

