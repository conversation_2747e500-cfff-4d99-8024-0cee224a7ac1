﻿




body {
    margin: 0;
    overflow: auto;
    background: url(none) #fff;
    direction: rtl;  
	text-align: right;
    
    background-color: #fff; 

    
    
}

.cms-page
{
	margin: 0px auto; 
	text-align: right;
	
    width: 100%;
     
    
}

.cms-page * {
    font-family: Tahoma;
    
    font-size: 10pt;
    color: #444;
    box-sizing: border-box;
    line-height: 24px;

    font-family: 'Traditional Arabic', Tahoma !important ;
    font-size: 14pt;
    font-weight: bold;
    line-height: 28px;

    line-height: 36px;
}

.cms-page-hdr
{
    height: 80px;
	vertical-align: top;
	
	color: #b0b0b0;
	background: url(bg/logo_small.png) no-repeat; 
    background-position: left center;
    padding: 10px 30px;
   

     
     background-position: center center;

}

.cms-page strong {
    font-size: 18pt;
}

.cms-page p {
    line-height: 36px;
}


li {
    list-style:square;
    list-style-type:square;
    list-style-image:url(imgs/li.png);
    margin: 4px auto;
}

A 
{
	color: #808000;
	text-decoration: none;
	  
}

A:Hover 
{
	color: red;
	text-decoration: none;
}






.cms-page-hdr A
{
	color: White; 
}


.cms-page-toolbar
{
	background-image: url(bg/m04.jpg); 
	height: 30px; 
	text-align: center;  
    line-height: 28px;
}

.cms-page-toolbar A
{
	color: White;  
	background-image: none;   
    line-height: 28px;
}


.cms-page-cont {
    height: 700px;
    min-height: 800px;
    vertical-align: top;
    padding: 0 20px;
    
   

    text-align:justify;
}

.cms-page-title {
    color: darkblue;
    font-size: 20pt;
    font-weight: bold;
    display: block;
    text-align: center;
    padding: 5px 20px;
    border-radius: 8px;

     padding: 5px 20px;
    border-radius: 8px;
    background-color: #cde;
    margin-top: 5px;
}

.cms-inline-title {
    color: darkblue;
    font-size: 20pt;
    font-weight: bold;
    display: block;
  
    padding: 5px 20px;
    border-radius: 8px;
    background-color: #eee;
    
}



.cms-page-footer
{
	
	
	
	color: white;
	text-align: center; 
	background-color: #e0e0e0;   
	background-image: url(bg/bg5.jpg);  
	vertical-align: middle;  
	height: 40px; 
}



.lft_shad, .rgt_shad {
        display: none;
    }



.rep_tab {
    border: 1px solid #cde;
    vertical-align: middle;
    border-collapse: collapse;
    width: 100%;
    margin: 2px auto;
    padding: 4px;
    
    
}



    .rep_tab .title {
        text-indent: 10px;
        font-weight: bold;
        color: #164871;
        background-color: #cde;
        vertical-align: middle;
        
    }
    
    .rep_tab .head, .rep_tab .total, .rep_tab .foot, .rep_tab .seq, .rep_tab thead, .rep_tab tfoot {
        text-indent: 10px;
        font-weight: bold;
        color: #164871;
        background-image: url(../images/bg/hdr_bg.jpg);
        background-repeat: repeat;
        background-color: #ddd;
        vertical-align: middle;
        text-align: center;
       
    }


        .rep_tab .data, .rep_tab td, .rep_tab th {
        padding: 4px 4px;
        border: solid 1px #ccc;
        
       
    }



@media screen and (min-width: 1200px) {

    body.cms {
         max-width: 1200px;
         margin: 0 auto;
    }
   
    

    .lft_shad {
        width: 20px;
      
        background-image: url(bg/bg1223.jpg);
        background-position: left top;
        background-repeat: repeat-y;
       display:table-cell;
    }

    .rgt_shad {
        width: 20px;
      
        background-image: url(bg/bg1222.jpg);
        font-size: 16px;
        color: Black;
        background-position: right top;
        background-repeat: repeat-y;
         display:table-cell;
    }
}


@media screen and (max-width: 700px) {

  .cms-page  * {
        font-weight: normal;
    }

    .cms-page-hdr {
        
    }

    .cms-page-cont {
        padding: 0 10px;
       
    }

    li {
   
    margin: 0;
}

    

}