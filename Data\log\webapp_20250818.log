2025/08/18 07:13:36.373 ; Info ; 1 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/08/18 07:13:36.532 ; <PERSON> ; 1 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/08/18 07:13:36.532 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/18 07:13:36.532 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/18 07:13:36.532 ; Log ; 1 ;  ; 0000: ; 18/8/2025 07:13:36 @Http Req#0
2025/08/18 07:13:36.532 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/18 07:13:36.532 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 650  @Http Req#0
2025/08/18 07:13:36.532 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/18 07:13:36.547 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/18 07:13:36.547 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/18 07:13:36.610 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/18 07:13:36.610 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/18 07:13:36.656 ; Info ; 1 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/08/18 07:13:36.672 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/18 07:13:36.688 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/18 07:13:36.688 ; Trace ; 1 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/18 07:13:36.703 ; Trace ; 1 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/08/18 07:13:36.703 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/18 07:13:36.703 ; Trace ; 1 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/18 07:13:36.719 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/18 07:13:36.719 ; Info ; 1 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/08/18 07:13:36.750 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/18 07:13:36.766 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/18 07:13:36.781 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/18 07:13:36.781 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/18 07:13:36.781 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/18 07:13:36.781 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/18 07:13:36.828 ; Trace ; 1 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/08/18 07:13:36.828 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/18 07:13:36.828 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/18 07:13:36.828 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/18 07:13:36.828 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/18 07:13:36.828 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/18 07:13:36.828 ; Trace ; 1 ;  ; 0000: ; HsStoredObject cached: 0 @Http Req#0
2025/08/18 07:13:36.922 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/18 07:13:36.922 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/18 07:13:36.922 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/18 07:13:36.922 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/18 07:13:36.922 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/18 07:13:36.922 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/18 07:13:36.922 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/18 07:13:36.969 ; Trace ; 9 ; 176.123.28.160 ; 0000: ; Building forms auth roles @Http Req#1
2025/08/18 07:13:36.969 ; Trace ; 9 ; 176.123.28.160 ; 0000: ; Failed to load form:login @Http Req#1
2025/08/18 07:13:36.969 ; Trace ; 9 ; 176.123.28.160 ; 0000: ; Failed to load form:logout @Http Req#1
2025/08/18 07:13:37.000 ; Trace ; 9 ; 176.123.28.160 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/08/18 07:13:37.016 ; Trace ; 9 ; 176.123.28.160 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/08/18 07:13:37.016 ; Trace ; 9 ; 176.123.28.160 ; 0000: ; Failed to load form:relation @Http Req#1
2025/08/18 07:13:37.031 ; Log ; 9 ; 176.123.28.160 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/18 07:13:37.031 ; Trace ; 9 ; 176.123.28.160 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/08/18 07:13:41.930 ; Log ; 8 ;  ; 0000: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#1
2025/08/18 07:13:41.930 ; Log ; 7 ;  ; 0000: ; Started threaded queue processor: DelayedSqlQueue @Http Req#1
2025/08/18 07:13:41.930 ; Log ; 8 ;  ; 0000: ; Loading scheduled tasks.. @Http Req#1
2025/08/18 07:13:41.946 ; Log ; 8 ;  ; 0000: ; Task (24BPU7FHR1W:BackUp) cancelled due to long delay @Http Req#1
2025/08/18 07:13:41.946 ; Log ; 8 ;  ; 0000: ; Task (24BPUGXOXUO:CleanUp) cancelled due to long delay @Http Req#1
2025/08/18 07:13:45.476 ; Trace ; 9 ; 176.123.28.160 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/08/18 07:13:45.570 ; Trace ; 9 ; 176.123.28.160 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/08/18 07:13:45.896 ; Trace ; 9 ; 176.123.28.160 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/08/18 07:13:45.912 ; Info ; 9 ; 176.123.28.160 ; 9900: ; Init Client: 9900 @Http Req#1
2025/08/18 07:13:45.912 ; Log ; 9 ; 176.123.28.160 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#1
2025/08/18 07:13:45.912 ; Log ; 9 ; 176.123.28.160 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#1
2025/08/18 07:13:45.928 ; Trace ; 9 ; 176.123.28.160 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#1
2025/08/18 07:13:45.928 ; Trace ; 9 ; 176.123.28.160 ; 9900: ; COA.RebuildAccountsTree @Http Req#1
2025/08/18 07:13:45.928 ; Trace ; 9 ; 176.123.28.160 ; 9900: ; Accounts loaded:65 @Http Req#1
2025/08/18 07:13:45.928 ; Trace ; 9 ; 176.123.28.160 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#1
2025/08/18 07:13:45.943 ; Trace ; 9 ; 176.123.28.160 ; 9900: ; Accounts loaded:0 @Http Req#1
2025/08/18 07:13:45.943 ; Trace ; 9 ; 176.123.28.160 ; 9900: ; Caching Sales Items:9900 @Http Req#1
2025/08/18 07:13:45.943 ; Log ; 9 ; 176.123.28.160 ; 9900: ; Caching SalesItem.. @Http Req#1
2025/08/18 07:13:45.959 ; Log ; 9 ; 176.123.28.160 ; 9900: ; Sales Items Cached: 0 @Http Req#1
2025/08/18 07:13:45.959 ; Trace ; 9 ; 176.123.28.160 ; 9900: ; Caching all items stock data.. @Http Req#1
2025/08/18 07:13:45.975 ; Trace ; 9 ; 176.123.28.160 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#1
2025/08/18 07:13:45.975 ; Log ; 9 ; 176.123.28.160 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#1
2025/08/18 07:13:45.975 ; Log ; 9 ; 176.123.28.160 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#1
2025/08/18 07:13:45.979 ; Log ; 9 ; 176.123.28.160 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#1
2025/08/18 07:13:45.979 ; Info ; 9 ; 176.123.28.160 ; 9900: ; Init Client Completed: 9900 @Http Req#1
2025/08/18 07:13:46.010 ; Trace ; 9 ; 176.123.28.160 ; 9900:guest ; Loading user fav menu:guest @Http Req#1
2025/08/18 07:13:46.010 ; Log ; 9 ; 176.123.28.160 ; 9900:guest ; App Comm Login Success, using user=9900:guest @Http Req#1
2025/08/18 07:14:01.912 ; Trace ; 5 ; 176.123.28.160 ; 9900:guest ; AuthCheck Failed: biz(14,) @Http Req#5 @Req#4 0s
2025/08/18 07:14:01.912 ; Trace ; 5 ; 176.123.28.160 ; 9900:guest ; AuthCheck Failed: fm(vucost,es-item) @Http Req#5 @Req#4 0s
2025/08/18 07:14:01.912 ; Trace ; 5 ; 176.123.28.160 ; 9900:guest ; AuthCheck Failed: fm(vuamnts,es-so-cust) @Http Req#5 @Req#4 0s
2025/08/18 07:14:01.912 ; Trace ; 5 ; 176.123.28.160 ; 9900:guest ; AuthCheck Failed: fm(add,es-so-cust) @Http Req#5 @Req#4 0s

2025/08/18 07:34:37.156 ; Trace ; 144 ;  ; 9900: ; Session End: User logged out @Http Req#6
2025/08/18 07:34:37.156 ; Log ; 144 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads... @Http Req#6
2025/08/18 07:34:37.156 ; Log ; 7 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue @Http Req#6
2025/08/18 07:34:37.156 ; Log ; 8 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue @Http Req#6
2025/08/18 07:34:37.172 ; Log ; 144 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ******** @Http Req#6

