﻿<?xml version="1.0" encoding="utf-8"?>
<SystemHelp xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
 <items>

   
   <Entry>
     <id>sys-cfg.es-sys-fi-gl</id>
     <title>إعدادات نظام إدارة الأستاذ العام</title>
     <content>
       <![CDATA[
إعدادات نظام الأستاذ العام لها تأثير كبير على عمل النظام - لذا يجب أن يتم ضبطها فقط بواسطة المحاسب أو المختص الذي لديه المعرفة بالتأثيرات.
وغالبا يتم ضبط هذه الإعدادات عند تركيب النظام ونادرا ان تكون  هناك حاجة لتغييرها.
ويفضل إعتماد الإعدادات التقائية إذا كان المستخدم ليس لديه معرفة بالتأثيرات.


]]>
     </content>
    
   </Entry>
   

   <Entry>
     <id>gl_sys_opts</id>
     <title>خيارات نظام الأستاذ العام</title>
     <content>
       <![CDATA[




]]>
     </content>
   </Entry>

   
   
   <Entry>
     <id>gl_sys_opts4</id>
     <title>تعدد العملات في النظام</title>
     <content>
       <![CDATA[
هذا الخيار يسمح بتعدد العملات في النظام - أي إنه سوف يسمح للنظام بالتعامل بالعملات الأجنبية.
عند تعطيل هذا الخيار:
- سوف يتم تثبيت حقل العملة في المستندات على العملة المحلية
- سوف يتم إخفاء اعمدة المقابل المحلي و سعر التحويل من شاشة القيود اليومية ومستندات الصرف والقبض متعددة الأطراف

هذا الخيار مفعل بشكل تلقائي، 
إذا كانت المؤسسة لا تتعامل بالعملات الأجنبية يفضل أن يتم تعطيل هذا الخيار

]]>
     </content>
   </Entry>


   <Entry>
     <id>gl_sys_opts5</id>
     <title>حساب الأستاذ متعدد العملات</title>
     <content>
       <![CDATA[
هذا الخيار يسمح بأن يكون الحساب الواحد له عملات متعددة على نفس رقم الحساب.
كل عملة من عملات الحساب تعتبر مستقله تماما عن العملات الأخرى لها رصيد خاص و كشف حساب مخصص و يمكن إعدادها بشكل مستقل مثلا يمكن عمل تسقيف مختلف أو السماح بالرصيد السالب وخلافه من إعدادات الحساب
       
عند إنشاء القيود المحاسبية آليا سوف يقوم النظام بإختيار عملة الحساب بناءا على عملة المستند، في حال عدم وجود عملة مناسبة فإن النظام سوف يستخدم العملة الرئيسية للحساب (إذا كانت إعدادات الحساب تسمح بذلك) وإذا كانت الإعدات لا تسمح بذلك فإن ترحيل المستند يفشل و تظهر رسالة للمستخدم أن الحساب ليس له العملة المعينه. و على المستخدم إضافة العملة إلى الحساب.
       
الخيار مفعل بشكل تلقائي

]]>
     </content>
   </Entry>

   <Entry>
     <id>gl_sys_opts19</id>
     <title>السماح بالتحويل بين العملات الأجنبية في حال تغيير سعر التحويل في المستند</title>
     <content>
       <![CDATA[
إذا كان المستند بعملة أجنبية ، و تصادف أن القيود المالية الناتجة عن المستند تؤثر على حسابات بعملة أجنبية أخرى, فإن النظام سيقوم بعمل تحويل  من عملة المستند الأجنبية إلى العملة الأجنبية الأخرى على مرحلتين من عملة المستند إلى العملة المحلية و من ثم تحويل من العملة المحلية إلى العملة الأجنبية الأخرى.  
وفي حال تم تغيير سعر التحويل على مستوى المستند، فإن ذلك سيؤثر على عملية التحويل. 
في حال تعطيل هذا الخيار فإن النظام لن يسمح بهذه العملية مادام تم تغيير سعر التحويل على مستوى المستند.

الخيار معطل بشكل تلقائي

]]>
     </content>
   </Entry>


      <Entry>
     <id>gl_sys_opts21</id>
     <title>عملة مبلغ القيد يجب أن تطابق عملة الحساب في القيود اليومية وسندات القبض والصرف متعددة الأطراف</title>
     <content>
       <![CDATA[
عند تفعيل هذا الخيار فإن النظام سوف يمنع المحاسب من إضافة قيد على حساب بعملة مختلفة عن عملة الحساب. 
سيقوم النظام بمنع التحويل الآلي من عملة القيد إلى عملة الحساب.

هذا الخيار ينطبق على شاشات القيود اليومية اليدوية وسندات القبض والصرف متعددة الأطراف فقط

الخيار مفعل بشكل تلقائي
     
]]>
     </content>
   </Entry>
     
   
<Entry>
     <id>gl_sys_opts6</id>
     <title>عملة حساب العميل يجب أن تطابق عملة المستند/القيد</title>
     <content>
       <![CDATA[
هذا الخيار يمنع عمل فاتورة أو سندات على حساب العميل بعملة تختلف عن عملة حساب العميل - وذلك يمنع التحويل الآلي للمبلغ من عملة الفاتورة أو المستند إلى عملة العميل.

عند تعطيل الخيار يمكن عمل فاتورة بعملة مختلفة و سيقوم النظام بعمل تحويل المبلغ إلى عملة العميل في القيد المحاسبي. 

الخيار مفعل بشكل تلقائي
]]>
     </content>
</Entry>
  
    
       <Entry>
     <id>gl_sys_opts7</id>
     <title>عملة حساب المورد يجب أن تطابق عملة المستند/القيد</title>
     <content>
       <![CDATA[
هذا الخيار يمنع عمل فاتورة أو سندات على حساب المورد بعملة تختلف عن  عملة حساب المورد - وذلك يمنع التحويل الآلي للمبلغ من عملة الفاتورة أو المستند إلى عملة المورد.

عند تعطيل الخيار يمكن عمل فاتورة بعملة مختلفة و سيقوم النظام بعمل تحويل المبلغ إلى عملة المورد في القيد المحاسبي. 

الخيار مفعل بشكل تلقائي
]]>
     </content>
   </Entry>
   
   
      <Entry>
     <id>gl_sys_opts18</id>
     <title>السماح بإنشاء حسابات (العملاء والموردين والموظفين) كحسابات استاذ عام (غير تحليلية) - غير محبذ</title>
     <content>
       <![CDATA[
النظام بشكل تلقائي يتعامل مع حسابات العملاء والموردين والموظفين كحسابات تحليلية (وليست حسابات أستاذ عام فرعية) ويربط الحساب التحليلي بحساب استاذ عام فرعي..

لو أن المؤسسة تستخدم الطريقة التقليدية و تريد التعامل مع حسابات العملاء أو الموردين أو الموظفين كحسابات استاذ عام فرعي يمكن تفعيل هذا الخيار - و بحيث يتم السماح بإنشاء هذه الحسابات كحسابات استاذ عام فرعي.
]]>
     </content>
      <linked>linked_acc_no;ana_acc_no;gl_acc_no;acc_parent</linked>
   </Entry>
   
   
      <Entry>
     <id>gl_sys_opts20</id>
     <title>إظهار عمود النسبة المئوية في شاشة القيود اليومية والسندات متعددة الأطراف</title>
     <content>
       <![CDATA[
عند تفعيل هذا الخيار سيتم إضافة عمود يعرض النسبة المئوية للمبلغ أمام كل حساب في شاشة القيود اليومية و السندات . 
و السماح للمحاسب بأن يدخل المبلغ أو النسبة المئوية و سيقوم النظام بحساب المبلغ.

و بالنسبة للقيود المتكررة يمكن إدخال المبلغ الإجمالي و النظام سوف يوزع المبلغ على الحسابات بناءا على النسبة المئوية.
       
هذا الخيار معطل بشكل تلقائي

]]>
     </content>
   </Entry>
   
   
      <Entry>
     <id>gl_sys_opts8</id>
     <title>ترتيب الحسابات الفرعية في الدليل المحاسبي بالإسم بدلا من الرقم</title>
     <content>
       <![CDATA[
هذا ينطبق على الحسابات الفرعية التي تحت كل حساب رئيسي

الخيار مفعل بشكل تلقائي
]]>
     </content>
   </Entry>
   

        <Entry>
     <id>gl_sys_opts22</id>
     <title>تفعيل الإشعارات الآلية عبر الرسائل القصيرة والبريد الإلكتروني</title>
     <content>
       <![CDATA[
عند تفعيل هذا الخيار سيقوم النظام بإعلام صاحب الحساب عند حدوث اي حركة على حسابه بما في ذلك العكس عند إلغاء الترحيل - بنفس الطريقة التي تقوم بها البنوك.
يتم ارسال الإشعار عند الترحيل وإلغاء الترحيل  - لذا  يفضل عند تفعيل هذه الميزة أن يتم تعطيل ميزة الترحيل التقائي عند الحفظ - بحيث يتم مراجعة الفواتير والمستندات قبل ترحيلها.

يمكن استعراض الرسائل الناتجة عن القيود المالية الناتجة عن ترحيل المستند من قائمة الأدوات الخاصة بالمستند أو الفاتورة - او من  (القائمة الرئيسية - النظام - الرسائل الخارجة من النظام).

هذا الخيار يقوم بتفعيل الإشعارات الألية بشكل عام و لكنه لن يتم إرسال الإشعارات إلى الحسابات حتى يتم تفعيل الخيارعلى مستوى الحساب - و ذلك من إعدادات الحساب.
و بحيث يمكن تفعيل الإشعارات لحسابات معينه فقط.
و يجب أن يكون صاحب الحساب لديه موبايل مسجل في بيانات التواصل أو بريد إلكتروني 
سيقوم النظام بإصدار الإشعارات و لكن لإستكمال الإرسال فعلا يجب تفعيل و إعداد قناة الرسائل القصيرة أو قناة البريد الإلكتروني من إعدادات النظام.
       
مثال على رسالة الإشعار في حالة ترحيل فاتورة مبيعات آجلة:

عليكم 177,991.65 ر.ي
مبيعات على الحساب
فاتورة مبيعات #12529
الرصيد: 890,376.65 ر.ي عليكم ( مدين ) 

مثال على الإشعار عند قبض مبلغ نقدي من العميل:

لكم 50,000 ر.ي
دفعة من قيمة الفاتورة: 12529
سند قبض #10645
الرصيد: 840,376.65 ر.ي عليكم ( مدين ) 

]]>
     </content>
   </Entry>
     
     
     
      <Entry>
     <id>gl_sys_opts23</id>
     <title>ترحيل سندات القبض آليا بعد الحفظ</title>
     <content>
       <![CDATA[
عند تفعيل هذا الخيار سيقوم النظام آليا بترحيل سندات القبض بعد الحفظ.

هذا الخيار يتطلب أيضا تفعيل الخيار : السماح بترحيل المستندات آليا بعد الحفظ مباشرة - من خيارات الفواتير و المستندات

يتم تعطيل هذا الخيار آليا عند إستخدام نظام الموافقة

]]>
     </content>
        <linked>auto_post_after_save</linked>
   </Entry>
     
   
   <!--
   
         <Entry>
     <id></id>
     <title></title>
     <content>
       <![CDATA[

]]>
     </content>
   </Entry>
     
     -->


 <!--  ***********************************************************************************************************   -->  
   
         <Entry>
     <id>sys-cfg.es-sys-gen</id>
     <title>الإعدادات العامة للفواتير والمستندات</title>
     <content>
       <![CDATA[
       هذه الإعدادات تؤثر على كل انواع الفواتير والمستندات مثل فواتير المبيعات او المشتروات او الأوامر والمستندات المخزنية أو المالية..
       لذا تم فصل هذه الإعدادات عن كل الأنظمة بحيث تكون عامه للكل.
       
       هذه الإعدادات تجب أن تضبط من قبل الإدارة المالية بالإتفاق مع الإدارات الأخرى مثل المبيعات والمخازن
]]>
     </content>
   
   </Entry>

   <Entry>
     <id>sales_extra_charges</id>
     <title>الأعباء الإضافية للمبيعات</title>
     <content>
       <![CDATA[
الأعباء هي المبالغ الإضافية المرتبطة بعملية البيع مثل تكاليف النقل أو العمولات أو الأجور الإضافية وخلافه
يسمح النظام بإضافة 3 أعباء على الفاتورة الواحدة  - في النسخة الإحترافية يسمح بتحديد العدد المناسب للشركة وكحد أقصى 99
الأعباء الإضافية لا تضاف على قيمة الفاتورة و يمكن تقييدها لحسابات معينه وعلى حساب معين (بشكل تلقائي العميل).
و يجب التفريق بين الأعباء الإضافية و أعباء الفاتورة التي تضاف على قيمة الفاتورة و يمكن تفعيلها من خيارات الفواتير

مثال:
تم بيع بضاعة للعميل احمد و تم الإتفاق معه على توصيل البضاعة إلى مخزنه و بشرط أن يتحمل كافة المصاريف

- تم دفع 2000 ريال  من الصندوق مقابل اجور تحميل البضاعة من المخزن إلى السيارة
- و تم الإتفاق مع المورد صادق لنقل البضاعة مقابل 3000 ريال 
- و تم الإتفاق مع العميل منصور لتوفير عمال لتنزيل البضاعة من السيارة إلى المخزن مقابل 4000 ريال

فيهذا المثال سيتم تحديد الأعباء لحساب الصندوق والمورد صادق و العميل منصور
و سيتم تقييد الأعباء على حساب العميل أحمد

لكن إذا كان الإتفاق أن يتم النقل والتوصيل على حساب الشركة فسيتم تقييد الأعباء على حساب مصروفات أعباء المبيعات 



]]>
     </content>
     <linked>sales_doc_opts14</linked>
   </Entry>

   
          <Entry>
     <id>check_profit_pct</id>
     <title>التنبية عندما تكون نسبة الربح من بيع الصنف أقل من المحددة</title>
     <content>
       <![CDATA[
سيقوم النظام بالتنبيه عندما تكون نسبة الربح من بيع الصنف أقل من النسبة المحددة في متغيرات الصنف. حيث يمكن تحديد هذه النسبة على مستوى الصنف. إذا لم يتم تحديد نسبة  لصنف معين أو كانت النسبة صفر فإن النظام سيعتبر أنه لم يتم تحديد النسبة وبالتالي لن يقوم بإصدار هذا التنبيه لهذا الصنف. 

هذا المتغير مفعل بشكل تلقائي.

ملاحظة: 
إذا كان المتغير (السماح بالبيع بأقل من سعر الكلفة لهذا الصنف) مفعل بالنسبة للصنف فإن النظام سوف يسمح ببيع هذا الصنف بدون تحذير، وبغض النظر عن القيمة المستخدمة في هذا المتغير.

]]>
     </content>
   </Entry>
   
   
          <Entry>
     <id>wl_price_lt_cost</id>
     <title>عند البيع بسعر أقل من سعر التكلفة</title>
     <content>
       <![CDATA[
هذا المتغير يقوم بتوجية النظام للإجراء المفروض إتخاذه عند بيع صنف بأقل من سعر التكلفة، وهناك ثلاث قيم ممكنة
-	السماح بالعملية بدون تحذير المستخدم.
-	تحذير المستخدم والسماح له بالمواصله.
-	إيقاف تنفيذ العملية و إظهار رسالة خطأ  (القيمة التلقائية).

ملاحظة: 
إذا كان المتغير (السماح بالبيع بأقل من سعر الكلفة لهذا الصنف) مفعل بالنسبة للصنف فإن النظام سوف يسمح ببيع هذا الصنف بأقل من سعر التكلفة بدون تحذير، وبغض النظر عن القيمة المستخدمة في هذا المتغير.

]]>
     </content>
   </Entry>
   
          <Entry>
     <id>sales_doc_region</id>
     <title>المنطقة التجارية في فاتورة المبيعات</title>
     <content>
       <![CDATA[
هذا المتغير يحدد طريقة إستخدام المناطق التجارية في فواتير المبيعات،
- غير مفعل: لن يتم إستخدام المناطق التجارية، وسيتم إخفاء حقل المنطقة التجارية. 
- إختياري: يمكن إستخدام المناطق المناطق التجارية بشكل إختياري.  (المتغير التلقائي)
- إجباري: يجب إختيار المنطقة التجارية في فاتورة المبيعات.  ولن يتم حفظ الفاتورة بدون إختيار المنطقة التجارية 
عند إستخدام المنطقة التجارية في فاتورة المبيعات سوف يسهل الحصول على تقارير تفصيلية وتحليلة وإحصاءات عن المبيعات حسب المناطق التجارية. ويمكن أيضا ربط العملاء بالمناطق التجارية و بالتالي يمكن تحديد المنطقة التجارية عن طريق العميل، وبالتالي يمكن الحصول على هذه التقارير حتى ولو لم يتم تحديد المناطق التجارية في الفواتير.

]]>
     </content>
   </Entry>
   
   
       
         <Entry>
     <id>sales_doc_rep</id>
     <title>المندوب في فاتورة المبيعات</title>
     <content>
       <![CDATA[
-	غير مفعل: سيتم إخفاء الحقول الخاصة بالمندوب  و عمولة  المندوب. 
-	إختياري:  سيتم إظهار حقول المندوب والعمولة، مع السماح بترك هذه الحقول فارغة.  (المتغير التلقائي)
-	إجباري: يجب إختيار المندوب في فاتورة المبيعات.  ولكن سيظل تحديد عمولة المندوب  إختياري.

]]>
     </content>
   </Entry>
         
         
           
         <Entry>
     <id></id>
     <title></title>
     <content>
       <![CDATA[

]]>
     </content>
   </Entry>
   
   
     
         <Entry>
     <id></id>
     <title></title>
     <content>
       <![CDATA[

]]>
     </content>
   </Entry>
   
   
     
         <Entry>
     <id></id>
     <title></title>
     <content>
       <![CDATA[

]]>
     </content>
   </Entry>
     
     
  
     <Entry>
     <id>sales_doc_opts5</id>
     <title>إحتساب نسبة التخفيض على سعر الكلفة</title>
     <content>
       <![CDATA[
تفعيل هذا الخيار سيوجه النظام لعمل تخفيضات الأصناف بناءا على سعر كلفة الصنف وليس على سعر البيع للصنف و ذلك عند تحديد نسبة مئوية للتخفيض سواء على مستوى الصنف أو الفاتورة.
مثلا إذا كانت كلفة الصنف 1000 وسعر البيع هو 1200 و تم عمل تخفيض بنسبة 10% 
إذا كان هذا الخيار مفعلا سيكون مبلغ التخفيض للصنف هو 100 وأما إذا لم يكن مفعلا سيكون 120 
وبشكل عام تفعيل هذا الخيار يقوم بعمل تخفيض أقل

]]>
     </content>
   </Entry>

   <Entry>
     <id>sales_doc_opts14</id>
     <title>أعباء الفاتورة</title>
     <content>
       <![CDATA[
مبلغ يضاف على قيمة الفاتورة و يتحمله دوما العميل
]]>
     </content>
   </Entry>
   
   
   
    
   <Entry>
     <id>es.en_sales_goods_out</id>
     <title>فواتير المبيعات تتطلب أوامر صرف مخزني</title>
     <content>
       <![CDATA[

       يتم تفعيل هذا المتغير عندما تكون المبيعات مستقلة عن المخازن، حيث يتم إصدار الفاتورة في المبيعات، بينما يتم إستلام البضاعة من المخازن. 
عند تفعيل هذا المتغير:
-	يجب أن يتم إصدار أوامر صرف لكل فاتورة مبيعات من قبل إدارة المخازن حتى يتم إنزال الكميات من المخزون.
-	لن تؤثر  فاتورة المبيعات على المخزون بالكمية أو الكلفة، وسيتم التأثير عند صرف الفاتورة عن طريق أوامر الصرف المخزني. 
-	يمكن صرف الكمية المحددة في الفاتورة من مخازن مختلفة وفي أوقات مختلفة بعمل أكثر من صرف مخزني لنفس الفاتورة  حتى يتم صرف الكمية كاملة،  لذا لن يتم فحص الكميات المتوفرة في المخازن عند حفظ فاتورة المبيعات.
-	يجب عمل مرتجع المبيعات من مستند الصرف المخزني، ولن يسمح النظام بعمل مردود مبيعات من الفاتورة نفسها.
-	بما أن فاتورة الصرف المخزني لا تحوي عمود الكميات المجانية ، فإنه سيتم  تعطيل  عمل المتغير (ترحيل كلفة الكميات المجانية للمبيعات) – وسيتم ترحيل كلفة الكمية كاملة على حساب كلفة البضاعة المباعة.

ملاحظات:
-	هذا المتغير يتطلب إعادة تشغيل نظام العميل لتطبيق تأثير التغييرات.
-	هذا المتغير لاينطبق على فواتير نقاط البيع.
-	هذا المتغير معطل بشكل تلقائي.
]]>
     </content>
   </Entry>
   
   
      <Entry>
     <id>sales_item_discount</id>
     <title>تفعيل التخفيض على مستوى الصنف في الفاتورة</title>
     <content>
       <![CDATA[


عند تفعيل هذا المتغير سيتم السماح بعمل تخفيض على مستوى كل صنف في فاتورة المبيعات، حيث سيتم إظهار عمود التخفيض في جدول الأصناف في الفاتورة.
-	التخفيض يكون على سعر البيع لوحدة للصنف، ويمكن إدخال مبلغ التخفيض على سعر الوحدة. أو إدخال نسبة مئوية بإدخال النسبة المطلوبة متبوعة أو مسبوقة ب علامة % ، حيث سيقوم النظام آليا بتحويلها إلى المبلغ المقابل لهذه النسبة.
-	عند تفعيل هذا المتغير سيتم تعطيل عمود السعر
-	عند تعطيل هذا المتغير سيتم تفعيل عمود السعر إذا كان للمستخدم صلاحيات تغيير سعر البيع 
-	إذا  لم يكن للمستخدم صلاحية (تغيير سعر البيع) لن يتمكن المستخدم من عمل تخفيض على مستوى الصنف، حيث سيتم تعطيل عمود التخفيض على مستوى الصنف وكذلك عمود  سعر الصنف في الفاتورة.
-	إذا قام المستخدم بتغيير وحدة الصنف سيتم تصفير حقل التخفيض على مستوى الصنف.
-	إذا قام المستخدم بتغيير عملة الفاتورة سيتم تصفير حقل التخفيض على مستوى الصنف وعلى مستوى الفاتورة.
-	هذا المتغير لن يؤثر على حقل التخفيض على مستوى الفاتورة، ويمكن عمل تخفيض إضافي على مستوى الفاتورة بعد عمل تخفيض على مستوى الصنف.
-	تطبيق التغييرات على هذا المتغير يتطلب إعادة تشغيل نظام العميل.

ملاحظة: هذا المتغير سيطبق على فاتورة المبيعات النقدية والآجلة، وبالنسبة لمبيعات نقاط البيع فهناك متغير مخصص ضمن متغيرات نقاط البيع.

       
]]>
     </content>
   </Entry>


   <Entry>
     <id>sales_sys_opts19</id>
     <title>تفعيل النظام للبيع بالتجزئة والجملة</title>
     <content>
       <![CDATA[
عند تفعيل النظام للبيع بنظام التجزئة و الجملة في نفس الوقت:

- سوف يسمح النظام بالبيع بسعرين مختلفين وذلك حسب نوع الفاتورة 
-	سيتحول حقل سعر البيع في بيانات الأصناف إلى حقل سعر البيع بالتجزئة
- سيتحول حقل الحد الأدنى للسعر إلى سعر الجملة.
-	سيتم إضافة ثلاثة أنواع جديدة لفاتورة المبيعات (جملة/نقدي ، جملة آجل، جملة نقدي وآجل)
-	عند إختيار نوع الفاتورة أحد هذه الأنواع الثلاثة سيتم تطبيق سعر الجملة عند إختيار الصنف من القائمة، عدا ذلك سيتم إختيار سعر التجزئة. 
-	هذا ينطبق على فواتير المبيعات ، و لا ينطبق على نقاط البيع.
-	في نقاط البيع يتم البيع بسعر التجزئة فقط.
- هذا الخيار معطل بشكل تلقائي
]]>
     </content>
   </Entry>
   



  
   <Entry>
     <id>sales_sys_opts20</id>
     <title>إظهار تقارير المبيعات بالصافي بعد خصم مرتجع المبيعات</title>
     <content>
       <![CDATA[
     
تفعيل هذا الخيار يوجه النظام بدمج تأثير مرتجع المبيعات في تقاير المبيعات، حيث سيتم عرض صافي الكميات المباعة بعد خصم الكميات المرتجعة، وبالتالي ستتأثر تقارير المبيعات كلها سواء بالكميات أو مبالغ المبيعات أو هامش الربح، كلها ستكون بالصافي بعد خصم الكميات المرتجعة.
في التقارير التفصيلية سوف تظهر الكميات وإجمالي السعر وهامش الربح الناتجة عن مرتجع المبيعات بالسالب.
سوف يتم التأثير على تقارير مبيعات اليوم المحدد بفاتورة المرتجع، لذا لو كان المطلوب هو التأثير على تقارير اليوم الذي بتاريخ فاتورة المبيعات، يجب يكون تاريخ فاتورة المرتجع هو نفس تاريخ فاتورة المبيعات.

       ]]>
     </content>
   </Entry>

   <Entry>
     <id>sales_sys_opts32</id>
     <title>تفعيل متابعة المراحل في فواتير المبيعات</title>
     <content>
       <![CDATA[


       ]]>
     </content>
   </Entry>


   

   <Entry>
     <id>sales_sys_opts</id>
     <title>خيارات المبيعات</title>
     <content>
       <![CDATA[






]]>
     </content>
     
   </Entry>


   
   
   
   <Entry>
     <id>sys-cfg.es-sys-sales</id>
     <title>إعدادات نظام المبيعات</title>
     <content>
       <![CDATA[
       إعداد كل ما يتعلق بنظام و فواتير المبيعات و نقاط البيع
       
       تحت التطوير

]]>
     </content>
   </Entry>





   <Entry>
     <id>sys-cfg.es-sys-stock</id>
     <title>إعدادات نظام المخزون والأصناف</title>
     <content>
       <![CDATA[
       تحت التطوير

]]>
     </content>
    
   </Entry>

   


   <!-- This is a test -->

   <Entry>
     <id>fi_pro_mode</id>
     <title>إدارة الحسابات - الوضع المتقدم</title>
     <content>
       <![CDATA[
تفعيل هذا الخيار يسمح يتجاوز بعض القيود الخاصة بإدارة الحسابات، وهذا غير محبذ لأن يقوم النظام بفرض هذه القيود لغرض الحصول على أفضل أداء وللحيلولة دون وقوع أخطاء غير مقصودة. 

يمكن تفعيل هذا الخيار عندما يكون حاجة لـ  :
-	تجاوز شرط أن يكون الحساب الفرعي مطابقا للحساب الرئيسي من حيث الطبيعة والتقرير 
-	إنشاء حسابات رئيسية إجمالية (الحسابات التي ليس لها حساب رئيسي) أو حسابات المستوى الأول
- إنشاء الدليل المحاسبي من الصفر أو إستيراده من ملف 

هذا الخيار غير مفعل بشكل تقائي و يمكن تفعيل هذا الخيار (بواسطة المحاسب أو المختص) وبشكل مؤقت لتنفيذ غرض معين و بعد ذلك يفضل تعطيله.

]]>
     </content>
   </Entry>

   <Entry>
     <id>gl_hrchy_follow</id>
     <title>الحساب الفرعي يتبع الحساب الرئيسي من حيث الطبيعة والتقرير</title>
     <content>
       <![CDATA[
هذا الخيار مفعل بشكل تلقائي - وهو يوجه النظام على أن الحساب الفرعي يجب أن يتبع الحساب الرئيسي من حيث الطبيعة (مدين أو دائن ) وكذلك التقرير (الميزانية أو الأرباح والخسائر) .

     عند تعطيل هذا الخيار سوف يسمح النظام للمستخدم من تحديد طبيعة الحساب و نوع التقرير بغض النظر على الحساب الرئيسي الذي يتبعه.
     
     يجب مراعاة أن تعطيل هذا الخيار يجب أن تتم بواسطة المحاسب أو المختص فقط.
]]>
     </content>
   </Entry>
   
   
      <Entry>
     <id>wl_chg_acc_root</id>
     <title>عند نقل الحساب إلى فرع مختلف في نوع الحساب</title>
     <content>
       <![CDATA[
مثلا عند نقل الحساب من الأصول إلى الإلتزامات أو من المصروفات إلى الإيرادات

القيمة الأفتراضية: منع المستخدم وإظهار رسالة خطأ

يتم تغيير هذا الخيار إذا كانت حاجة فعلية وبواسطة المحاسب أو المختص
]]>
     </content>
   </Entry>

  
      <Entry>
     <id>post_sales_discount</id>
     <title>ترحيل تخفيضات ومسموحات المبيعات</title>
     <content>
       <![CDATA[
عند تفعيل هذا المتغير سيقوم النظام بترحيل قيمة الخصم إلى حساب مسموحات المبيعات وسيتم ترحيل الإيرادات بالمبلغ الإجمالي (شاملا مبلغ الخصم). 
وسيتم عمل قيد محاسبي بمبلغ الخصم
من ح/ مسموحات المبيعات
&nbsp;&nbsp;&nbsp; إلى ح/ الإيرادات

في حال عدم التفعيل سيتم ترحيل الإيرادات بالمبلغ الصافي.
الفائدة من تفعيل هذا المتغير هو معرفة إجمالي مبالغ الخصومات التي سمحت بها الشركة خلال الفترة المالية، حيث ستسجل هذه الخصومات كخسائر على الشركة خلال الفترة.  
هذا المتغير غير مفعل بشكل تلقائي. يتم تفعيل هذا المتغير بطلب من إدارة الحسابات.
]]>
     </content>
   </Entry>


   <Entry>
     <id>recv_check_posting</id>
     <title>طريقة ترحيل شيكات القبض</title>
     <content>
       <![CDATA[
هذا المتغير سوف يوجه النظام للطريقة التي يجب إستخدامها عند ترحيل شيكات القبض. والطريقة ستحدد متى سيتم التأثير على حساب العميل الذي أصدر الشيك وكذلك حساب البنك المكتوب عليه الشيك.
وهناك أربع طرق 
- بتاريخ المستند: سيتم ترحيل المستند مباشرة بتاريخ المستند (تاريخ إستلام الشيك) – حيث سيتم التأثير على حساب العميل والبنك بنفس تاريخ المستند.
-	بتاريخ الشيك: لن يقوم النظام بترحيل الشيك إلى أن يحين تاريخ إستحقاق الشيك حتى ولو حاول المستخدم ترحيله يدويا, وبالتالي لن يظهر أي تأثير على حساب العميل أو البنك أو في قيود اليومية، حتى يحين تاريخ إستحقاق الشيك. 
-	توسيط أوراق الدفع/القبض:  سيتم التأثير على حساب العميل مباشرة بتاريخ المستند، وسيتم التأثير آليا على حساب البنك بتاريخ استحقاق الشيك. وسيتم توسيط حساب أوراق القبض بين حركة العميل والبنك
-	إدخال الإستحقاق يدويا: نفس المتغير السابق، ويختلف بأن التأثير على حساب البنك سيتم يدويا عندما يقوم المحاسب بالتأكيد أنه تم صرف الشيك (من بيانات الشيك في سند القبض) – وذلك بموجب إشعار البنك، عندها سيقوم النظام بعمل التأثير على حساب البنك بتاريخ الإستحقاق.

القيمة التلقائية لهذا المتغير: بتاريخ المستند  

]]>
     </content>
   </Entry>


   <Entry>
     <id>pay_check_posting</id>
     <title>طريقة ترحيل شيكات الدفع</title>
     <content>
       <![CDATA[
هذا المتغير سوف يوجه النظام للطريقة التي يجب إستخدامها عند ترحيل شيكات الدفع. والطريقة ستحدد متى سيتم التأثير على حساب المورد الذي أصدر  له الشيك وكذلك حساب البنك المكتوب عليه الشيك.
وتستخدم نفس الطرق المشروحة في في طريقة ترحيل شيكات القبض، مع إختلاف إن التعامل يتم مع حساب المورد.

]]>
     </content>
     <linked>recv_check_posting</linked>
   </Entry>



   
      <Entry>
     <id>auto_close_net_income</id>
     <title>إغلاق حساب ملخص الدخل في حساب رأس المال آليا بعد إغلاق الأرباح والخسائر</title>
     <content>
       <![CDATA[
 عند تفعيل هذا الخيار:
 بعد إغلاق حسابات الأرباح و الخسائر في حساب ملخص الدخل يتم آليا إغلاق حساب ملخص الدخل في رأس المال.
بحيث يتم رفع رأس المال في حالة الربح و تخفيضه في حالة الخسارة.

     يجب تعطيل هذا الخيار إذا كان المؤسسة تنوي توزيع أرباح 
]]>
     </content>
   </Entry>
   
   
      <Entry>
     <id>col_swap_neg_bal</id>
     <title>في التقارير المالية قم بنقل الرصيد السالب للحساب إلى العمود الأخر المخالف لطبيعة الحساب - عدم إظهار أرصدة سالبة</title>
     <content>
       <![CDATA[
مثلا عندما يكون رصيد المورد بالسالب فسوف يتم إظهار رصيده في عمود المدين بدلا من إظهاره بالسالب في عمود الدائن

 يفضل ترك الخيار مفعلا
]]>
     </content>
   </Entry>
   
   
      <Entry>
     <id>fi_jrnl_line_amnt</id>
     <title>عرض مبلغ وعملة الحركة في تقرير اليومية العامة في حال إختلفت عن عملة الحساب</title>
     <content>
       <![CDATA[
تفعيل هذا الخيار سوف يضيف أعمدة إضافية في تقرير اليومية العامة تعرض بيانات المبلغ الأصلي للحركة في قيد اليومية في حال قام النظام بعمل تحويل إلى عملة الحساب وذلك عندما يتم إعداد النظام للسماح بالتحويل إلى عملة الحساب 

مثلا عند قبض مبلغ بالدولار من عميل ليس لديه حساب بالدولار 
]]>
     </content>
   </Entry>
     
     
        <Entry>
     <id>gl.post_rep_comm</id>
     <title>ترحيل عمولة المندوبين</title>
     <content>
       <![CDATA[
عند تفعيل هذا المتغير سيقوم النظام بترحيل عمولة المندوب إلى حساب المندوب عند ترحيل الفاتورة

حيث سيتم إضافة قيد مالي من ضمن فاتورة المبيعات ( من حـ/ عمولات المبيعات  - إلى حـ/ المندوب ).

مفعل بشكل تلقائي
]]>
     </content>
   </Entry>
   
   
   
      <Entry>
     <id>fi_max_doc_lines</id>
     <title>الحد الأعلى لعدد الأطراف في سندات القبض والصرف</title>
     <content>
       <![CDATA[
العدد الإفتراضي هو 20 
الحد الأقصى هو 50 في النسخة العامة

     و 999 في النسخة الإحترافية
]]>
     </content>
   </Entry>
   
   
      <Entry>
     <id>fi_max_gle_lines</id>
     <title>الحد الأعلى لعدد القيود في مستند القيود اليومية اليدوية</title>
     <content>
       <![CDATA[
العدد الإفتراضي هو 20 
الحد الأقصى هو 50 في النسخة العامة

     و 999 في النسخة الإحترافية
]]>
     </content>
   </Entry>
       
       
<Entry>
     <id>cash_rcpt_coll</id>
     <title>المحصل في سندات القبض</title>
     <content>
       <![CDATA[ 
هذا الخيار يسمح بتحديد المحصل في سندات القبض - بالإمكان تفعيل هذا الخيار بالنسبة للشركات التي لديها محصلين و تريد متابعة نشاط المحصل أو تريد تقييد عمولة للمحصل.

غير مفعل: لن يتم إظهار حقل المحصل في سند القبض
إختياري: سيتم إظهار المحصل و سيكون تحديد المحصل إختياري
إجباري: يجب تحديد المحصل في كل سند قبض و لن يتم حفظ السند بدون تحديد المحصل

يتم إضافة بيانات المحصلين من نفس شاشة المندوبين.. لذا بالإمكان أن يكون المحصل هو المندوب



]]>
     </content>
   </Entry>
   
       
        <Entry>
     <id>coll_comm_required</id>
     <title>يجب تحديد العمولة عند تحديد المحصل في السند</title>
     <content>
       <![CDATA[
إجبار المستخدم على إدخال عمولة المحصل و ذلك عند تحديد المحصل في سند القبض

سيقوم النظام بفحص المبلغ مع الحد الأعلى لنسبة العمولة المحددة للمحصل
]]>
     </content>
   </Entry>
   
   
      <Entry>
     <id>auto_coll_comm</id>
     <title>إحتساب آلي لعمولة المحصل في سند القبض في حال لم يتم تحديدها من قبل المستخدم</title>
     <content>
       <![CDATA[
سيقوم النظام بحساب عمولة المحصل آليا في حال لم يقوم المستخدم بتحديدها و ذلك بناءا على نسبة المحصل المحددة 

وسيقوم النظام بعرض المبلغ للمستخدم قبل الحفظ
     
]]>
     </content>
   </Entry>
   
   
      <Entry>
     <id>gl.post_coll_comm</id>
     <title>ترحيل عمولة المحصلين - إضافة مبلغ العمولة إلى حساب المحصل عند ترحيل سند القبض</title>
     <content>
       <![CDATA[
سيتم إضافة مبلغ العمولة إلى حساب المحصل عند ترحيل سند القبض 
]]>
     </content>
   </Entry>
   
   
        <!--   
   <Entry>
     <id></id>
     <title></title>
     <content>
       <![CDATA[

]]>
     </content>
   </Entry>
-->

   <Entry>
     <id>manual_doc_no</id>
     <title>السماح للمستخدم بإدخال أرقام المستندات و الفواتير يدويا</title>
     <content>
       <![CDATA[
       هذا الخيار يسمح للمؤسسة  بالتحكم بترقيم المستندات 
       
النظام بشكل تلقائي يقوم بعمل ترقيم تسلسلي آليا للمستندات، وبحسب نوع المستند. حيث أن كل نوع من أنواع المستندات له تسلسله الخاص، ويبدأ التسلسل للمستندات حسب الإعدادات  المحددة لبداية تسلسل الفواتير أو المستندات. ولكن إذا كان عمل المؤسسة يتطلب ترقيم يدوي للمستندات أو الفواتير يمكن السماح بهذا من خلال هذا المتغير. ولهذا المتغير ثلاث قيم 
-	غير مفعل: سيقوم النظام بعمل ترقيم آلي للمستندات
-	إختياري: سوف يسمح النظام للمستخدم بإدخال رقم المستند وفي حال أن المستخدم ترك حقل رقم المستند فارغا فإن النظام سيقوم بعمل رقم تسلسلي للمستند بعد آخر مستند موجود في النظام.
-	إجباري: إجبار المستخدم على إدخال رقم المستند يدويا. ولن يقوم النظام بعمل ترقيم آلي.  
ملاحظات:
-	سيرفض النظام في حال ان المستخدم قام بإدخال رقم موجود مسبقا
-	في النسخة الإحترافية يمكن إعداد كل مستند بشكل مختلف وبحيث يمكن عمل تسلسل للمستندات حسب الفروع أو الصندوق أو المخزن.

]]>
     </content>
   </Entry>


   <Entry>
     <id>en_doc_crncy_exchg</id>
     <title>السماح بتغيير سعر التحويل في المستندات</title>
     <content>
       <![CDATA[
       عند تفعيل هذا المتغير سيقوم النظام بإظهار حقل سعر الصرف في الفاتورة أو المستند بشرط أن يكون للمستخدم صلاحية تغيير سعر الصرف على مستوى هذا النوع من المستندات
       فمثلا يمكن السماح للمستخدم بتغيير سعر الصرف في المشتروات فقط..
       
النظام يسمح بالتعامل بعملة محلية واحدة وبعدد غير محدود من العملات الأجنبية، ويجب تحديد سعر التحويل للعملات الأجنبية إلى المحلية على مستوى النظام. ومع كل عملية مالية بالعملة الأجنبية يقوم النظام بعمل المقابل المحلي لهذه العملية وذلك بإستخدام سعر الصرف المحدد في بيانات العملة. ولكن في بعض الحالات قد يتطلب الأمر تنفيذ عملية معينة بسعر صرف مختلف. 
عند التعامل مع المستندات يقوم النظام بإختيار العملة المحلية تلقائيا و يسمح للمستخدم بتحديد عملة أجنبية. حيث يقوم النظام بإعتماد سعر الصرف الحالي للعملة الأجنبية.  ولكن إذا كان هذا المستند يتطلب سعر صرف مختلف فيجب تفعيل هذا المتغير. 
]]>
     </content>
     <linked>doc_crncy;doc_crncy_exrate</linked>
   </Entry>
   
   
     
      <Entry>
     <id>en_doc_multi_store</id>
     <title>تعدد المخازن في الفاتورة الواحدة</title>
     <content>
       <![CDATA[
هذا المتغير يسمح بالتعامل مع أكثر من مخزن في فاتورة واحدة، حيث  يمكن تحديد المخزن على مستوى الصنف الواحد في الفاتورة. 
وهذا سيمكن المستخدم من البيع من أكثر مخزن في نفس الفاتورة. ونفس الحال بالنسبة للمشتروات أو التوريد.
في حال أن المستخدم ترك حقل المخزن على مستوى الصنف فارغا، فإن النظام سوف يستخدم المخزن المحدد على مستوى الفاتورة بشكل تلقائي لهذا الصنف. 
هذا المتغير يطبق على أنواع المستندات: المبيعات، المشتروات، والمخزون.. بإستثناء بعض المستندات التي ليس من المنطقي استخدام تعدد المخازن مثل طلبات البيع وعروض الأسعار.
يجب أن يكون للمستخدم صلاحية على المخزن الذي سيتم إختياره. سواء على مستوى الفاتورة أو الصنف.
القيمة التلقائية لهذا المتغير: غير مفعل

]]>
     </content>
         <linked>store_id</linked>
   </Entry>


   <Entry>
     <id>enable_item_memo</id>
     <title>إظهار حقل البيان على مستوى كل صنف في الفاتورة</title>
     <content>
       <![CDATA[
هذا المتغير سوف يضيف عمود البيان في جدول الأصناف في الفاتورة، وسوف يسمح للمستخدم بإضافة البيان على مستوى الصنف الواحد.
بحيث يمكن إضافة تفصيل أكثر عن نوع الصنف. هذا المتغير مفيد جدا للمؤسسات الخدمية والتي تقدم خدمات مثل المشتشفيات أو الشركات العاملة في مجال فحص وإصلاح الأجهزة مثلا.. 


هذا المتغير غير مفعل بشكل تلقائي.

]]>
     </content>
   </Entry> 
   
   
   <Entry>
     <id>wl_chk_doc_post_on_save</id>
     <title>عند فشل فحص إمكانية ترحيل المستند قبل الحفظ</title>
     <content>
       <![CDATA[
عند تفعيل هذا المتغير سيقوم النظام بعملية فحوصات إضافية للتأكد من إمكانية ترحيل هذا المستند، 
حيث سيقوم بفحص الحدود العليا والدنيا للحسابات المتأثرة، مثل حد المديونية على العملاء أو الموردين. وكذلك الأرصدة السالبة وغيرها. ويفضل أن يتم تفعيل هذا المتغير. 
لن يسمح النظام بحفظ المستند أو الفاتورة في حال أن فحص إمكانية الترحيل فشل، وبالتالي قد يشكل تفعيل هذا المتغير إعاقة للعمل في بعض الأحيان.. وقد يتطلب ترحيل المستندات السابقة..

هذا المتغير مفعل بشكل تلقائي.

]]>
     </content>
   </Entry>

	 <Entry>
		 <id>max_doc_age_to_cncl_post</id>
		 <title>منع إلغاء ترحيل المستندات التي اقدم من مدة معينة</title>
		 <content>
			 <![CDATA[
هذا المتغير يسمح للمؤسسة بفرض قيود زمنية تمنع إلغاء ترحيل المستندات القديمة. 	 
حيث يمكن تحديد المدة السابقة (قبل تاريخ المستند) باليوم التي  يسمح النظام بإلغاء ترحيل المستندات خلالها، وأي مستند أقدم من هذه المدة سوف يرفض النظام إلغاء ترحيله .

عند تحديد المدة (0) سوف يسمح النظام بالغاء ترحيل مستندات اليوم الحالي فقط.
عند تحديد المدة (1) سوف يسمح النظام بالغاء ترحيل مستندات اليوم والأمس فقط. وهكذا
القيمة التلقائية لهذا المتغير (30) يوما

- يمكن إعطاء المستخدم صلاحية (إلغاء الترحيل للمستندات القديمة) لتجاوز هذا المنع بشرط أن تكون الفترة مفتوحة و الشهر مفتوح

]]>
		 </content>
	 </Entry>

	 <Entry>
		 <id>manual_doc_date</id>
		 <title>السماح للمستخدم بإدخال تاريخ المستندات و الفواتير يدويا</title>
		 <content>
			 <![CDATA[ 
هناك ثلاث خيارات لهذا المتغير:
- غير مفعل: سوف يقوم النظام بتعيين تاريخ المستند آليا  (حسب تاريخ النظام) و لن يسمح للمستخدم بتغييره			 
- إختياري: سوف يقوم النظام بتعيين تاريخ المستند آليا مع السماح للمستخدم بتغييره 
- إجباري: لن يتم تحديد تاريخ إفتراضي للمستند، و سيكون لزاما على المستخدم تحديد التاريخ يدويا

يجب أن يكون للمستخدم صلاحية (تغيير تاريخ المستند) عدا ذلك لن يسمح النظام للمستخدم بتغيير تاريخ المستند، و سوف يتم تعيين التاريخ آليا.
هذا الخيار لايؤثر على تاريخ إضافة المستند.

القيمة الإفتراضية: إختياري

]]>
		 </content>
	 </Entry>


	 <Entry>
		 <id>biz_day_start_hour</id>
		 <title>اليوم المالي يبدأ الساعة</title>
		 <content>
			 <![CDATA[
هذا المتغير يسمح للمؤسسة بتحديد ساعة بداية اليوم، أي الساعة التي يعتمد فيها النظام يوم جديد.
وهذا يؤثر على التحديد الآلي لتاريخ المستند فقط. و لن يؤثر على تاريخ أو وقت إضافة أو تعديل المستند أو غيرها.

مثلا إذا تم تحديد الساعة (6) أي السادسة صباحا، فإن المستندات التي سوف تضاف من السادسة صباحا إلى اليوم التالي السادسة صباحا سوف تكون بنفس التاريخ بغض النظر عن تاريخ النظام

]]>
		 </content>
	 </Entry>
	 

   <Entry>
     <id>auto_post_after_save</id>
     <title>ترحيل المستندات آليا بعد الحفظ مباشرة</title>
     <content>
       <![CDATA[
عند تفعيل هذا المتغير سيقوم النظام بشكل آلي  بمحاولة ترحيل الفاتورة بعد الحفظ مباشرة، 
في حال فشلت عملية الترحيل فإن ذلك لا يؤثر على عملية حفظ الفاتورة، وتظهر رسالة تنبيه للمستخدم تفيد بفشل عملية الترحيل.
هذا المتغير ينطبق على المستندات المبيعات والمشتروات والمخازن. ولا ينطبق على المستندات المالية (القبض والصرف والقيود اليومية)
إذا كان المستند يتطلب موافقة أو تحقيق  فلا يتم ترحيله آليا بعد الحفظ. ويتطلب ترحيل يدوي عن طريق زر الترحيل أو من شاشة الترحيل بعد الحصول على الموافقات المطلوبة.

هذا المتغير مفعل بشكل تلقائي.

]]>
     </content>
   </Entry>


   <Entry>
     <id>enable_doc_edit</id>
     <title>السماح بتعديل المستندات قبل الترحيل</title>
     <content>
       <![CDATA[
هذا المتغير يسمح للمستخدمين المخولين بتعديل المستندات التي لم ترحل. عند تعطيل هذا المتغير فإن النظام سيقوم بإزالة زر التعديل من المستندات وبالتالي لن يتمكن المستخدم من تعديل أي مستند حتى ولو كان لديه صلاحيات.
هذا المتغير غير مفعل بشكل تلقائي.

إذا قد كان تم طباعة المستند يجب أن يكون للمستخدم صلاحية : التعديل/الحذف بعد الطباعة
]]>
     </content>
   </Entry>


   <Entry>
     <id>enable_doc_delete</id>
     <title>السماح بحذف المستندات قبل الترحيل</title>
     <content>
       <![CDATA[
هذا المتغير يسمح للمستخدمين المخولين بحذف المستندات التي لم ترحل.
 عند تعطيل هذا المتغير فإن النظام سيقوم بإزالة زر الحذف من المستندات وبالتالي لن يتمكن المستخدم من حذف أي مستند حتى ولو كان لديه صلاحيات.
هذا المتغير غير مفعل بشكل تلقائي.

إذا كان قد تم طباعة المستند يجب أن يكون للمستخدم صلاحية : التعديل/الحذف بعد الطباعة
]]>
     </content>
   </Entry>


   <Entry>
     <id>enable_cancel_post</id>
     <title>السماح بإلغاء الترحيل للمستندات المرحلة</title>
     <content>
       <![CDATA[
هذا المتغير يسمح للمستخدمين المخولين بإلغاء ترحيل المستند (عكس القيود المالية الخاصة بالمستند). وعند تعطيل هذا المتغير فإنه لا يمكن للمستخدمين إلغاء ترحيل المستندات حتى ولو كان لديهم الصلاحيات. عند ترحيل المستند فإن النظام يمنع أي عمليات على هذا المستند بالحذف أو التعديل. ولو ظهرت حاجة لتعديل المستند أو حذفه يجب إلغاء الترحيل لهذا المستند أولا.  
هذا المتغير غير مفعل بشكل تلقائي.

إذا قد كان تم طباعة المستند يجب أن يكون للمستخدم صلاحية : إلغاء الترحيل بعد الطباعة
]]>
     </content>
   </Entry>
   
   
      <Entry>
     <id>gl.en_branch</id>
     <title>إستخدام الفروع في المستندات</title>
     <content>
       <![CDATA[
هذا المتغير يحدد إمكانية إستخدام حقل الفرع في المستندات
-	غير مفعل: سيقوم النظام بإخفاء الحقل من المستندات.
-	إختياري: سيقوم النظام بإظهار الحقل في المستندات و لكن إختياره  لن يكون إجباريا .
-	إجباري:سوف يقوم النظام بإظهار الحقل في المسندات  ويجبر المستخدم على تحديد فرع لكل مستند. ولن يسمح بحفظ المستند مالم يتم تحديد الفرع. 
سيقوم النظام بشكل تلقائي بإختيار الفرع الخاص بالمستخدم. ويمكن للمستخدم تغيير الفرع إلى أي فرع آخر بشرط أن يكون لديه صلاحية على هذا الفرع.
استخدام الفروع  بشكل إجباري في المستندات سوف يوفر آلية لفصل مستندات كل فرع على حدة. و سوف يوفر إمكانية الحصول على تقارير مالية أو إحصائية أو تفصيلية أو تحليلية على مستوى الفرع الواحد.

النظام يقوم بإغلاق الحسابات على مستوى الفروع والمشاريع والأنشطة ومراكز التكلفة

عند جعل الفرع إجباريا في المستندات :
- فإنه يمكن الحصول على تقارير  ميزان مراجعة و ميزانية عمومية وأرباح وخسائر على مستوى الفرع



]]>
     </content>
         <linked>branch</linked>
   </Entry>
   
   
   
      <Entry>
     <id>gl.en_proj</id>
     <title>إستخدام المشاريع  في المستندات</title>
     <content>
       <![CDATA[
يسمح النظام بربط المستندات بالمشاريع. ويمكن الحصول على تقارير مالية أو إحصائية حسب المشاريع وكذلك فرز المستندات الخاصة بمشروع معين. 
يجب أن يكون للمستخدم صلاحيات على المشروع  المحدد في المستند.

يمكن تغيير مصطلح المشروع إلى أي شي آخر مثل العملية أو الشحنة وهكذا بحيث يتناسب وعمل المؤسسة

النظام يقوم بإغلاق الحسابات على مستوى الفروع والمشاريع والأنشطة ومراكز التكلفة
]]>
     </content>
        <linked>proj_id</linked>
   </Entry>
   
   
      <Entry>
     <id>gl.en_actvty</id>
     <title>إستخدام الأنشطة في المستندات</title>
     <content>
       <![CDATA[
يمكن إستخدام الأنشطة مع المشاريع للحصول على تقارير تحليلة أعمق وكذلك لتحقيق رقابة أدق. 
يجب أن يكون للمستخدم صلاحيات على النشاط المحدد في الفاتورة.

ملاحظة: يمكن إستخدام كل من المراكز والمشاريع والأنشطة بشكل مترادف 

النظام يقوم بإغلاق الحسابات على مستوى الفروع والمشاريع والأنشطة ومراكز التكلفة

]]>
     </content>
        <linked>actvty_id</linked>
   </Entry>


   

   <Entry>
     <id>gl.en_costcontrol</id>
     <title>استخدام مراكز التكلفة في المستندات</title>
     <content>
       <![CDATA[
يسمح النظام بتحديد مراكز التكلفة في المستندات وذلك للحصول على تقارير مالية وتحليلية حسب المراكز. ولتحقيق رقابة أكثر على المراكز من الناحية المالية.
ويمكن إعدادا النظام بحيث أن يكون إستخدام مراكز التكلفة في المستندات غير مفعل أو إختياري أو إجباري وذلك حسب حاجة المؤسسة.
يجب أن يكون للمستخدم صلاحيات على مركز التكلفة المحدد في المستند.

مراكز التكلفة يمكن أن تستخدم لعدة أغراض حسب تصور وحاجة المؤسسة

النظام يقوم بإغلاق الحسابات على مستوى الفروع والمشاريع والأنشطة ومراكز التكلفة
]]>
     </content>
     <linked>cc_no</linked>
   </Entry>


   <Entry>
     <id>use_cc_on_pls_only</id>
     <title>حصر استخدام مراكز التكلفة على حسابات الأرباح والخسائر فقط</title>
     <content>
       <![CDATA[
في حال تفعيل هذا الخيار سيتم استخدام مراكز التكلفة مع حسابات الأرباح والخسائر فقط.. في هذه الحالة إن أرصدة المراكز ستمثل المصروفات أو الإيرادات الخاصة بالمركز

عند تعطيل الخيار سيتم استخدام المراكز مع كل الحسابات - و بالتالي فإن الأرصدة النهائية للمراكز كلها ستكون صفرا - و لكن يمكن الحصول على تقارير تحليله للحسابات حسب المراكز..
]]>
     </content>
   </Entry>



   <Entry>
     <id>es.approve_level</id>
     <title>عدد الموافقات المطلوبة للمستند الواحد</title>
     <content>
       <![CDATA[
إذا كانت الشركة تريد تطبيق نظام الموافقة والتحقق من المستندات قبل ترحيلها، فإن النظام يسمح بتحديد ثلاث مستويات للموافقة. وكل مستوى يتطلب صلاحية مختلفة. وذلك لتمكين الشركة من توزيع الصلاحيات على عدة موظفين لتحقيق أقصى درجة من الرقابة.
في النسخة الإحترافية يمكن تحديد هذا المتغير على مستوى المستند. 
أي مثلا يمكن إعداد النظام بأن يطلب ثلاث موافقات لفاتورة المشتروات. بينما يتطلب موافقة واحدة للمبيعات الآجلة ولا يتطلب أي موافقة على الفواتير النقدية.

]]>
     </content>
   </Entry>



   <Entry>
     <id>es.no_items</id>
     <title>إصدار فواتير بدون أصناف. إخفاء جدول الأصناف في الفواتير - الفواتير المختصرة</title>
     <content>
       <![CDATA[
هذا المتغير سيتيح للشركة إصدار فواتير مبيعات ومشتروات نقدية وآجلة بدون أصناف. 
تم إضافة هذه الميزة لتغطية حاجة بعض الشركات التي ليس لديها مخزون أو من الصعب عليها متابعة المخزون في الفواتير. أو أنها تريد متابعة الأمور المالية والمديونيات فقط. 

وبإمكان الشركة عمل جرد دوري لتقييم المخزون وإضافة قيمة المخزون في نظام الإستاذ العام. للحصول على تقارير مالية مضبوطة. ويجب عمل هذا الجرد في نهاية الفترة المحاسبية..
عند تفعيل هذا المتغير سوف يقوم النظام بـ: 
-	إزالة جدول الأصناف، حقل المخزن،  من فواتير المبيعات والمشتروات.
-	تعطيل نظام المخزون (تعطيل نظام المخزون - المؤسسة تقدم خدمات فقط)
-	سيتم إزالة  جدول الأعباء الإضافية من فاتورة المشتروات.

ويفضل أن يتم توقيف حساب المخزون السلعي (لتجنب أي عمليات عليه بالخطأ).

]]>
     </content>
   </Entry>


   <Entry>
     <id>accountant_mode</id>
     <title>تفعيل خيارات المحاسب</title>
     <content>
       <![CDATA[
يتم تفعيل هذا المتغير عندما يكون المحاسب هو من يقوم بإدخال فواتير المبيعات والمشتروات في النظام، وغالبا هذا يكون في الشركات التي تقوم تعمل بالمستندات الورقية وتقوم بتقييد هذه المستندات الورقية في النظام لاحقا. 

هذا المتغير سيقوم بتفعيل بعض المتغيرات في النظام، 
-	السماح للمستخدم بتحديد الصندوق يدويا في الفواتير. بشرط أن يكون للمستخدم صلاحيات على هذا الصندوق.
-	تفعيل الوضع المتقدم في مسندات الصرف بحيث يتم تقييد الملبغ على أكثر من حساب في نفس الوقت.
-	تفعيل الوضع المتقدم في مسندات القبض بحيث يتم تقييد الملبغ لأكثر من حساب في نفس الوقت.
-	السماح للمستخدم بتغيير طريقة ترحيل الشيكات في مستندات الصرف والقبض.
-	عند إنشاء حساب مالي جديد سيتم السماح للمستخدم بإدخال أرقام الحسابات يدويا.

عند تعطيل هذا المتغير:
-	في الفواتير سيقوم النظام آليا بإختيار الصندوق المحدد للمستخدم ولن يسمح له بتغييره لذا يجب تحديد الصندوق في بيانات المستخدم الإضافية.
-	سيتم إظهار مستندات الصرف و القبض بالشكل المبسط . 
-	سيتم إعتماد طريقة ترحيل الشيكات المعتمدة في إعدادات النظام ولن يسمح للمستخدم بتغييرها.

ويفضل أن يتم تفعيل هذا المتغير و يمكن حصر المستخدم على صندوق معين من خلال الصلاحيات.

-	هذا المتغير مفعل بشكل تلقائى.

]]>
     </content>
   </Entry>
   
   
      <Entry>
     <id>enable_easy_mode</id>
     <title>تفعيل الوضع المبسط</title>
     <content>
       <![CDATA[
تفعيل هذا المتغير سوف يسهل العمل على النظام بإختيار القيم التلقائية لبعض الحقول في المستندات.
-	هذا المتغير مفعل بشكل تلقائى.

]]>
     </content>
   </Entry>
   
      <Entry>
     <id>enable_pay_networks</id>
     <title>تفعيل شبكات الدفع</title>
     <content>
       <![CDATA[
تفعيل هذا المتغير سوف يوفر 
-	إمكانية التعامل مع خدمات الدفع المقدمة عبر البنوك والصرافين 
-	إمكانية التعامل مع  شركات التأمين الطبية.
-	 إمكانية التعامل مع كروت الإئتمان (فيزا – ماستر وغيره).

في فواتير المبيعات سوف يسمح النظام بقبول دفعات نقدية بالإضافة إلى قبول مبالغ عبر شبكات الدفع في نفس الفاتورة.

لإستكمال إعدادت شبكة الدفع يجب أن يتم فتح حساب للشبكة المحددة في النظام وذلك تحت الحساب الرئيسي (البنوك). ويمكن تغيير الحساب الرئيسي للبنوك إلى (البنوك/شبكات الدفع).

]]>
     </content>
   </Entry>
   
   
      <Entry>
     <id>branch_isolation</id>
     <title>عزل الفروع</title>
     <content>
       <![CDATA[
الهدف من هذا الخيار هو جعل كل فرع  يطلع على قائمة المستندات والفواتير الخاصة به فقط ،  ومنع الفروع  من الإطلاع على قائمة المستندات والفواتير التي لا تتبعها.
عندما يقوم المستخدم بعرض مستند معين، يقوم النظام بالتأكد من أن للمستخدم الصلاحية على هذا الفرع، و يمنع عرض المستند إذا لم يكن للمستخدم الصلاحية على الفرع، ولكن هذه الصلاحية لاتنطبق على عرض (قائمة) المستندات، سواء عن طريق عرض القائمة مباشرة أو البحث عن المستندات.  
عند تفعيل هذا الخيار، سوف يعرض النظام قائمة المستندات التي تخص فرع المستخدم فقط.. 
يمكن تحديد فرع المستخدم من: (بيانات المستخدم  - البيانات الإضافية للمستخدم - الفرع الإفتراضي)، وعند ترك الفرع الإفتراضي للمستخدم فارغا، سوف يسمح النظام للمستخدم بعرض قائمة المستندات الخاصة بكل الفروع، ولكنه سوف يتمكن فقط من عرض المستندات التي له صلاحية على فروعها.. 
يمكن تحديد الفروع التي للمستخدم صلاحيات عليها من خلال: (بيانات المستخدم  - البيانات الإضافية للمستخدم - الفروع).

هذا الخيار لن يتم تطبيقه على قيود اليومية العامة، أو كشف الحساب، وسيكون بإمكان المحاسب عرض القيود المالية التي تتبع كل الفروع.

]]>
     </content>
   </Entry>
   
   
         
   <Entry>
     <id>es_tax_policy</id>
     <title>ضريبة القيمة المضافة</title>
     <content>
       <![CDATA[
هذا الخيار يسمح بتفعيل إحتساب القيمة المضافة على المبيعات بشكل آلي. و بالامكان توجيه النظام لإحتساب الضريبة على المبلغ الصافي أو إجمالي الفاتورة قبل الخصم

عند تفعيل ضريبة القيمة المضافة:
- سيتم إضافة عمود في جدول الأصناف باسم الضريبة سيحوي إجمالي الضريبة على الصنف
- سيتم إضافة حقل في الفاتورة بحيث يحوي إجمالي الضريبة على الفاتورة
- سيتم إضافة خيار مفعل آليا في الفاتورة : إحتساب ضريبة القيمة المضافة ، و بإمكان المستخدم تعطيله بحيث لا يقوم النظام بإحتساب الضريبة
- عند ترحيل الفاتورة سيتم ترحيل مبلغ الضريبة إلى حساب مصلحة الضرائب
- بالنسبة لفاتورة المشتروات و في حال قام المستخدم بتفعيل إحتساب الضريبة على الفاتورة سيتم الخصم من حساب مصلحة الضرائب
- سيتم طباعة مبلغ الضريبة على مستوى وإجمالي الفاتورة

لإستكمال تفعيل الضريبة يجب
- تحديد نسبة الضريبة الثابتة لكل الأصناف أو..
- تحديد نسبة الضريبة على مستوى الصنف (الضريبة الإنتقائية) و يمكن إستخدام التعديل الجماعي لتحديد النسبة على عدة أصناف
- يجب تحديد حساب مصلحة الضرائب في الحسابات المخصصة
     
]]>
     </content>
   </Entry>

   <Entry>
     <id>en_installs</id>
     <title>تفعيل الأقساط على الفواتير الآجلة  </title>
     <content>
       <![CDATA[
تفعيل متابعة الأقساط في فواتير المبيعات والمشتروات يسمح للشركة بمعرفة كل فاتورة وكم الأقساط المتبقية عليها. وكذلك الفواتير والدفعات المستحقة للسداد. 

تفعيل نظام الأقساط سوف يضيف بيانات خاصة بالأقساط في فاتورة المبيعات والمشتروات مثل اجمالي المبلغ الواصل و إجمالي المبلغ الباقي و كذلك تاريخ آخر قسط - وتاريخ  استحقاق القسط القادم 
- سوف يسمح للمستخدم بتحديد تاريخ استحقاق اول قسط عند إضافة الفاتورة
- السماح للمستخدم  بإستعراض كل الأقساط المدفوعة للفاتورة مع ارقام السندات التي استخدمت في دفع الأقساط
- من شاشة سند القبض والصرف يمكن معرفة الفواتير والأقساط التي تم تسديدها بواسطة السند
- تفعيل تقرير الدفعات المستحقة  
- و يمكن إعداد النظام لعرض تنبيه للمستخدم عند الدخول للنظام بالدفعات المستحقة على العملاء أو للموردين
- نظام الأقساط يطبق تأثيرات قسائم التخفيضات الحاصلة على الفاتورة حتى بعد إصدارها و ترحيلها و يتم إظهار التخفيضات في قائمة الأقساط الواصلة
- نظام التقسيط يطبق تأثير المرتجعات على الفاتورة - ويتم إظهار المرتجعات في قائمة الأقساط الواصلة

]]>
     </content>
     <linked>due_date;last_pay_date;paid_installs;total_paid;unpaid_installs</linked>
   </Entry>
   

        <Entry>
     <id>en_installs_auto</id>
     <title>توزيع التسديدات آليا على الأقساط</title>
     <content>
       <![CDATA[
عند تفعيل هذا الخيار سوف يقوم النظام بتسديد الأقساط على الفواتير آليا - بحيث يتم تسديد الفواتير الأقدم أولا.

يمكن للمستخدم تسديد أقساط فاتورة معينة و ذلك بإنشاء سند القبض من فاتورة المبيعات نفسها من قائمة الأدوات الخاصة بالفاتورة: إستلام مبلغ من عميل

]]>
     </content>
   </Entry>
   
   
      <Entry>
     <id>biz_sys_features5</id>
     <title>عدم السماح بتعديل أو إلغاء ترحيل المستند إذا كان لديه مستندات مرتبطة</title>
     <content>
       <![CDATA[
هذا الخيار مفعل آليا وهو يمنع المستخدم من تعديل أو إلغاء ترحيل المستند الذي لديه مستندات مرتبطة

إذا تم تعطيل هذا الخيار سوف يسمح النظام بتعديل المستند او إلغاء ترحيله

بشكل عام لن يسمح النظام بحذف المستند الذي لديه مستندات مرتبطة

لا يفضل تعطيل هذا الخيار، و يمكن تعطيله مؤقتا عند الضرورة
     
]]>
     </content>
        <linked>doc_date</linked>
   </Entry>
   
   
      <Entry>
     <id>biz_sys_features16</id>
     <title>عند نسخ المستند، استخدم نفس تاريخ المستند السابق</title>
     <content>
       <![CDATA[
هذا الخيار يوجه النظام لتحديد القيمة الإفتراضية لتاريخ المستند المنسوخ من مستند سابق 

في حال تفعيل هذا الخيار سيتم إعتماد تاريخ المستند الأصلي 

في حال تعطيل الخيار سيتم إستخدام تاريخ اليوم

]]>
     </content>
   </Entry>


	 <Entry>
		 <id>biz_sys_features18</id>
		 <title>السماح بإنشاء مستندات بتاريخ مستقبلي</title>
		 <content>
			 <![CDATA[
عند تفعيل (تأشير) هذا الخيار سوف يسمح النظام بإنشاء مستندات بتاريخ في المستقبل، 
في حال تعطيل الخيار سوف يرفض النظام بإنشاء أي مستندات بعد تاريخ اليوم الحالي.

]]>
		 </content>
	 </Entry>

	 <Entry>
		 <id>biz_sys_features19</id>
		 <title>السماح بطباعة المسودات</title>
		 <content>
			 <![CDATA[
إذا تم تأشير المستند على إنه مسودة (غير نهائي) فإن النظام يمنع طباعة المستند. يمكن تفعيل هذا الخيار  للسماح بطباعة المستند إذا تم تأشيره على إنه مسودة.

]]>
		 </content>
	 </Entry>

	 <Entry>
		 <id>biz_sys_features20</id>
		 <title> رقم المستند يتضمن معرف التسلسل للمستند - السماح بتكرار نفس التسلسل لكل معرف</title>
		 <content>
			 <![CDATA[
إذا تم إعداد تسلسل مخصص للمستند بحيث يكون  مثلا حسب ( الفرع أو السنة أو الشهر أو التاريخ أو الفئة أو الصندوق أو المخزن) فإن النظام يقوم بتحديد تسلسل مختلف للمستند حسب البيانات المحددة
و لكن النظام لن يسمح أن يتكرر نفس رقم المستند في فرعين مختلفين مثلا. 
يمكن تفعيل هذا الخيار للسماح أن يتكرر رقم المستند حسب الفئة أو الفرع أو التاريخ أو السنة ..

مثلا:
- أن يكون لكل فرع تسلسل مستندات مستقل تماما يبدأ من الصفر.
- إذا قررت المؤسسة أن يتم تصفير  تسلسل فواتير المبيعات سنويا
- إذا قررت المؤسسة أن تفصل ترقيم فواتير المبيعات النقدية عن الآجلة
- إذا كان هناك حاجة لتصفير تسلسل فواتير المبيعات يوميا لسهولة المتابعة مثل المطاعم و شركات الخدمات
- الجهات التي تريد أن تكون سندات القبض مسلسلة من الصفر حسب كل صندوق 
وغيرها

]]>
		 </content>
	 </Entry>


	 <Entry>
		 <id>biz_sys_features21</id>
		 <title>السماح للمستخدم بتعطيل إحتساب الضريبة في فواتير المبيعات</title>
		 <content>
			 <![CDATA[
تفعيل هذا الخيار سوف يسمح للمستخدم بتعطيل إحتساب الضريبة على مستوى الفاتورة - غير محبذ

]]>
		 </content>
	 </Entry>
	 

	 <Entry>
     <id>biz_sys_features4</id>
     <title>إعطاء المستخدم صلاحية عرض المبالغ إذا لدية صلاحية الإضافة أو التعديل</title>
     <content>
       <![CDATA[
كل المستندات لها صلاحية مخصصة لعرض المبالغ. و يجب أن يكون للمستخدم هذه الصلاحية حتى يتمكن من عرض المبالغ.
كي يتمكن المستخدم من إضافة المستند يجب أن يكون لديه صلاحية عرض المبالغ في هذا المستند.
تفعيل هذا الخيار سوف يمنح المستخدم صلاحية عرض المبالغ آليا إذا كان لديه صلاحية الإضافة والتعديل - إذا لم يتم تفعيل هذا الخيار يجب أن يتم إضافة هذه الصلاحية للمستخدم يدويا. 
     
]]>
     </content>
   </Entry>
   
      <Entry>
     <id>biz_sys_features17</id>
     <title>طباعة نسخة المخزن او المباشر عند الطباعة على الطابعات الصغيرة فقط</title>
     <content>
       <![CDATA[
عدم طباعة نسخة العامل (المباشر أو عامل المخزن) عند الطباعة على الطابعات الكبيرة A4  أو اكبر منها
]]>
     </content>
   </Entry>
   

 
   
      <Entry>
     <id>en_sales_free_qty</id>
     <title> السماح بالكميات المجانية في المبيعات</title>
     <content>
       <![CDATA[
عند تفعيل هذا الخيار سوف يسمح النظام بصرف كميات مجانية في فواتير المبيعات مع مراعاة
- يجب أن يكون للمسخدم صلاحية: صرف كميات مجانية أثناء البيع
- يجب أن يتم بالسماح بصرف كميات مجانية من الصنف المحدد و ذلك من إعدادات الصنف

التاثيرات:
- سيتم إظهار عمود (كمية مجانية) في جدول الأصناف في فاتورة المبيعات
- كلفة الكميات المجانية يمكن أن ترحل على حساب الترويج او كلفة البضاعة المباعة حسب الإعدادات
- الكمية المجانية سوف تؤثر على هامش الربح الخاص على مستوى الصنف والفاتورة
- عند عمل مرتجع على صنف بكمية مجانية فإن النظام يأخذ بالحسبان الكمية المجانية بحيث يتم حساب سعر بيع الوحدة الفعلي وليس السعر المحدد في الفاتورة

هذا المتغير غير مفعل  بشكل تلقائي
]]>
     </content>
   </Entry>
   
         <Entry>
     <id></id>
     <title></title>
     <content>
       <![CDATA[

]]>
     </content>
   </Entry>
   
         <Entry>
     <id></id>
     <title></title>
     <content>
       <![CDATA[

]]>
     </content>
   </Entry>
   
      <!--   
   <Entry>
     <id></id>
     <title></title>
     <content>
       <![CDATA[

]]>
     </content>
   </Entry>
-->
   
     <Entry>
     <id>es.no_stock</id>
     <title>المؤسسة تقدم خدمات فقط - تعطيل نظام المخزون السلعي</title>
     <content>
       <![CDATA[
هذا المتغير يوجه النظام إلى تعطيل نظام المخزون، 
يتم تفعيل هذا المتغير للمؤسسات التي ليس لها مخزون أو لن تستخدم نظام المخزون، وبالتالي فإن النظام سوف يفترض أن المؤسسة تقدم خدمات فقط. وسيقوم ببعض التغييرات وهي
-	سيتم  السماح بإضافة أصناف الخدمات/ السلع الغير مخزنية فقط، 
-	سيتم تعطيل نظام تعدد الوحدات للصنف الواحد.
-	سيتم إخفاء حقل المخزن من الفواتير
-	كل عمليات المخزون كالصرف والتوريد والتحويل لن تكون متاحة كون هذه العمليات تتطلب أصناف مخزنية

]]>
     </content>
   </Entry>
   
   
     <Entry>
     <id>enable_multi_units</id>
     <title>السماح بتعدد الوحدات للصنف الواحد</title>
     <content>
       <![CDATA[
تفعيل هذا المتغير يسمح بالتعامل مع كميات الصنف بثلاث وحدات مختلفة، مع إمكانية التحويل بينها آليا عند الحاجة. حيث سيقوم النظام آليا بتفكييك الوحدة الأكبر إلى الوحدة الأصغر ، أو تجميع الوحدات الأصغر إلى وحدة أكبر، عند الحاجة لذلك.
عند تفكيك  أو تجميع الوحدات سيقوم النظام بعمل حركات آليه داخلية في حركة المخزون. مثلا لو تم تفكيك كرتون إلى 20 باكت مثلا. سيقوم النظام بعمل الحركة:
1 كرتون – صادر
20 باكت – وارد

]]>
     </content>
   </Entry>
   
   
     <Entry>
     <id>stock_out_hide_cost</id>
     <title>إخفاء حقل السعر/الكلفة في أوامر الصرف والتحويل المخزني</title>
     <content>
       <![CDATA[
عند تفعيل هذا المتغير سيتم إخفاء عمود الكلفة/السعر من جدول الأصناف في الفاتورة.. 
حيث أن طبيعة عمل المخازن تركز على الكميات فبالإمكان إخفاء عمود الأسعار من أوامر الصرف.
   
هذا المتغير معطل بشكل تلقائي

]]>
     </content>
   </Entry>


  <Entry>
     <id>enable_item_stock_batch</id>
     <title>تخزين الأصناف حسب الدفعات، أو تاريخ الإنتهاء، أو حسب الرقم التسلسلي للصنف</title>
     <content>
       <![CDATA[
عند تفعيل هذا المتغير سوف يسمح النظام بعمل عدة مخزونات للصنف الواحد حسب طريقة التخزين المحددة في إعدادات الصنف. وسيتم إظهار عمود بجوار اسم الصنف في جدول الأصناف في الفاتورة بإسم (الدفعة) و يمكن تغيير هذا الإسم.  

عند إدخال بيانات الأصناف 
-	يمكن تحديد طريقة لتخزين الصنف على مستوى الصنف الواحد،
-	سيقوم النظام بإختيار الطريقة التلقائية المحددة في الأعدادات. 
-	يسمح النظام بطريقة واحدة الصنف الواحد 
-	يمكن تحديد طرق مختلفة لأصناف مختلفة. 
  
عندما يتم تحديد طريقة معينة (مثلا تاريخ الإنتهاء) للصنف، فإن كل العمليات (بيع/شراء/صرف/توريد) على هذا الصنف  تتطلب تحديد تاريخ الإنتهاء. وسوف يقوم النظام بإظهار الكميات الموجودة حسب تاريخ الإنتهاء
عند عدم تفعيل هذا المتغير سيقوم النظام بإستخدام طريقة التخزين التراكمي لتخزين الأصناف.

هذا المتغير غير مفعل  بشكل تلقائي

ملاحظة: إذا كانت المؤسسة تستخدم طريقة معينة لتخزين صنف معين في مخازنها، مثلا حسب موديل الصنف، أو اللون، أو بلد المنشأ، فيمكنها إختيار طريقة التخزين حسب (1- الدفعة)، وتقوم بتغيير الإسم بما يتوافق مع عملها.

]]>
     </content>
   </Entry>
   
   
    
   
   
     <Entry>
     <id>item_batch_title</id>
     <title>تغيير تسمية الدفعة إلى</title>
     <content>
       <![CDATA[
يمكن تغيير مصطلح (الدفعة) إلى أي مصطلح بحيث يتوافق مع عمل المؤسسة. وسيظهر في الفواتير والمستندات والتقارير بالإسم الجديد.
]]>
     </content>
   </Entry>
   
      <Entry>
     <id>es_stock_opts0</id>
     <title>إخفاء الأصناف التي بدون كميات في فواتير المبيعات والصرف المخزني</title>
     <content>
       <![CDATA[
عند تفعيل هذا الخيار سيتم إخفاء الأصناف التي بدون كميات في فواتير المبيعات ومستندات الصرف المخزني وأي مستند يقوم بتخفيض كميات المخزون

هذا الخيار يعمل في شاشة الإضافة فقط - في شاشة التعديل يتم عرض كل الأصناف
]]>
     </content>
   </Entry>


    
   
   
      <Entry>
     <id>es_stock_opts5</id>
     <title>عدم السماح بإلغاء ترحيل المستندات التي تؤثر على كلفة الأصناف</title>
     <content>
       <![CDATA[
عند تفعيل هذا الخيار سوف يقوم النظام عند إلغاء ترحيل الفواتير بفحص هل سوف يتم التأثير على كلفة الأصناف التي في الفاتورة، و في حال وجد النظام أن هناك تأثير على الكلفة سوف يرفض القيام بعمل إلغاء الترحيل، 

وغالبا السبب أن هناك فواتير مرحلة بعد هذه الفاتورة، لذا سيكون على المستخدم إلغاء ترحيل الفواتير التي تم ترحيلها بعد هذه الفاتورة حتى يتمكن المستخدم من عمل إلغاء الترحيل للفاتورة.

عند تفعيل هذا الخيار سوف يقوم النظام بـ:
- ربط التأثير المخزني بالترحيل بمعنى إنه لن يتم التأثير على كميات الصنف إلا بعد الترحيل 
- تعطيل الترحيل الآلي بعد الحفظ و سيكون على المستخدم عمل الترحيل يدويا

وبشكل عام عند تفعيل هذا الخيار يفضل عمل الترحيل في نهاية اليوم

هذا الخيار مفعل بشكل تلقائي، و يفضل دوما عدم تعطيل هذا الخيار لضبط كلفة المخزون بشكل دقيق


]]>
     </content>
   </Entry>



   <Entry>
     <id>es_vat_pct</id>
     <title>نسبة الضريبة الثابتة لكل الأصناف</title>
     <content>
       <![CDATA[
إذ تم تحديد هذه النسبة سوف تطبق آليا على كل الأصناف. بغض النظر عن القيم المحددة في بيانات الأصناف
ولن يتم السماح بتغيير النسب من بيانات الأصناف.
إذا كان هناك أصناف معفية من الضرائب أو تطبق عليها نسبة مختلفة يجب ترك هذا الحقل فارغا و تحديد النسبة على مستوى الصنف.
يمكن إستخدام أداة التعديل الجماعي للأصناف لتغيير النسب بشكل جماعي بسهولة
]]>
     </content>
   </Entry>

	 <Entry>
		 <id>vat_tax_title</id>
		 <title>مسمى الضريبة عند الطباعة</title>
		 <content>
			 <![CDATA[
المسمى الذي سوف يتم طباعته في الفاتورة بجوار مبلغ الضريبة.
مثلا يمكن السماح بطباعة اسم ونسبة الضريبة
  
]]>
		 </content>
	 </Entry>


   <!--   
   <Entry>
     <id></id>
     <title></title>
     <content>
       <![CDATA[

]]>
     </content>
   </Entry>
-->

 </items> 
</SystemHelp>