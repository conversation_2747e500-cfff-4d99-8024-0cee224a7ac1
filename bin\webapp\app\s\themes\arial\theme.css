﻿

/*Themes: Arial*/

/*
    font-family:<PERSON><PERSON><PERSON>,<PERSON><PERSON>,sans-serif;
    font-size: 11pt;

       font-family: 'Times New Roman';
    font-family:'Traditional Arabic';
    font-size: 12pt;
    font-weight: bold;

    font-family:'Traditional Arabic' !important;
    font-size: 14pt;
*/
   
      
    /*
     font-family: 'Times New Roman';
     font-size: 12pt !important;
     font-weight: bold !important;
     letter-spacing:normal !important;
 */

@media screen {

    * {

        font-family:  <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>l, sans-serif;
        font-size: 12pt;
       font-weight: bold;

        
   
       
    }

    input, select, textarea {
        color: #246;
        
    }



    .page_header, .page_footer {
        letter-spacing: normal;
    }



 
     div.tree a:not(.folder), a.tree-l {
       font-weight: normal;
       color: #235;
    }


    .tit {
        font-weight: bold;
    }



} /*Media screen*/