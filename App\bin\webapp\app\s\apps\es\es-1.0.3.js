﻿// ***** EasySales *** //

var es_cl_items;
var es_cl_items_bc; 
// var es_items_list;
// var crncy_data_list;
var es_item_units;

var use_sale_price = true;
var es_doc_line_count = 100;
var is_item_grid = false;
var wq_bc_fmt = { pre: 1, item: 6, chk1: 0, qty: 5, dec: 3 }; // weighted items barcode format (len of each part)

var es = {
    CC: '01', // company currency,
    AA: 1 // Amount adjustment (adjust amount to be divisble by AA)
};

var pd = {
    SI: 0 // stock impact
};

function getLineSeq(obj) {
   

   // return $(obj).attr("id").substr(0, 3);

    obj = $(obj);
    if (obj == null || obj.length == 0)
        return null;

    var id = $(obj).attr("id");
    if (hs.isEmpty(id))
        id = $(obj).attr("name");

    if (hs.isEmpty(id))
        return null;

    return id.substr(0, 3);
}

function adjustLineSeq(seq) {
    seq = seq.toString();

    if (seq.length == 1)
        seq = "00" + seq;
    else
        if (seq.length == 2)
            seq = "0" + seq;


    return seq;
}

function getLineDocControlId(lines_fld_key, fld_key, seq) {
    seq = adjustLineSeq(seq);

    return seq + "_" + lines_fld_key + "_" + fld_key;
}

function get_sub_control_id(fld_key, seq) {
    seq = adjustLineSeq(seq);

    return seq + "_items_" + fld_key;
}

function get_sub_control(fld_key, seq) {
    seq = adjustLineSeq(seq);

   return $("#" + seq + "_items_" + fld_key);
}

function get_item_id(objList) {
    if ($(objList).is("select"))
        return $(objList).val();
    else
        return $(objList).attr("data-sel_id");
}

var _items_arr = null;
function get_item_list() {

    if (_items_arr == null)
        _items_arr = hs.cache.getItem('es_items_list');

    return _items_arr;
}


function parseItem(a) {
    return {
        id: a[0],
        item_id: a[0],
        item_name: a[1],
        item_code: a[2],
        for_name: a[3],
        item_type: a[4],

        batch_type: a[5],
        item_group: a[6],
        def_purch_unit: a[7],
        def_sale_unit: a[8],
        item_crncy: a[9],

        u1_id: a[10],
        u1_price: a[11],
        u1_cost: a[12],
        u1_min_price: a[13],

        u2_id: a[14],
        u2_price: a[15],
        u2_cost: a[16],
        u2_min_price: a[17],

        u3_id: a[18],
        u3_price: a[19],
        u3_cost: a[20],
        u3_min_price: a[21]
    }
}


function get_item_by_id(item_id) {
    var es_items_list = get_item_list();
    var i;
    for (i = 0; i < es_items_list.length; i++) {
        if (es_items_list[i][0] == item_id)
            return parseItem(es_items_list[i]);
    };

    return null;
}

function get_item_by_code(code) {
    if (hs.isEmpty(code))
        return null;

    var es_items_list = get_item_list();
    var i;
    for (i = 0; i < es_items_list.length; i++) {
        if (es_items_list[i][2] == code)
            return parseItem(es_items_list[i]);
    };

    return null;
}

function get_item_by_index(idx) {
    var es_items_list = get_item_list();
    var i;
    for (i = 0; i < es_items_list.length; i++) {
        if (i == idx)
            return parseItem(es_items_list.items[i]);
    };

    return null;
}


/*
function build_items_codelist_array_ORG() {

    var target_item_type = null;

    if (getUrlParameter("fm") == "es-sale-pos-svc")
        target_item_type = "S";

    if (getUrlParameter("fm") == "es-sale-pos-mat")
        target_item_type = "M";


    if (hs.isEmpty(target_item_type))
        target_item_type = null;

    es_cl_items = hs.nol.getCodeListArray(get_item_list(), null, function (item) {
        if (target_item_type != null && target_item_type != item.item_type)
            return null;

        return item.item_name;
    });

    es_cl_items_bc = hs.nol.getCodeListArray(get_item_list(),
        function (item) {
            if (target_item_type != null && target_item_type != item.item_type)
                return null;

            return item.item_id;
        },
        function (item) {
            if (target_item_type != null && target_item_type != item.item_type)
                return null;

            return item.item_name;
        });

    
}
*/

function build_items_codelist_array() {

    es_cl_items = es_cl_items_bc = get_item_list();
    return;

    var target_item_type = null;

    if (getUrlParameter("fm") == "es-sale-pos-svc")
        target_item_type = "S";

    if (getUrlParameter("fm") == "es-sale-pos-mat")
        target_item_type = "M";


    if (hs.isEmpty(target_item_type))
        target_item_type = null;

    es_cl_items = hs.nol.getCodeListArray(get_item_list(), null, function (item) {
        if (target_item_type != null && target_item_type != item.item_type)
            return null;

        return item.item_name;
    });

   
    var j = 0;
    var item_list = get_item_list();

    es_cl_items_bc = new Array(item_list.length);

    hs.nol.foreach(item_list, function (item, i) {

        if (target_item_type == null || target_item_type == item.item_type) {
            es_cl_items_bc[j] = new Array();

            es_cl_items_bc[j][0] = item.item_id;
            es_cl_items_bc[j][1] = item.item_name;
            es_cl_items_bc[j][2] = item.item_code;
            es_cl_items_bc[j][3] = item.for_name;
            j++;
        };

    });
    
}

function fill_list_with_items(seq) {
    var cl = new CodeList(es_cl_items);
    cl.fillSelectList(get_sub_control("id", seq));
}

function fill_item_units_list(seq, item_id) {

    var item_data = get_item_by_id(item_id);
    var theList = get_sub_control("un", seq);
    var cl_item_units = new CodeList(es_item_units);
    var sel_id = theList.val(); // get selected unit

    if (item_data == null)
        return null;

    if (hs.isEmpty(sel_id)) {
        if (use_sale_price)
            sel_id = item_data.def_sale_unit;
        else
            sel_id = item_data.def_purch_unit;
    };

    theList.empty();

    theList.append($('<option />'));

    if (use_sale_price) {
        if (!hs.isEmpty(item_data.u1_id))
            theList.append($('<option />').val(item_data.u1_id).html(cl_item_units.getTitle(item_data.u1_id)).data("price", item_data.u1_price));

        if (!hs.isEmpty(item_data.u2_id))
            theList.append($('<option />').val(item_data.u2_id).html(cl_item_units.getTitle(item_data.u2_id)).data("price", item_data.u2_price));

        if (!hs.isEmpty(item_data.u3_id))
            theList.append($('<option />').val(item_data.u3_id).html(cl_item_units.getTitle(item_data.u3_id)).data("price", item_data.u3_price));
    }
    else {
        

            theList.append($('<option />').val(item_data.u1_id).html(cl_item_units.getTitle(item_data.u1_id)).data("price", item_data.u1_cost));

            if (!hs.isEmpty(item_data.u2_id))
                theList.append($('<option />').val(item_data.u2_id).html(cl_item_units.getTitle(item_data.u2_id)).data("price", item_data.u2_cost));

            if (!hs.isEmpty(item_data.u3_id))
                theList.append($('<option />').val(item_data.u3_id).html(cl_item_units.getTitle(item_data.u3_id)).data("price", item_data.u3_cost));
       
    };

    theList.val(sel_id); // restore the selected id

    return theList;
}



function config_sale_line_item(seq, isMobile, use_bc) {
   
    

    // item id
    if (isMobile) { // use normal list control
        fill_list_with_items(seq);

        $(get_sub_control("id", seq))
        .attr("onchange", "on_item_list_changed(this,true)").minLen(1);
        
    }
    else { // hs code list control
        $('#' + get_sub_control_id("id", seq)).exList(
            {
                codelist: es_cl_items_bc,
                select_only: true,
                support_bc: use_bc, // true
                on_item_changed:on_item_list_changed
            }).minLen(1);
    };
    

    // batch no
    $(get_sub_control("bn", seq))
        .css("max-width", "100px")
      //  .attr("tabindex", "-1")
        .attr("autocomplete", "off")
        .attr("onfocus", "on_item_batch_focus(this)")
        .attr("onblur", "on_item_batch_blur(this)")
        //.addClass("no-enter")
        
    ;

    // ---------------

    // unit
    $(get_sub_control("un", seq))
    // .attr("tabindex", "-1")
     .attr("onchange", "on_item_unit_changed(this,null)").minLen(1);
    
        
    // note
    $(get_sub_control("nt", seq))
    //   .attr("tabindex", "-1")
    ;

    // unit price
    $(get_sub_control("up", seq))
        .attr("onchange", "on_item_price_changed(this)").attr("autocomplete", "off").addClass("flds lft hs-num-fld").css("max-width", "150px").minLen(1);
    
    // unit count
    $(get_sub_control("uc", seq))
        .attr("onchange", "on_item_price_changed(this)").attr("autocomplete", "off").addClass("flds lft hs-num-fld").css("max-width", "150px").minLen(1);

    // unit count
    $(get_sub_control("di", seq))
        .attr("onchange", "on_item_discount_changed(this)").attr("autocomplete", "off").addClass("flds lft hs-num-fld").css("max-width", "150px");
    
    //subtotal
    $(get_sub_control("st", seq))
        .attr("tabindex", "-1")
        .attr("readonly", "readonly")
        .attr("autocomplete", "off")
        .addClass("flds lft hs-num-fld").css("max-width", "150px");
    ;

    $(get_sub_control("ex", seq))
        .attr("autocomplete", "off")
        .addClass("flds lft hs-num-fld").css("max-width", "150px");
     

    if (isMobile) {
        $(get_sub_control("id", seq)).css("min-width", "100px");
        $(get_sub_control("un", seq)).css("min-width", "50px");
        $(get_sub_control("up", seq)).css("min-width", "70px");
        $(get_sub_control("st", seq)).css("min-width", "60px");
        $(get_sub_control("bn", seq)).css("min-width", "60px");
    };
};



function init_es_doc(lines_count, isMobile, use_bc) {

    use_bc = true;
   
        var fm_mode = getUrlParameter("fm-mode");

        es_doc_line_count = lines_count;

        is_item_grid = (!hs.isEmpty(fm_mode) && fm_mode.startsWith("pos-hos"));


        try {
            build_items_codelist_array();
        }
        catch (ex) {
            window.alert('ex:' + ex.toString());
        }



        var i;
        for (i = 0; i < lines_count; i++) {
            var seq = i.toString();
            config_sale_line_item(seq, isMobile, use_bc);


            var item_id = get_item_id(get_sub_control("id", seq));

            fill_item_units_list(seq, item_id);
        };

        $('#inv_total').html($('#price').val());

        document._on_form_reset = on_es_form_reset;



        if (is_item_grid) {
          //  window.setTimeout(function () { hs.ui.scrollTo("easyform-tabs-0") }, 10);// scroll to items
        };
    
        var doc_type=get_doc_type();

        if (doc_type == "201" || doc_type == "203" || doc_type == "210")
            hs.fm.onSubmit = showSaveDlg;
          // $("a.link_icon.icon_save").attr("href", "javascript:showSaveDlg()");

        flds_order.push('discount');
        flds_order.push('net_amount');
        flds_order.push('paid');
        flds_order.push('but-save');
           

        //window.alert(flds_order.join());
}

// ----------------



function closeSaveDlg() {
    hs.page.cover(false);
    HideCover(window);
    $("#doc-save-tb").remove();

    var amnt_panel = $("#amnt-panel").removeClass("center");/*.css("top", "initial").css("position", "fixed");
    
    if (amnt_panel.hasClass("side"))
        amnt_panel.css("left", "40px").css("bottom", "40px");
    else
        amnt_panel.css("left", "0").css("bottom", "0").css("right", "0");
        */
        
}

function submitSaveDlg() {
    hs.fm.onSubmit = null;
    DoSubmit('', '');
    closeSaveDlg();
    hs.fm.onSubmit = showSaveDlg;
}

function showSaveDlg() {

    if (hs.e.exists("doc-save-tb")) {
        on_paid_changed();
        submitSaveDlg();
        return;
    };

    // ShowCover(0.5);
    hs.page.cover(true);

    var buts = "<div id='doc-save-tb'><div style='margin:0; margin-top:20px; padding: 10px; border-radius: 8px 8px;' class='ui-toolbar'><a id='but-save' class='link_icon icon_save' href='javascript:submitSaveDlg()'>حفظ</a><a class='link_icon icon_cancel' href='javascript:closeSaveDlg()'>تراجع</a></div></div>";

    var amnt_panel = $("#amnt-panel").addClass("center").append(buts); 
  
    if (hs.e.isFocusable("discount"))
        $("#discount").attr("title", null).focus();
    else
        $("#paid").focus();

    $(amnt_panel).on("keydown", function (e) {
        if (e.which === 27) {
            closeSaveDlg();
            goto_first_vacant_line();
        };

    });
   

    return false; // do not submit
}

function is_cash_invoice() {

    var fm_id = hsEasyForm.getFormId();
    var fm_mode = getUrlParameter("fm-mode")
    

    if (fm_id == 'es-sale-quot')
        return true;

   // if (fm_id == 'es-sale-frd' && fm_mode == "cash-sales" && $('#doc_subtype').val() == '01')
       // return true;

    if (fm_id.startsWith('es-sale-pos')) {
        ;
        if (fm_mode == "post-paid" || fm_mode == "pos-hos-waiter")
            return false;
       
          return true;
    };

    return false;
}

function reset_item_line(seq, item_id) {

    if (item_id != null)
        $("#l-items-" + item_id).removeClass("selected has-invalid-value");

    get_sub_control("id", seq).val('').data("wq", null);
    get_sub_control("uc", seq).val('');
    get_sub_control("fu", seq).val('');
    get_sub_control("un", seq).val('');
    get_sub_control("up", seq).val('');
    get_sub_control("st", seq).val('');

    get_sub_control("bn", seq).val('');
    get_sub_control("wh", seq).val('');
}


function on_pos_add_items(seq, item_id, unit_id, unit_price, count) {

    var units = parseInt(get_sub_control("uc", seq).val());

    if (isNaN(units) || units < 0)
        units = 0;

    units += count;

    if (units < 0 || count == 0)
        units = 0;
    
    if (units > 0) {

        hs.msg.removeAll();

        get_sub_control("id", seq).val(item_id);
        get_sub_control("uc", seq).val(units);

        get_sub_control("un", seq).val(unit_id);
        get_sub_control("up", seq).val(unit_price);

        $("#l-items-" + item_id).addClass("selected");
    }
    else {
        reset_item_line(seq, item_id);
    };

    on_item_price_changed(get_sub_control("up", seq));
}

function on_es_form_reset() {
    for (var seq = 0; seq < es_doc_line_count; seq++) {
        var item_id = get_sub_control("id", seq).attr("placeholder", null).attr("data-sel_id", null).val();
        reset_item_line(seq, item_id);
        on_item_price_changed(get_sub_control("up", seq));
    };

    // hs.msg.toast("document form reset: lines=" + es_doc_line_count);
}

var use_item_discount_in_doc_discount = false;
var use_item_discount_std_pct = true;

function get_amount_discount(amount, pct, is_net_amount, use_std_pct) {
    if (is_net_amount && !use_std_pct) {
        use_std_pct = true;
        hs.msg.toast("Force using standard percentage calculation");
    };

    if (use_std_pct)
    {
        if (is_net_amount)
            return pct * (amount / (100.0 - pct));
        else
            return amount * pct / 100;
    }
    else
    {
        pct = pct / 100;
        return amount - (amount / (1.0 + pct));
    };



}

function get_item_discount(seq, line_total, is_net_amount) {
    var item_discount_str = get_sub_control("di", seq).val();

    if (hs.isEmpty(item_discount_str))
        return 0;
        
    var item_discount = hs.safe.parseFloat(item_discount_str);
    if (item_discount > 0) {
        if (item_discount_str.contains("%")) {
            var is_std_pct = !(item_discount_str.contains("%%"));
            item_discount = get_amount_discount(line_total, item_discount, is_net_amount, is_std_pct);
        };
    };

    item_discount = hs.safe.round(item_discount, 2);

    get_sub_control("di", seq).val(item_discount.toString());

    return item_discount;

};

function on_item_discount_changed(obj) {

    var seq = getLineSeq(obj);

    if (hs.isEmpty(seq))
        return;

    if (hsEasyForm.fm_id == "es-purch-inv") {
        // return;

        var price = hs.safe.parseFloat(get_sub_control("up", seq).val());
        get_item_discount(seq, price, true);
        return;
    };
       

    on_item_unit_changed(get_sub_control("un", seq.toString()), seq);
}


function on_item_price_changed(objPrice) {

    var seq = getLineSeq(objPrice);

    if (hs.isEmpty(seq))
        return;
   

    var price = hs.safe.parseFloat(get_sub_control("up", seq).val());
    var units = hs.safe.parseFloat(get_sub_control("uc", seq).val());
    
    var sub_total, seq;
    var total_price = 0;
      

    if (isNaN(price) || price < 0)
        price = 0;

    if (isNaN(units) || units < 0)
        units = 0;
       

    sub_total = price * units;
    
    sub_total = hs.safe.round(sub_total);
        

    get_sub_control("up", seq).val(price != 0 ? hs.fmtAmount(price) : ''); // item price
    get_sub_control("uc", seq).val(units != 0 ? units : ''); // item count
    get_sub_control("st", seq).val(sub_total != 0 ? hs.fmtAmount(sub_total) : ''); // item subtotal
    
    for (seq = 0; seq < es_doc_line_count; seq++) {
        sub_total = hs.safe.parseFloat(get_sub_control("st", seq.toString()).val());
        if (!isNaN(sub_total))
            total_price += sub_total;
    };

    total_price = hs.safe.round(total_price);

    $("#price").val(hs.fmtAmount(total_price)); // total price

    $('#inv_total').html($('#price').val());

    var pricing_policy = $("#pricing").val();

    if (use_sale_price && pricing_policy == "03") { // apply cust discount
        var cust_disc_pct = get_cust_discount_pct($("#cov_no").val());
        if (cust_disc_pct > 0)
            $("#discount").val(cust_disc_pct.toString() + "%");
        else
            $("#discount").val("0");
    };


    on_discount_changed(null);
  
}


function on_discount_changed(theDiscount) {

    var is_pct = $("#discount").val().contains("%");
    var is_std_pct = !($("#discount").val().contains("%%"));
    var total_price =hs.safe.parseFloat($("#price").val()); // total price
    var discount = hs.safe.parseFloat($("#discount").val());
    var pnet_amnt = hs.safe.parseFloat($("#pnet_amount").val());
    var net_amount = 0;

    if (isNaN(total_price))
        total_price = 0;

    if (isNaN(discount)) {
        discount = 0;
        $("#discount").val(discount);
    };

    if (is_pct) {
        // discount = total_price * discount / 100;
        discount = hs.safe.round(get_amount_discount(total_price, discount, false, is_std_pct));
        $("#discount").val(hs.fmtAmount(discount));
    };


    net_amount = total_price - discount;
    $("#net_amount").val(hs.fmtAmount(net_amount));

    if (is_cash_invoice()) {
        if (net_amount - pnet_amnt > 0)
            $("#paid").val(hs.fmtAmount((net_amount - pnet_amnt)));
        else
            $("#paid").val("0");

        $("#rem_amount").val("0");
    };
   

    on_paid_changed();
}

// -----------------------

function on_paid_changed() {

    var rem_amount;
    var paid = hs.safe.parseFloat($("#paid").val());
    var total_price = hs.safe.parseFloat($("#price").val()); // total price
    var discount = hs.safe.parseFloat($("#discount").val());
    var pnet_amnt = hs.safe.parseFloat($("#pnet_amount").val());

    if (isNaN(total_price))
        total_price = 0;

    if (isNaN(discount)) {
        discount = 0;
        $("#discount").val(discount);
    };

    if (isNaN(paid))
        paid = 0;


    rem_amount = total_price - (discount + paid + pnet_amnt);
    $("#rem_amount").val(hs.fmtAmount(rem_amount));


    on_doc_crncy_changed('price', 'doc_crncy');

    var info_msg = "";
    var crncy_title = get_currency_title(hs.fm.val('doc_crncy'));
    

    info_msg += "الإجمالي: " + $('#price').val() + " " + crncy_title;
  //  info_msg += "الخصم: " + $('#discount').val();
   // info_msg += "الصافي: " + $("#net_amount").val();

    $("#fm_state").html(info_msg).css("font-size", "11pt").css("color", "#008").css("padding", "10px");

    $("#amnt-crncy").text(crncy_title);

}

// ---------------------



function on_item_unit_changed(unitsList, seq) {

    var is_actual_unit_change = false;

    if (hs.isEmpty(seq)) {
        seq = getLineSeq(unitsList); // $(unitsList).attr("id").substr(0, 2);
        is_actual_unit_change = true;
        
    };

    if (hs.isEmpty(seq))
        return;

    if (is_actual_unit_change)
        get_sub_control("di", seq).val("");

    var price_obj = get_sub_control("up", seq);

   
    var item_data = get_item_by_id(get_item_id(get_sub_control("id", seq)));

    var sel_unit_price_val = hs.safe.parseFloat($(unitsList).find(':selected').data("price"));

    if (isNaN(sel_unit_price_val))
    {
        if (use_sale_price)
            sel_unit_price_val = item_data.u1_price;
        else
            sel_unit_price_val = item_data.u1_cost;
    };
    
    var unit_price_val_dc = 0; 

    var doc_crncy = get_doc_crncy();//

    if (hs.isEmpty(doc_crncy))
        doc_crncy = es.CC; // 
    

    unit_price_val_dc = convert_currency(sel_unit_price_val, item_data.item_crncy, doc_crncy); // in doc crncy not cc

    if (!isNaN(sel_unit_price_val) && (sel_unit_price_val != unit_price_val_dc))
        hs.msg.toast(sel_unit_price_val.toString() + " " + get_currency_title(item_data.item_crncy));
    //hs.e.setTip(price_obj, unit_price_val_dc.toString() + " = " + sel_unit_price_val.toString() + " " + get_currency_title(item_data.item_crncy));

    unit_price_val_dc = hs.safe.round(unit_price_val_dc);

    


    if (hsEasyForm.fm_id == "es-purch-inv") {
        price_obj.val("");
        price_obj.attr("placeholder", unit_price_val_dc.toString());
    }
    else {

        unit_price_val_dc = unit_price_val_dc - get_item_discount(seq, unit_price_val_dc, false);
        price_obj.val(unit_price_val_dc.toString());
        

    };

    on_item_price_changed(price_obj);
    
}

// -----------------------



function on_item_list_changed(theList, move_to_next_item) {

    if (!hs.isEmpty(hs.e.$(theList).val()))
        hs.msg.removeAll(); // for POS and offline mode

    // if readonly no need to do anything
    if (hs.e.isReadonly(theList))
        return;

  //   var item_id = $(theList).val();
    var item_id = get_item_id($(theList));//  $(theList).val();
   
    var seq = getLineSeq(theList);

    if (hs.isEmpty(seq))
        return;

    var objPrice = get_sub_control("up", seq);

    var objBatch = get_sub_control("bn", seq);

    if (hs.isEmpty(item_id)) {
        objPrice.val('0');
        get_sub_control("uc", seq).val('0');
        get_sub_control("fu", seq).val('');
        get_sub_control("st", seq).val('0');
        get_sub_control("un", seq).empty().val('');
        get_sub_control("nt", seq).val('');
        get_sub_control("bn", seq).val('');
        get_sub_control("wh", seq).val('');
        $(theList).data("wq", null);
        on_item_price_changed(objPrice);
        return;
    };
    
    var units_obj = fill_item_units_list(seq, item_id);
   
    var qty = $(theList).data("wq"); // wiegthed items

   

    if (hs.isEmpty(qty))
        qty = "1";
    else {
        // $(theList).data("wq", null);
        //log(qty);
    };



    $(get_sub_control("uc", seq)).val(qty);

       
       // get_sub_control("st", seq).val(item[2]);

        on_item_unit_changed(get_sub_control("un", seq), seq);

    // on_item_price_changed(price_obj);
    
        if (hs.safe.parseFloat(objPrice.val()) <= 0) {
            $(objPrice).prop('readonly', false); //.focus(); return;
        };
       
        
  
        if (move_to_next_item) {
            focus_next(theList);
        };
};

// -------------------------



function nextBatchListItem(next,objBatch) {
    var _a;

    if (next == 1)
        _a = $("table.item-batch-menu tbody tr.hs-selected").next();
    else
        if (next == -1)
            _a = $("table.item-batch-menu tbody tr.hs-selected").prev();
        

    if (_a.length == 0)
        _a = $("table.item-batch-menu tbody tr:first");

    if (_a.length != 0) {
        $("table.item-batch-menu tbody tr.hs-selected").removeClass("hs-selected");
        $(_a).addClass("hs-selected");
        $(objBatch).val($(_a).attr("data-val"));

        _a[0].scrollIntoView(false);
    };
}

function on_item_batch_menu_setup(objBatch) {

    var is_setup = $(objBatch).attr("data-setup");
    
    if (hs.isEmpty(is_setup))
        $(objBatch).attr("data-setup", "1");
    else
        return; // 


    $(objBatch).on("keydown", function (event) {
        var keycode = (event.keyCode ? event.keyCode : event.which);
        if (keycode == '40' || keycode == '39') {
            hs.absorb(event);
            nextBatchListItem(1, this);
            return;
        }

        if (keycode == '38' || keycode == '37') {
            hs.absorb(event);
            nextBatchListItem(-1, this);
            return;
        }

        if (keycode == '13') {

            hs.absorb(event);
            
            var _a = $("table.item-batch-menu tbody tr.hs-selected");
            var bat_val = $(_a).attr("data-val");
            if (!hs.isEmpty(bat_val))
                $(this).val();

            line_item_focus_next(this);
        }
    });
}


function on_item_batch_focus(objBatch) {
    var seq = getLineSeq(objBatch);

    if (hs.isEmpty(seq))
        return;

    var item_id = get_item_id(get_sub_control("id", seq));

    if (hs.isEmpty(item_id))
        return;

    
    var item_data = get_item_by_id(item_id);

    if (hs.isEmpty(item_data.batch_type) || item_data.batch_type == '0') {
        focus_next(objBatch);
        return; // item configured for no batch
    };

    if (hs.e.isReadonly(objBatch))
        return;
    
    if (pd.SI == 1) {  // stock increase
        if (item_data.batch_type != '3') { // 3=by SN

            $(objBatch).datepicker({
                changeMonth: true,
                changeYear: true,
                
                dateFormat: hsUiDateFormat,
                firstDay: 6,
                monthNames: ["يناير", "فبراير", "مارس", "ابريل", "مايو", "يونيو", "يوليو", "اغسطس", "سبتمبر", "اكتوبر", "نوفمبر", "ديسمبر"],
                monthNamesShort: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"],
                dayNamesMin: ["ح", "ن", "ث", "ر", "خ", "ج", "س"]
                /*
                ,
                showOn: "button",
                buttonImage: "/app/s/images/icons/calendar.png",
                buttonImageOnly: true
                */
            });
        };

        return;
    };
    

    var res_div = "item-stock-list-" + seq;

    create_suggest_element($(objBatch).attr("id"), res_div);
    
    var url = "/app/fms/?fm=es-item&cmd=list-stock&ajax=y&id=" + item_id + "&select-target=" + $(objBatch).attr("id") + "&store-id=" + hs.fm.val("store_id");

   // hs.msg.toast(url);

    do_ajax(url, res_div, null);

    on_item_batch_menu_setup(objBatch)
}




function on_item_batch_blur(objBatch) {
    if (!use_sale_price) {
      //  return;
    };

    var seq = getLineSeq(objBatch);// $(objBatch).attr("id").substr(0, 2);
    if (hs.isEmpty(seq))
        return;
  
    var res_div = "item-stock-list-" + seq;

    delayDelElm(res_div, 200);
}


// ---------------------------

function line_item_focus_next(obj) {
    // focus_next(obj);
    // return;
    var seq = hs.safe.parseInt(getLineSeq($(obj))) + 1;

    var itemList = $("#" + get_sub_control_id("id", seq), parent.document.body);

    $(itemList).focus();
}

function on_item_batch_select(target_key_ctl, bat_id, store_id, move_next) {

   // window.alert("t=" + target_key_ctl + ";bat=" + bat_id + "store=" + store_id);

    var $target = $("#" + target_key_ctl, parent.document.body);

    $target.val(bat_id);

    var seq = getLineSeq($target);

    var $store = $("#" + get_sub_control_id("wh", seq), parent.document.body);

    if ($store.length != 0) {
        $store.val(store_id);
        if (move_next)
            line_item_focus_next($store);
    }
    else {
        if (move_next)
            line_item_focus_next($target);
    }


}


// -------------------------

function adjust_amount(amount, divisible_by) {
    amount = Math.ceil(amount);

    if (divisible_by != 0)
        while (amount % divisible_by != 0)
            amount = amount + 1;

    amount = hs.safe.round(amount);

    return amount;
}

// ----------------------------

function on_item_crncy_changed() {
    var crncy_title = " / " + $("#item_crncy").find(':selected').text();
    
    $(".item-crncy").text(crncy_title);

    crncy_title = " / " + $("#item_crncy_fc").find(':selected').text();

    $(".item-crncy-fc").text(crncy_title);
}

function on_item_crncy_fc_changed() {
    var crncy_title = " / " + $("#item_crncy_fc").find(':selected').text();

    $(".item-crncy-fc").text(crncy_title);

    var item_crncy = $('#item_crncy').val()
    var item_crncy_fc = $('#item_crncy_fc').val();

    $('#u1_price_fc').val(convert_currency(hs.safe.parseFloat($('#u1_price').val()), item_crncy, item_crncy_fc));
    $('#u2_price_fc').val(convert_currency(hs.safe.parseFloat($('#u2_price').val()), item_crncy, item_crncy_fc));
    $('#u3_price_fc').val(convert_currency(hs.safe.parseFloat($('#u3_price').val()), item_crncy, item_crncy_fc));
}



function on_item_u1_to_u2_convert_changed() {
    var u_id = $('#u2_id').val();
    var n = hs.safe.parseFloat($('#u1_to_u2').val());

    if (hs.isEmpty(u_id) || n <= 0) {
        $('#u1_to_u2, #u2_cost, #u2_price, #u2_min_price').val('0');
        return;
    };
   
    $('#u2_cost').val(hs.safe.round(hs.safe.parseFloat($('#u1_cost').val()) / n).toString());
    $('#u2_price').val(adjust_amount(hs.safe.parseFloat($('#u1_price').val()) / n, es.AA).toString());
    $('#u2_min_price').val(adjust_amount(hs.safe.parseFloat($('#u1_min_price').val()) / n, es.AA).toString());

    on_item_u2_to_u3_convert_changed();
}

//------------------------------

function on_item_u2_to_u3_convert_changed() {
    var u_id = $('#u3_id').val();
    var n = hs.safe.parseFloat($('#u2_to_u3').val()) || 0;

    if (hs.isEmpty(u_id) || n <= 0) {
        $('#u2_to_u3, #u3_cost, #u3_price, #u3_min_price').val('0');
        return;
    };

    $('#u3_cost').val(hs.safe.round(hs.safe.parseFloat($('#u2_cost').val()) / n).toString());
    $('#u3_price').val(adjust_amount(hs.safe.parseFloat($('#u2_price').val()) / n, es.AA).toString());
    $('#u3_min_price').val(adjust_amount(hs.safe.parseFloat($('#u2_min_price').val()) / n, es.AA).toString());
}

// -------------------------------

function adjust_unit_convert_labels(isFmLoad) {

    var t = $('#u1_id').find(':selected').text();
    $('.item-u1').text(t);

    t = $('#u2_id').find(':selected').text();
    $('.item-u2').text(t);

    t = $('#u3_id').find(':selected').text();
    $('.item-u3').text(t);

    var flds = '#u1_to_u2, #u2_cost, #u2_price, #u2_min_price';

    if (hs.isEmpty($("#u2_id").val()))
        $(flds).val('0').hide();
    else 
        $(flds).show();

    flds = '#u2_to_u3, #u3_cost, #u3_price, #u3_min_price';
    if (hs.isEmpty($("#u3_id").val()))
        $(flds).val('0').hide();
    else
        $(flds).show();

    var fm_mode = getUrlParameter("fm-mode");
    var fm_cmd = getUrlParameter("cmd");

    if (isFmLoad && fm_cmd == "edit") {

        hs.e.enable("u1_id", false);
        hs.e.enable("u2_id", false);
        hs.e.enable("u3_id", false);

        if (fm_mode == "change-units") {
            if (!hs.isEmpty($("#u1_id").val()))
                hs.e.enable("u1_id", true);

            if (!hs.isEmpty($("#u2_id").val()))
                hs.e.enable("u2_id", true);

            if (!hs.isEmpty($("#u3_id").val()))
                hs.e.enable("u3_id", true);
        };

        if (fm_mode == "add-units") {
            if (hs.isEmpty($("#u1_id").val()))
                hs.e.enable("u1_id", true);

            if (hs.isEmpty($("#u2_id").val()))
                hs.e.enable("u2_id", true);

            if (hs.isEmpty($("#u3_id").val()))
                hs.e.enable("u3_id", true);
        };
    };

}

function enableChangeItemUnits() {
    hs.e.enable("u1_id", true);
    hs.e.enable("u2_id", true);
    hs.e.enable("u3_id", true);
}

function resetItemUnit(u) {
    $("#u" + u +"_id").val("");
    $("#u"+ u +"_cost").val("");
    $("#u" + u +"_price").val("");
    $("#u"+ u +"_min_price").val("");
}

function insertMidItemUnits() {

    if (!hs.isEmpty($("#u3_id").val())) {
        hs.msg.error("لا يمكن الإضافة");
        return;
    };

    $("#u3_id").val($("#u2_id").val());
   
    hs.e.enable("u2_id", true);
    hs.e.enable("u3_id", false);

    resetItemUnit("2");

    $("#u1_to_u2").val("0");
   

}

function addBigItemUnits() {

    if (!hs.isEmpty($("#u3_id").val())) {
        hs.msg.error("لا يمكن إضافة وحدة أكبر");
        return;
    };

  


    var inst = "يمكن تفكيك الوحدة الأكبر إلى : ";
    var unit = $('#u1_id').find(':selected').text();

    hs.inputBox.show("إضافة وحدة أكبر", inst, unit, null, "", "", function () {
        // window.alert(hs.inputBox.value);
        var n = hs.safe.parseInt(hs.inputBox.value);

        if (n <= 1) {
            hs.msg.error("القيمة المدخلة غير مباسبة");
            return;
        };

        $("#u3_id").val($("#u2_id").val());
        $("#u3_cost").val($("#u2_cost").val());
        $("#u3_price").val($("#u2_price").val());
        $("#u3_min_price").val($("#u2_min_price").val());
        $("#u2_to_u3").val($("#u1_to_u2").val());


        $("#u2_id").val($("#u1_id").val());
        $("#u2_cost").val($("#u1_cost").val());
        $("#u2_price").val($("#u1_price").val());
        $("#u2_min_price").val($("#u1_min_price").val());

        resetItemUnit("1");


        adjust_unit_convert_labels(false);

        // ------


        $("#u1_to_u2").val(n.toString());

        $('#u1_cost').val(hs.safe.round(hs.safe.parseFloat($('#u2_cost').val()) * n).toString());
        $('#u1_price').val(adjust_amount(hs.safe.parseFloat($('#u2_price').val()) * n, es.AA).toString());
        $('#u1_min_price').val(adjust_amount(hs.safe.parseFloat($('#u2_min_price').val()) * n, es.AA).toString());

        hs.e.enable("u1_id", true);
        hs.e.enable("u2_id", false);
        hs.e.enable("u3_id", false);
    });
}

// ---

function get_currency_list() {
    return hs.cache.getItem('crncy_data_list');
}

var _acc_arr = null;

function fi_acc_get_all() {
    _acc_arr = hs.cache.getItem('acc_data_list');

  //  window.alert(_acc_arr.length);

    return _acc_arr;
}

// ********** FiAcc ************ //

var FiAcc = {

    getMatched: function (matchFunc) {
        var acc_all = fi_acc_get_all();
        var matched_arr = [], i, c = 0;

        for (i = 0; i < acc_all.length; i++) {
            if (matchFunc(acc_all[i])) {
                matched_arr[c++] = acc_all[i];
            };
        };

        return matched_arr;
    },

    getSubList: function (ap, at) {
        return FiAcc.getMatched(function (ac) {
            return ac[3] == '2' &&  // subacc
                  (ap == null || ac[2] == ap) && // parent
                  (at == null || ac[6] == at) // type
            ;
        });
    },

    getSubs: function () {
        return FiAcc.getSubList(null, null);
    },

    getGLs: function () {
        return FiAcc.getMatched(function (ac) {
            return ac[3] == '2' && (ac[6]=='0' || hs.isEmpty(ac[6]));
        });
    },

    getChildern: function (acc_par) {
        return FiAcc.getSubList(acc_par, null);
    },

    getAnals: function (acc_type) {
        if (hs.isEmpty(acc_type))
            return FiAcc.getSubs();
        

        if (acc_type == '0')
            return FiAcc.getGLs();

 

        return FiAcc.getSubList(null, acc_type);
    },
}; // FiAcc

// ****************************** //

function get_currency_title(crncy) {
    if (hs.isEmpty(crncy))
        return '';
    var crncy_data = hs.nol.get_item(get_currency_list(), crncy);
    return (crncy_data == null) ? crncy : crncy_data.cur_short;
}

function get_doc_crncy() {
    return hs.fm.val("doc_crncy");
}

function get_exchange_rate(crncy) {

    if (crncy == es.CC)
        return 1;

    var crncyList = get_currency_list();
    if (crncyList == null)
        return 1;
    else {

        var doc_crncy = $("#doc_crncy").val();
       
        if (!hs.isEmpty(doc_crncy) && doc_crncy == crncy) {
            var doc_crncy_exrate = hs.safe.parseFloat($("#doc_crncy_exrate").val(), 0);
            if (doc_crncy_exrate > 0) {
                hs.trace("doc_crncy_exrate=" + doc_crncy_exrate.toString());
                return doc_crncy_exrate;
            };
        };
        
        var crncy_data = hs.nol.get_item(crncyList, crncy);
        return (crncy_data == null) ? 0 : crncy_data.ex_rate;
    };
}

function convert_amount_to_cc(amnt, amnt_crncy) {
    if (isNaN(amnt))
        return 0;

    if (hs.isEmpty(amnt_crncy))
        return amnt;
    else
        return hs.safe.round( amnt * get_exchange_rate(amnt_crncy));
}


function convert_currency(amount, from_currency, to_currency) {

    if (hs.isEmpty(from_currency))
        from_currency = es.CC;

    if (hs.isEmpty(to_currency))
        to_currency = es.CC;

    if (from_currency == to_currency)
        return amount;

    if (from_currency == es.CC)
        return amount / get_exchange_rate(to_currency);

    if (to_currency == es.CC)
        return amount * get_exchange_rate(from_currency);

    // cross exchange 
    var amount_cc = convert_currency(amount, from_currency,es.CC);

    return convert_currency(amount_cc, es.CC, to_currency);
}


function on_doc_crncy_changed(amnt_fld_id, crncy_lst_id) {
    var amnt_val = hs.safe.parseFloat($('#' + amnt_fld_id).val()) || 0;
    var crncy_code = $('#' + crncy_lst_id).val();
    var info;

    if (hs.isEmpty(crncy_code))
        crncy_code = es.CC;

    if (crncy_code == es.CC)
        $("#doc_crncy_exrate").val('');
    else
        if (!hs.e.isReadonly("doc_crncy_exrate"))
            $("#doc_crncy_exrate").attr('placeholder', get_exchange_rate(crncy_code));
    
    if (crncy_code == es.CC || amnt_val == 0)
        info = '';
    else {
        var exrate = hs.safe.parseFloat($("#doc_crncy_exrate").val());
        if (exrate == 0)
            info = '=' + convert_amount_to_cc(amnt_val, crncy_code);
        else
            info = '=' + hs.safe.round(amnt_val * exrate);
    };

    $("#cc_" + amnt_fld_id).html(info);

}


function on_sales_doc_currency_changed(crncy_lst) {
 
   //  window.alert("You must reset all items..");

    if (crncy_lst == null)
        crncy_lst = "doc_crncy";

    var crncy_code = hs.e.$(crncy_lst).val();

    if (!hs.e.isReadonly("doc_crncy_exrate"))
        $("#doc_crncy_exrate").attr('placeholder',get_exchange_rate(crncy_code));
        //$("#doc_crncy_exrate").val(get_exchange_rate(crncy_code));

    $("#discount").val("");
    
    for (seq = 0; seq < es_doc_line_count; seq++) {
        var item_id = get_item_id(get_sub_control("id", seq.toString()));
        if (hs.isEmpty(item_id))
            continue;

        get_sub_control("di", seq.toString()).val("");
        on_item_unit_changed(get_sub_control("un", seq.toString()),seq);
    };

}


function on_paytype_changed(e) {
    var pt = hs.e.$(e).val();
    //show_on_list_value('pay_type', '2', 'r_check_no;r_check_date;', 'r_check_no;r_check_date;', 'pay_type', true);

    var is_cash = hs.isEmpty(pt) || pt == '1';
    var cl;
       
    if (is_cash) {
        $("#r_check_no, #r_check_date, #r_post_method").hide();
        cl = new CodeList(es.tlrs);
    }
    else {
        $("#r_check_no, #r_check_date, #r_post_method").show();
        cl = new CodeList(es.banks);
    };
    
    
    cl.fillSelectList('cash_acc_no');
}

// *** offline pos print




function pos_offline_print_items() {

    var s = '';
    var seq, total;
    var item_id, item, item_unit;

    var cl_item_units = new CodeList(es_item_units);

    for (seq = 0; seq < es_doc_line_count; seq++) {
        total = hs.safe.parseFloat(get_sub_control("st", seq.toString()).val());
        if (!isNaN(total)) {
            item_id = get_item_id(get_sub_control("id", seq)); // ;
            if (hs.isEmpty(item_id))
                item_id = get_sub_control("id", seq).val();
            item_unit = cl_item_units.getTitle(get_sub_control("un", seq).val());
            item = get_item_by_id(item_id);

            if (item != null)
                s += '<tr><td>' + item.item_name + '</td><td>'+ item_unit +'</td><td>'+get_sub_control("up", seq).val()+'</td><td>' + get_sub_control("uc", seq).val() +'</td><td>' + get_sub_control("st", seq).val() + '</td></tr><tr>';
        };
    };

    return s;

}

// --------------------------

function get_pos_print_hdr() {
    var s = '';

    s += '<!DOCTYPE html>';
    s += '<html lang="en">';

    s += '<head>';
    
    /*
            s += '<link rel="stylesheet" href="/app/s/jquery/jquery-ui.min.css" ><\/script>';
            s += '<script type="text/javascript" src="/app/s/jquery/jquery-1.11.1.min.js"><\/script>';
            s += '<script type="text/javascript" src="/app/s/jquery/jquery-ui.min.js" ><\/script>';
            s += '<script type="text/javascript" src="/app/s/plugins/timepicker/jquery.timepicker.min.js" ><\/script>';
    */

    s += '<link rel="stylesheet" type="text/css" href="http://localhost:2080/app/s/css/hs_app.css" />';
    s += '<link href="http://localhost:2080/app/s/css/hs_app_ar.css" rel="stylesheet" type="text/css" />';
   
    s += '<style type="text/css">';
    s += '* {font-size: 7pt;font-family: Tahoma;color: #333;} body {width: 7cm !important; overflow:scroll;} page-content {margin: 0 !important; padding: 0 !important;}';
    s += 'body { vertical-align: middle; border-collapse:collapse; background: url(none) repeat  #fff;margin: auto; font-family: Tahoma; height: 100%; } ';
    s += 'div.full-screen-box { position:relative; top:0; bottom:0;   margin: auto; width: auto !important; } ';
    s += '#page {position:relative; width:100%; height:auto;  overflow:visible;  margin: auto; } ';
    s += '</style>';

    s += '<title>pos-offline-print</title>';
    s += '</head>';
    s += '<body><form>';

    //  s += 'This is a test...';

    s += '<center class="no-print"><br/><a id="but-print" style="width: 200px;" class="no-print icon_print link_icon ui-hilight" href="javascript:window.print();">طباعة</a><br/></center>';

    s += '<div class="full-screen-box"><div id="page"><div class="page-content">';

    // client info
    s += '<table class="doc-header"><tr><td>الجمهورية اليمنية<br/>صنعاء</td><td class="doc-logo"><img class="logo " src="http://localhost:2080/store/1077_sys-cfg_hs-sys-clt_logo84.jpg" alt="الشعار" />hello...</td><td>Yemen Republic<br/>Sanaa</td></tr></table>';
    s += '<div class="doc-title">مبيعات عبر نقاط البيع</div>';
    s += '<div class="doc-content"><table class="easyform">';
    s += '<tr id="r_sale_doc_no" class="fld"><td class="tit">رقم المستند</td><td class="val">' + hs.fm.val("doc_no") + '</td></tr>';
    s += '<tr id="r_cust_name" class="fld"><td class="tit">العميل</td><td class="val">' + $("#cov_name").val() + '</td></tr>';
    

    // items
    s += '<tr id="r_items" class="fld"><td colspan=2 class="val">';
    s += '<table class="rep_tab child-items-list pos"><tr class="head"><td>الصنف</td><td>الوحدة</td><td>السعر</td><td>الكمية</td><td>الإجمالي</td></tr>';
    s += pos_offline_print_items();
    s += '<tr class="head"><td></td><td colspan=3>الإجمالي&nbsp;&nbsp;:&nbsp;&nbsp;<span id="inv_total">' + hs.fm.val("price") + '</span></td><td></td></tr></table></td></tr>';

    // amount details
    // s+='';

    s += '<tr class="fld"><td colspan=2 class="val">' + hs.fm.val("doc_note") + '</td></tr>'

    s += '</table></div>'; // form

    s += '</div></div></div>';

    s += '</form>';

    // auto print dlg 
    
    s += '<script> window.print(); <\/script>';

    s += '</body></html>';

    // window.alert(s);

    return s;
};


function offline_print_pos() {
    var s = "";

    s = get_pos_print_hdr();

    s = hs.ui.fmtFrameDataUrl(s);

    s = "<iframe frameborder='0' scrolling='auto' marginwidth='0' style='width:100%; height:100%;' src='" + s + "' />";

    // ShowContentDialog(s, 400, 700);
    hs.ui.showContentDlg('', s);

    //ShowDlg(s);


    //hs.ui.setFrameHtml('iframe1', s);
}


function filter_grid_items(e) {

    var filter = $(e).val();
     
    $("a.tit > span").each(function () {
        if (hs.isEmpty(filter) || $(this).text().contains(filter))
            $(this).parents("div.doc-inline-item").show();
        else
            $(this).parents("div.doc-inline-item").hide();
    });

    
}


function filter_grid() {
    window.setTimeout(function () {
        filter_grid_items($("#g-f"));
    }, 500);
}


function on_item_type_changed() {

    if (getUrlParameter("cmd") != "edit")
        return;

    var item_type = $("#item_type").val();

    if (item_type == "S") { // service
        $("#u1_cost").attr('readonly', null);
        
    }
    else { // Material
    }

   // window.alert(item_type);

}

function goto_first_vacant_line() {
    var seq;
    for (seq = 0; seq < es_doc_line_count; seq++) {
        var item_id = get_item_id(get_sub_control("id", seq.toString()));
        if (hs.isEmpty(item_id)) {
            get_sub_control("id", seq.toString()).focus();
            break;
        };
       
    };
}

function config_es_doc_hotkeys() {

    hs.R.press_ctrl_help_extra = '   F1 : فاتورة جديدة';
    hs.R.press_ctrl_help_extra += ' | F4 : الصنف التالي';
    hs.R.press_ctrl_help_extra += ' | F6 : العميل';
    hs.R.press_ctrl_help_extra += ' | F8 : المبلغ الدفوع';
    hs.R.press_ctrl_help_extra += ' | F12 : حفظ الفاتورة';

    hs.R.press_ctrl_help = '';

    $(document).keydown(function (e) {
        // e.ctrlKey &&
        if (e.which === K.F1) {
            var a = $("a.icon_add.new-page");
            if (a.length > 0) {
                var url = a.attr("href");
                url = appendUrlParam(url, "sind=y");

                window.open(url);

                hs.absorb(e);
                return;
            }
        };

        if (e.which === K.F6) { // F6
            $("#cov_name").focus().click();
            hs.absorb(e);
            return;
        };

        if (e.which === 119) { //  F8 = Pay
            $("#paid").focus().select();
            hs.absorb(e);
            return;
        };

        if (e.which === K.F4) { //  F8 = Pay
            goto_first_vacant_line();
            hs.absorb(e);
            return;
        };

       

        
    });
}


function get_cust_discount_pct(cust_id) {

   // hs.msg.info(cust_id);
    
    if (!use_sale_price)
        return 0;

    var cl_cust_disc_pct = new CodeList(es_cust_disc_pct);

    var pct = cl_cust_disc_pct.getTitle(cust_id);
    if (pct == null)
        return 0;

    return hs.safe.parseFloat(pct);


}

function on_pricing_policy_changed() {

    var pricing_policy = $("#pricing").val();

    if (pricing_policy == "00") {
        $("#discount").val("0");
        hs.e.disable("discount");
    };

    if (pricing_policy == "01") {
       hs.e.enable("discount", true);
    };

    if (pricing_policy == "03") {
        hs.e.disable("discount");
    };

    on_item_price_changed(get_sub_control("up", 0));
}

function on_cust_changed(el, move) {
   // hs.msg.toast('cust changed..');

    on_item_price_changed(get_sub_control("up", 0));
}


function get_doc_type() {
    return $("#_doc_type").val();
}

// ******** Fin Doc ******* //

var fi_doc_line_count = 100;
var fi_cl_sub_accs = null;
var FI_LINES_FLD = null;

function fi_line_fld(fld_key, seq) {
    seq = adjustLineSeq(seq);

    return $("#" + seq + "_" + FI_LINES_FLD + "_" + fld_key);
}

function get_fi_doc_amount_cc() {
    var amnt = hs.safe.parseFloat($("#amount").val());
    var crncy = $('#doc_crncy').val();
    return convert_amount_to_cc(amnt, crncy);
}

function on_acc_list_changed(theList, move_to_next_item) {

    if (!hs.isEmpty(hs.e.$(theList).val()))
        hs.msg.removeAll(); // for POS and offline mode

    // if readonly no need to do anything
    if (hs.e.isReadonly(theList))
        return;

    if (move_to_next_item)
        focus_next(theList);
}

function on_acc_list_open(e, theExList) {
        
    // if readonly no need to do anything
    if (hs.e.isReadonly(e))
        return;

    var seq = getLineSeq(e);
    var at = $(fi_line_fld("at", seq)).val();

   
    
    theExList.options.codelist = FiAcc.getAnals(at);
   
    hs.msg.toast("AccType=" + at);
}



function fi_init_doc(lines_count, isMobile, fld_name) {
    // hs.msg.toast("init fi doc..");

    var doc_type = get_doc_type();

    fi_cl_sub_accs = FiAcc.getSubs();

    fi_doc_line_count = lines_count;
    FI_LINES_FLD = fld_name;

    
    

    $("#amount, #doc_crncy, #doc_crncy_exrate").on("change", function () { fi_recalc_all_lines(); });

    var cl_acc = new CodeList(fi_cl_sub_accs);

    var i;
    for (i = 0; i < lines_count; i++) {
        var seq = i.toString();

        var ac_fld = fi_line_fld("ac", seq);

        if (isMobile) {
            cl_acc.fillSelectList($(ac_fld));
            
            $(ac_fld)
                .attr("onchange", "on_acc_list_changed(this,true)");
        }

        else {
            $(ac_fld).exList({
                codelist: fi_cl_sub_accs,
                select_only: true,
                auto_open_on_focus: true,
                on_item_changed: on_acc_list_changed,
                on_focus: on_acc_list_open

            }).minLen(1);
        };

        $(fi_line_fld("cy", seq)).attr("onchange", "on_fi_crncy_changed(this)").minLen(1);
        $(fi_line_fld("xr", seq)).attr("onchange", "fi_recalc_line(this)");

        $(fi_line_fld("da", seq)).attr("onchange", "fi_recalc_line(this)");
        $(fi_line_fld("ca", seq)).attr("onchange", "fi_recalc_line(this)");

        $(fi_line_fld("dl", seq)).attr("onchange", "fi_line_amnt_cc_changed(this,'da')");
        $(fi_line_fld("cl", seq)).attr("onchange", "fi_line_amnt_cc_changed(this,'ca')");


        if (doc_type == '101') // cash rcpt
            $(fi_line_fld("ca", seq)).minLen(1);

        if (doc_type == '102') // pay vouch
            $(fi_line_fld("da", seq)).minLen(1);
    };


    fi_recalc_all_lines();
}

function fi_line_amnt_cc_changed(e,ac_amnt_fld) {
    hs.msg.toast("fi-line-cl-changed");

    var seq = getLineSeq(e);

    var crncy = $(fi_line_fld("cy", seq)).val();


    var ex_rate = hs.safe.parseFloat($(fi_line_fld("xr", seq)).val());
    var ac_amnt = hs.safe.parseFloat($(fi_line_fld(ac_amnt_fld, seq)).val());
    var cc_amnt = hs.safe.parseFloat($(e).val());

    if (cc_amnt == 0)
        return;

    if (hs.isEmpty(crncy) || ( ex_rate == 0 && ac_amnt == 0)) {
        hs.msg.tip($(e), "يجب تحديد العملة، والمبلغ أو سعر الصرف");
        return;
    };

    if (ac_amnt == 0) {
        ac_amnt = cc_amnt / ex_rate;
        $(fi_line_fld(ac_amnt_fld, seq)).val(hs.fmtAmount(ac_amnt));
    };

    if (ex_rate == 0) {
        ex_rate = cc_amnt / ac_amnt;
        $(fi_line_fld("xr", seq)).val(ex_rate.toString());
    };


    // ex_rate = get_exchange_rate(crncy);

    fi_recalc_all_lines();

}


function on_fi_crncy_changed(e) {
    var seq = getLineSeq(e);
    var crncy = hs.e.$(e).val();
    var doc_crncy = get_doc_crncy();
    var xr_val = get_exchange_rate(crncy);
    var xr_fld = fi_line_fld("xr", seq);

    $(xr_fld).val(xr_val);
   
    var ena_xr = crncy != es.CC && crncy != doc_crncy;
    // if (crncy == doc_crncy) 
       // ena_xr = false; // avoid crncy line/doc conflict

    hs.e.enable(xr_fld, ena_xr); 
    hs.e.enable(fi_line_fld("cl", seq), ena_xr);
    hs.e.enable(fi_line_fld("dl", seq), ena_xr);
    
    fi_recalc_line(e);
}


function fi_recalc_line(e) {
    var seq = getLineSeq(e);
    var crncy = $(fi_line_fld("cy", seq)).val();
    var ex_rate = hs.safe.parseFloat($(fi_line_fld("xr", seq)).val());

    if (ex_rate == 0)
        ex_rate = get_exchange_rate(crncy);

    var cl_val = hs.safe.parseFloat($(fi_line_fld("ca", seq)).val()) * ex_rate;
    var dl_val = hs.safe.parseFloat($(fi_line_fld("da", seq)).val()) * ex_rate;

    $(fi_line_fld("cl", seq)).val(hs.fmtAmount(cl_val));
    $(fi_line_fld("dl", seq)).val(hs.fmtAmount(dl_val));

    fi_recalc_all_lines();

}

function fi_recalc_all_lines() {

    if (!hs.e.exists("tot_debit")) 
        return;
    
    var doc_type = get_doc_type();
    var seq;
    var tot_debit = 0, tot_credit = 0, tot_diff=0;
    for (seq = 0; seq < fi_doc_line_count; seq++) {
        tot_debit += hs.safe.parseFloat($(fi_line_fld("dl", seq)).val());
        tot_credit += hs.safe.parseFloat($(fi_line_fld("cl", seq)).val());
    };

    if (doc_type == '101') // cash rcpt
        tot_debit = get_fi_doc_amount_cc();

    if (doc_type == '102') // pay vouch
        tot_credit = get_fi_doc_amount_cc();
    
    tot_diff = tot_credit - tot_debit;

    $("#tot_debit").val(hs.fmtAmount(tot_debit));
    $("#tot_credit").val(hs.fmtAmount(tot_credit));
    $("#tot_diff").val(hs.fmtAmount(Math.abs(tot_diff)));

}


// **** End: Fin Doc ****** //