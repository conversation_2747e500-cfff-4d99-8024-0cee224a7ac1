﻿<?xml version="1.0" encoding="utf-8"?>
<SystemHelp xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
 <items>

<Entry>
     <id>doc_type</id>
     <title>نوع المستند</title>
     <content>
       <![CDATA[
كل نوع من أنواع المستندات له رقم مميز له عن بقية الأنواع، وهذا الرقم يتكون من ثلاثة أحرف

فمثلا رقم نوع فاتورة مبيعات هو 201 و سند القبض 101 

وهذا الرقم يحفظ آليا مع بيانات  المستند، و يرتبط نوع المستند مع رقم المستند في كل العمليات التي تجري على المستند.

<a href='/app/fms/?fm=es-biz-doc&cmd=list-doc-types'>انواع المستندات التي يدعمها النظام</a>
]]>
     </content>
   </Entry>


   <Entry>
     <id>doc_subtype</id>
     <title>فئة المستند</title>
     <content>
       <![CDATA[
       يمكن أن يتم تقسيم نوع المستند الواحد إلى عدة فئات، مثلا فاتورة البيع يمكن أن تكون نقدية أو آجلة،
       
       ويمكن إضافة عدة فئات للنوع الواحد من المستندات حسب ماتتطلبه إحتياجات الشركة، وتقسيم المستند إلى عدة فئات يوفر إمكانية الحصول على تقارير وإحصائيات حسب كل فئة

]]>
     </content>
   </Entry>



   <Entry>
<id>doc_no</id>
<title>رقم المستند</title>
<content>
  <![CDATA[
رقم المستند: هو الرقم المميز للمستند  في النظام حيث يمكن إستخدامه في البحث عن المستند او الفاتورة، 
بشكل تلقائي يتم إعداد النظام على التسلسل الآلي للفواتير، و يكون هذا الحقل مخفيا عند الإدخال، حيث يقوم النظام بإنشاء أرقام تسلسلية للفواتير تبدأ بالرقم المحدد في إعدادات النظام ، وكل نوع من أنواع الفواتير له تسلسله الخاص به.
يمكن إعداد النظام على إظهار هذا الحقل وأيضا تعطيل الترقيم الآلي للفواتير بحيث يتم إجبار المستخدم على الترقيم اليدوي للفواتير.

في النسخة الإحترافية، يمكن عمل تسلسل خاص للفواتير حسب السنة/الفرع/الصندوق/المخزن/الفئة (أو أي خليط من هذه البيانات). ويمكن إعداد هذا على مستوى نوع الفاتورة.
]]></content>
   <linked>manual_doc_no;base_sales_doc_cntr;base_fin_doc_cntr</linked>
</Entry>

<Entry>
<id>doc_date</id>
<title>تاريخ المستند</title>
<content>
  <![CDATA[

التاريخ الفعلي للمستند، أي التاريخ الذي حصلت فيه العملية التي يعبر عنها المستند ، ويجب التفريق بين تاريخ المستند وتاريخ السجل – حيث أن تاريخ السجل يحدد التاريخ الذي تم فيه إضافة المستند للنظام، و يقوم النظام آليا بتسجيله بتاريخ النظام.   

للتسهيل يقوم النظام آليا بتعيين تاريخ الجهاز الرئيسي للنظام بشكل تلقائي كقيمة أولية لهذا الحقل، مع إمكانية تغييره إلى التاريخ الفعلي للمستند، وكون هذا التاريخ سيحدد الفترة المحاسبية التي سوف تقيد بها العمليه من الناحية المالية، لذا يجب أن تكون الفترة المحاسبية مفتوحة، عدا ذلك لن يتم ترحيل المستند. وإذا تم إعداد النظام لعمل فحص إمكانية ترحيل المستند قبل الحفظ، فإنه لن يتم حفظ المستند حتى يتم فتح الفترة المحاسبية.


]]></content>
</Entry>


   <Entry>
     <id>draft</id>
     <title>مسودة</title>
     <content>
       <![CDATA[
عند تفعيل خيار مسودة/قالب سوف يتم السماح بحفظ المستند مع وجود تحذيرات وأخطاء لها علاقة بكميات الأصناف أو مديونيات العملاء، أو أرصدة الحسابات أو إعدادات النظام عموما، يمكن تفعيل هذا الخيار لتجنب فقدان البيانات المدخلة عند عدم القدرة على حفظ المستند بشكل طبيعي.  وأيضا إذا كان المستخدم يشعر أن المستند غير مكتمل البيانات و يريد أن يقوم بإكماله لاحقا. 

سوف يتم حفظ الفاتورة كمسودة (مستند أولي) عند تفعيل خيار (مسودة)، و سوف يتعامل النظام مع هذا المستند على أساس أنه غير مكتمل البيانات، وبالتالي: 
-	سوف يسمح النظام بالحفظ حتى مع وجود تحذيرات وأخطاء نتيجة القيود المتعلقة بكميات المخزن أو ارصدة  العملاء والحسابات. 
-	لن يقوم النظام بعمل فحص إمكانية ترحيل المستند قبل الحفظ.
-	سوف يتم إيقاف الإجراءات الآلية على المستند مثل الترحيل وتطبيق التاثير المخزني.
-	إذا تم تعديل المستند على أنه مسودة بعد حصول التأثير المخزني سابقا ، سيقوم النظام بإزالة التأثير المخزني السابق للفاتورة.
-	وبعد الحفظ سوف يسمح النظام فقط بـ (التعديل أو الحذف) لهذا المستند، وسوف يتم منع بقية الإجراءات (مثل الطباعة أو الموافقة أوالترحيل أو غيرها) 

وغالبا يتم اللجوء لهذا الخيار عند عدم القدرة على حفظ المستند بشكل طبيعي مثلا:
-	إعدادات النظام تفرض فحص إمكانية ترحيل المستند بينما أحد الحسابات المتأثرة موقف أو غير موجود.  
-	لا توجد كميات كافية من الصنف في المخازن، بسبب أن الكميات الموردة لم يتم تسجيلها في النظام بعد.
-	هناك تحذيرات متعلقة بحدود مديونية العميل أو أرصدة الحسابات، أو إعدادات الأصناف.


]]>
     </content>
   </Entry>

   <Entry>
     <id>suspended</id>
     <title>تعليق المستند</title>
     <content>
       <![CDATA[
المستند معلق: أي أنه لا يمكن تنفيذ أي إجراءات على هذا المستند حتى يتم إيقاف التعليق. ويمكن تعليق المستند في أي مرحلة حتى بعد الترحيل والطباعة. 
لتعليق المستند: يتم عرض المستند،  الذهاب إلى قائمة الأدوات /  تعليق.
و بمجرد تعليق المستند يظهر زر (أيقاف التعليق).
  
عند تعليق المستند، سوف يقوم النظام بتجميد المستند عند المرحلة التي تم فيها التعليق، و سوف يمنع تنفيذ أي إجراءات لاحقة على هذا المستند مثل (ترحيل، إلغاء ترحيل، تعديل، حذف، طباعة، تحقيق، استلام أو صرف اقساط ، عمل مرتجع و غيرها) على المستند إذا كان معلقا.

ويمنع أيضا إضافة مستندات مرتبطة بالمستند أو تعتمد على المستند بإعتباره المستند الرئيسي لها مثل:
- يمنع عمل مردود مبيعات/مشتروات من فاتورة مبيعات/مشتروات معلقة.
- يمنع عمل إستلام دفعة من عميل من خلال فاتورة مبيعات معلقة.
- يمنع دفع مبلغ لمورد من خلال فاتورة مشتروات معلقة.

يمكن الإطلاع أكثر على موضوع ترابط المستندات.

]]>
     </content>
   </Entry>


   <Entry>
     <id>doc_crncy</id>
     <title>عملة المستند</title>
     <content>
       <![CDATA[
الحقل الذي سيحدد عملة كل المبالغ في الفاتورة أو المستند،
(أسعار وتخفيضات الأصناف، مبلغ التخفيض، صافي المبلغ، المبلغ المدفوع نقدا، المبلغ المدفوع عبر الشبكة، المبلغ المتبقي).
للتسهيل يقوم النظام آليا بإختيار العملة المحلية (الرئيسية) للشركة المحددة عند إعداد العملات، وبإمكان المستخدم تحديد أي عمله أخرى.


]]>
     </content>
   </Entry>

   <Entry>
     <id>doc_crncy_exrate</id>
     <title>سعر التحويل لعملة المستند</title>
     <content>
       <![CDATA[

- عند تحديد عملة أجنبية في حقل عملة الفاتورة ، سيقوم النظام بإظهار حقل سعر التحويل، وهذا الحقل سيحدد سعر تحويل العملة الأجنبية إلى المحلية بالنسبة لهذه الفاتورة، وسيكون لهذا السعر تأثير مباشر على القيود المحاسبية الناتجة عن الفاتورة.
- عند تعطيل خيار (السماح بتغيير سعر التحويل في المستندات) من إعدادات المستندات لن يتم إظهار هذا الحقل.
- إذا لم يكن للمستخدم صلاحية تغيير سعر التحويل في الفاتورة، فسيتم إخفاء هذا الحقل و لن يتمكن المستخدم من تغيير سعر التحويل،
- يجب أن يكون سعر التحويل المحدد في الفاتورة بين الحد الأدنى والأعلى لسعر التحويل المحدد للعملة في بيانات العملات. 
- عند عدم تحديد أي سعر، سيقوم النظام بإستخدام سعر التحويل الحالي للعملة والمحدد في بيانات العملات.

]]>
     </content>
   </Entry>

   <Entry>
     <id>cash_id</id>
     <title>الصندوق</title>
     <content>
       <![CDATA[
صندوق النقدية الذي سوف يتأثر بالعمليات النقدية التي في المستند (قبض أو صرف)  للتسهيل يقوم النظام آليا بإختيار الصندوق التلقائي للمستخدم والذي تم تحديده من االبيانات الإضافية للمستخدم (في إدارة المستخدمين)، ويمكن للمستخدم التغيير إلى أي صندوق آخر بشرط أن يكون لديه صلاحيات على الصندوق.
الصندوق يرتبط بحساب مالي بعملات معينة، لذا يجب أن تكون عملة الفاتورة مطابقة لأحدى عملات الصندوق.
إذا تم تعطيل خيارات المحاسب من (الإعدادات/الفواتير والمستندات/متقدم)، فإن المستخدم لن يتمكن من تغيير الصندوق، وسيتم حصره على الصندوق التلقائي المحدد له . 

]]>
     </content>
   </Entry>

   <Entry>
     <id>store_id</id>
     <title>المخزن</title>
     <content>
       <![CDATA[
       المخزن الذي سيتم صرف الأصناف المخزنية منه عند البيع أو الصرف، و سيتم توريد الأصناف له في المشتروات والتوريد والمخزون الإفتتاحي. 
وللتسهيل  يقوم النظام بإختيار المخزن التلقائي للمستخدم ، ويمكنه تحديد أي مخزن آخر بشرط أن يكون لديه صلاحيات على المخزن المحدد.

يمكن تحديد المخزن التلقائي للمستخدم من بيانات المستخدم الإضافية، وكذلك يمكن تحديد بقية المخازن التي يراد أن يتم إعطاء المستخدم صلاحيات عليها. إذا لم يكن للمستخدم صلاحيات على المخزن فلن يستطيع عرض أي مستند مرتبط بهذا المخزن، وللسماح له بعرض المستندات المرتبطة بالمخازن التي ليس صلاحيات عليها، يمكن منحه صلاحية  عرض مستندات كل المخازن من خلال  (إدارة الصلاحيات – الحسابات – عرض مستندات – كل المخازن).. ولإعطائه صلاحية عرض مخازن معينه فقط يمكن الإطلاع على (الصلاحيات المتقدمة على المستندات).

]]>
     </content>
   </Entry>

   <Entry>
     <id>ref_no</id>
     <title>رقم المرجع</title>
     <content>
       <![CDATA[
حقل يمكن إستخدامه لربط الفاتورة في النظام بالفاتورة الورقية، أو لأي غرض أخر حسب سياسة الشركة.

وحسب إعدادات النظام يمكن أن يكون هذا الحقل إجباريا أو إختياريا. و يمكن إيضا إعداد النظام بعدم السماح بتكرار نفس المرجع لنفس العميل أو المورد. وهذه تفيد مثلا في المشتروات بإدخال رقم فاتورة المورد و بحيث يمنع النظام تكرار إدخال نفس الفاتورة  

لمزيد من المعلومات: ابحث عن (رقم المرجع) في الإعدادات

]]>
     </content>
   </Entry>

   

   <Entry>
     <id>doc_note</id>
     <title>ملاحظات المستند</title>
     <content>
       <![CDATA[
أي ملاحظات إضافية حول المستند، وسوف تظهر في اليومية العامة، وكشف الحساب الخاص بالعميل أو المورد.

في المستندات المالية (الصرف والقبض والقيود اليومية) سوف يظهر البيان الذي على مستوى القيد، وسيظهر البيان الذي على مستوى السند في حال لم يتم تحديد بيان مخصص 

]]>
     </content>
   </Entry>

   <Entry>
     <id>cov_no</id>
     <title>رقم الحساب</title>
     <content>
       <![CDATA[
رقم الحساب الذي سوف تقيد عليه أو له الحركات المالية.. 

وهذا الحقل سيكون مطلوبا في حال وجود مبلغ آجل في الفاتورة، وغير مطلوب في الفواتير النقدية، ولكن يمكن إدخاله أيضا لغرض الإحصاءات

بالنسبة لفواتير المبيعات :
هذا سيكون رقم حساب العميل  الذي سوف تقيد عليه المبالغ المالية المتبقية من فواتير المبيعات، وتقيد له المبالغ في مردود المبيعات.


بالنسبة لفواتير المشتروات :
هذا سيكون رقم حساب المورد  الذي سوف تقيد له المبالغ المالية المتبقية من فواتير المشتروات، وتقيد عليه المبالغ في مردود المشتروات.


بالنسبة لمستندات المخزون :
هذا الحقل إجباري و يجب إختياره




]]>
     </content>
   </Entry>
   

      <Entry>
     <id>cov_name</id>
     <title>اسم الحساب</title>
     <content>
       <![CDATA[
اسم العميل أو المورد أو الحساب المرتبط بالمستند، وذلك حساب نوع المستند


]]>
     </content>
   </Entry>


   <Entry>
     <id>sales_cov_no</id>
     <title>رقم العميل</title>
     <linked>ex_list_use</linked>
     <content>
       <![CDATA[
في هذا الحقل يتم تحديد العميل الذي تم التعامل معه من خلال هذه الفاتورة، وهذا الحقل يكون إجباريا فقط عند وجود مبلغ متبقي حيث سيتم تقييد المبلغ على حساب العميل. وإذا لم يكن هناك مبلغ متبقي فيمكن ترك الحقل فارغا، أو إدخال اسم العميل (الغير مسجل في النظام) وذلك كي يظهر عند طباعة فاتورة المبيعات.
لإظهار رصيد العميل/المورد تلقائيا بمجرد إختيار العميل/المورد يتم تفعيل الخيار  (إظهار رصيد العميل/المورد آليا - بمجرد إختياره في الفاتورة) من خيارات فواتير المبيعات /المشتروات.

]]>
     </content>
   </Entry>

   <Entry>
     <id>purch_cov_no</id>
     <title>رقم المورد</title>
     <linked>ex_list_use</linked>
     <content>
       <![CDATA[
 في هذا الحقل يتم تحديد المورد الذي تم التعامل معه من خلال هذه الفاتورة، وهذا الحقل يكون إجباريا فقط عند وجود مبلغ آجل حيث سيتم تقييد المبلغ لحساب المورد عند الشراء و على حساب المورد عند المردود. وإذا لم يكن هناك مبلغ متبقي فيمكن ترك الحقل فارغا، أو إدخال اسم المورد (الغير مسجل في النظام) وذلك كي يظهر عند طباعة فاتورة المشتروات.

   ويمكن إدخال حساب المورد في الفواتير النقدية لغرض الإحصائيات، ولن يكون له أي تأثير مالي.

   لإظهار رصيد المورد تلقائيا بمجرد إختياره في الفاتورة يتم تفعيل الخيار  (إظهار رصيد المورد/المورد آليا - بمجرد إختياره في الفاتورة) من خيارات نظام المشتروات .

]]>
     </content>
   </Entry>

   <Entry>
     <id>stock_cov_no</id>
     <title>رقم الحساب</title>
     <linked>ex_list_use</linked>
     <content>
       <![CDATA[
رقم الحساب الذي سوف تقيد عليه أو له الحركات المالية المرتبطة بالحركة المخزنية، وهذا الحقل إجباري للمستندات التي لها تأثير مالي مرتبط بالمخزون مثل أوامر الصرف والتوريد المخزني
]]>
     </content>
   </Entry>



   


   <Entry>
     <id>doc_charges</id>
     <title>أعباء الفاتورة</title>
     <content>
       <![CDATA[
الأعباء هي المبالغ الإضافية المرتبطة بعملية البيع مثل تكاليف النقل أو التحميل أو الأجور الإضافية وخلافه، و يتم إضافة المبلغ على  إجمالي مبلغ الفاتورة

هذا المبلغ لا يخضع للتخفيض

]]>
     </content>
   </Entry>


   <Entry>
     <id>charges</id>
     <title>الأعباء الإضافية على الفاتورة</title>
     <content>
       <![CDATA[
الأعباء هي المبالغ الإضافية المرتبطة بعملية البيع أو الشراء مثل تكاليف النقل أو التحميل أو الأجور الإضافية وخلافه، 
وتختلف عن أعباء الفاتورة إنه يمكن تحميلها على  حساب العميل أو أي حساب آخر حسب سياسة الشركة، و يمكن عمل عدة أعباء على نفس الفاتورة و يتم تحميلها على حسابات مختلفة



]]>
     </content>
   </Entry>

   <Entry>
     <id>discount</id>
     <title>التخفيض</title>
     <content>
       <![CDATA[
   يمكن تحديد مبلغ التخفيض أو تحديد نسبة مئوية و سيقوم النظام بتحويل النسبة المئوية إلى مبلغ التخفيض مباشرة
  
  لعمل نسبة مئوية معيارية يمكن كتابة الرقم متبوعا بـ %  
لعمل نسبة غير معيارية يمكن كتابة الرقم متبوعا بـ %% (مستخدمة بشكل شائع لدى شركات الأدوية – ويكون التخفيض أقل من النسبة المئوية المعيارية) 
علما أن النسبة المئوية سوف تطبق على الأصناف الخاضعة للتخفيض فقط.
النظام يحتسب نسبة التخفيض على سعر البيع للصنف و يمكن إعداد النظام لإحتساب نسبة التخفيض بناءا على سعر كلفة الصنف. 

إذا كان هذا حقل التخفيض غير مفعل فهذا يعني أن المستخدم ليس لديه صلاحية لعمل تخفيض، يمكن إضافة هذه الصلاحية من البيانات الإضافية للمستخدم، وذلك بتحديد النسبة المئوية المسموحة للمستخدم

]]>
     </content>
     <linked>sales_doc_opts5</linked>
   </Entry>
   

   <Entry>
     <id>paid_installs</id>
     <title>الأقساط الواصلة</title>
     <content>
       <![CDATA[
يشمل الأقساط المدفوعة بالسندات المالية (القبض أو الصرف) + الأقساط المخفضة عبر قسائم التخفيضات
]]>
     </content>
   </Entry>

   <Entry>
     <id>total_paid</id>
     <title>إجمالي الواصل</title>
     <content>
       <![CDATA[
إجمالي الواصل من مبلغ الفاتورة وهذا يشمل المبلغ المدفوع عند إضافة الفاتورة + مجموع الأقساط الواصلة

المدفوع عند إضافة الفاتورة :   المبلغ المدفوع نقدا + المبلغ المدفوع عبر شبكة
الأقساط الواصلة: سواء المدفوعة عبر السندات المالية أو المخفضة عبر قسائم التخفيضات.
]]>
     </content>
   </Entry>

   

   <Entry>
     <id>unpaid_installs</id>
     <title>إجمالي الباقي</title>
     <content>
       <![CDATA[
إجمالي الأقساط الباقية من الفاتورة، وهو يعبر عن المبلغ الفعلي المتبقي من الفاتورة
ويحسب هذا المبلغ: 
إجمالي الباقي = المبلغ المتبقي (المبلغ المؤجل عند إضافة الفاتورة) - الأقساط الواصلة
]]>
     </content>
   </Entry>

   
   <Entry>
     <id>due_date</id>
     <title>تاريخ الإستحقاق</title>
     <content>
       <![CDATA[
 تاريخ إستحقاق أول دفعة من أقساط الفاتورة الآجلة، سيتم طباعة هذا التاريخ في الفاتورة.
 
 في حال ترك هذا الحقل فارغا فإن تاريخ إستحقاق أول دفعة ستكون بعد 30 يوما من تاريخ الفاتورة
 
]]>
     </content>
   </Entry>
   

   <Entry>
     <id>last_pay_date</id>
     <title>تاريخ آخر دفعة</title>
     <content>
       <![CDATA[
 هذا الحقل يحدد التاريخ الذي تمت فيه آخر دفعة على الفاتورة الآجلة، حيث يظهر في الفواتير الآجلة فقط.
 
 تاريخ إستحقاق الدفعة القادمة سيكون بعد 30 يوما من تاريخ آخر دفعة
]]>
     </content>
   </Entry>


   <Entry>
     <id>branch</id>
     <title>الفرع</title>
     <content>
       <![CDATA[
     الفرع الذي سيتم ربط المستند أو الفاتورة به، من الناحية الإدارية فإن المستند أو الفاتورة ستكون تابعة لهذا الفرع وتحت صلاحياته، وللتسهيل يقوم النظام بإختيار الفرع التلقائي للمستخدم ، ويمكنه تحديد أي فرع آخر بشرط أن يكون لديه صلاحيات على الفرع المحدد.

- عند تحديد الفرع في المستند سيتم ربط كل العمليات المالية والمخزنية الناتجة عن الفاتورة بهذا الفرع، وبما يمكن الشركة من الحصول على تقارير وإحصائيات على مستوى الفروع.
- عند جعل الفرع إجباريا في المستندات سيمكن إستخراج التقارير المالية مثل المركز المالي والأرباح والخسائر حسب الفروع.

- يمكن تحديد فرع المستخدم بتحديد الفرع التلقائي للمستخدم من بيانات المستخدم الإضافية، وكذلك يمكن تحديد بقية الفروع التي يراد أن يتم إعطاء المستخدم صلاحيات عليها. إذا لم يكن للمستخدم صلاحيات على  الفرع فلن يستطيع عرض أي مستند مرتبط بهذا الفرع وللسماح له بعرض المستندات المرتبطة بالفروع التي ليس له صلاحيات عليها، يمكن منحه صلاحية عرض مستندات كل االفروع من خلال (إدارة الصلاحيات – الحسابات – عرض مستندات – كل الفروع).. ولإعطائه صلاحية عرض مستندات فروع معينه فقط يمكن الإطلاع على (الصلاحيات المتقدمة على المستندات).

- بشكل تلقائي يقوم النظام بعرض قائمة المستندات لكل الفروع لكل المستخدمين في النظام، ولكن لن يستطيع المستخدم سوى عرض المستندات التي تتبع الفروع الذي له صلاحيات عليها.
ولجعل  قائمة المستندات الخاصة بالفرع محصورة  فقط على المستخدمين التابعين للفرع، يجب تفعيل خيار (عزل الفروع) من إعدادات (المستندات والفواتير)، حيث سيتم عزل مستندات الفروع تماما عن بعضها، وسيتم حصر المستخدم على قائمة المستندات التي تتبع الفرع التابع له (الفرع التلقائي للمستخدم). وإذا كان حاجة لبعض المستخدمين من استعراض كافة الفروع أو فروع متعددة - يمكن جعل حقل (الفرع التلقائي للمستخدم فارغا) وبالتالي سيتم عرض قائمة كل المستندات للمستخدم، ولكنه لن يتمكن سوى من عرض المستندات التي تتبع الفروع التي له صلاحيات عليها.
  
]]>
     </content>
   </Entry>

   <Entry>
     <id>proj_id</id>
     <title>المشروع</title>
     <content>
       <![CDATA[
  المشروع الذي سيتم ربط المستند أو الفاتورة به، من الناحية الإدارية فإن المستند أو الفاتورة ستكون تابعة لهذا المشروع وتحت صلاحياته

- عند تحديد المشروع في المستند سيتم ربط كل العمليات المالية والمخزنية الناتجة عن الفاتورة بهذا المشروع، وبما يمكن الشركة من الحصول على تقارير وإحصائيات على مستوى المشاريع.
- عند جعل المشروع إجباريا في المستندات سيمكن إستخراج التقارير المالية مثل المركز المالي والأرباح والخسائر حسب المشاريع.

إذا لم يكن للمستخدم صلاحيات على  المشروع فلن يستطيع عرض أي مستند مرتبط بهذا المشروع وللسماح له بعرض المستندات المرتبطة بالمشاريع التي ليس له صلاحيات (الإضافة والتعديل) عليها، يمكن منحه صلاحية عرض مستندات كل االمشاريع من خلال (إدارة الصلاحيات – الحسابات – عرض مستندات – كل المشاريع).. ولإعطائه صلاحية عرض مستندات مشاريع معينه فقط يمكن الإطلاع على (الصلاحيات المتقدمة على المستندات).  
]]>
     </content>
   </Entry>


   <Entry>
     <id>actvty_id</id>
     <title>النشاط</title>
     <content>
       <![CDATA[
  النشاط الذي سيتم ربط المستند أو الفاتورة به، من الناحية الإدارية فإن المستند أو الفاتورة ستكون تابعة لهذا النشاط وتحت صلاحياته

- عند تحديد النشاط في المستند سيتم ربط كل العمليات المالية والمخزنية الناتجة عن الفاتورة بهذا النشاط، وبما يمكن الشركة من الحصول على تقارير وإحصائيات على مستوى الأنشطة.
- عند جعل النشاط إجباريا في المستندات سيمكن إستخراج التقارير المالية مثل المركز المالي والأرباح والخسائر حسب الأنشطة.

إذا لم يكن للمستخدم صلاحيات على  النشاط فلن يستطيع عرض أي مستند مرتبط بهذا النشاط وللسماح له بعرض المستندات المرتبطة بالأنشطة التي ليس له صلاحيات (الإضافة والتعديل) عليها، يمكن منحه صلاحية عرض مستندات كل االأنشطة من خلال (إدارة الصلاحيات – الحسابات – عرض مستندات – كل الأنشطة).. ولإعطائه صلاحية عرض مستندات أنشطة معينه فقط يمكن الإطلاع على (الصلاحيات المتقدمة على المستندات).
]]>
     </content>
   </Entry>


   <Entry>
     <id>cc_no</id>
     <title>مركز التكلفة</title>
     <content>
       <![CDATA[
مركز التكلفة الذي سيتم ربط المستند أو الفاتورة به، من الناحية الإدارية فإن المستند أو الفاتورة ستكون تابعة لهذا مركز التكلفة وتحت صلاحياته

- عند تحديد مركز التكلفة في المستند سيتم ربط كل العمليات المالية والمخزنية الناتجة عن الفاتورة بهذا مركز التكلفة، وبما يمكن الشركة من الحصول على تقارير وإحصائيات على مستوى مراكز التكلفة.
- عند جعل مركز التكلفة إجباريا في المستندات سيمكن إستخراج التقارير المالية مثل المركز المالي والأرباح والخسائر حسب المراكز.

إذا لم يكن للمستخدم صلاحيات على  مركز التكلفة فلن يستطيع عرض أي مستند مرتبط بهذا مركز التكلفة وللسماح له بعرض المستندات المرتبطة بالأنشطة التي ليس له صلاحيات (الإضافة والتعديل) عليها، يمكن منحه صلاحية عرض مستندات كل المراكز من خلال (إدارة الصلاحيات – الحسابات – عرض مستندات – كل مراكز التكلفة).. ولإعطائه صلاحية عرض مستندات مراكز معينه فقط يمكن الإطلاع على (الصلاحيات المتقدمة على المستندات).
]]>
     </content>
   </Entry>


   <Entry>
     <id>sales_cov_name</id>
     <title>اسم العميل</title>
     <content>
       <![CDATA[
اسم العميل في فواتير المبيعات ، وهو الاسم الذي سوف يطبع في الفاتورة و يظهر في القوائم.

.هذا الحقل يظهر مرتبط بحقل رقم العميل، حيث يظهران وكأنها حقل واحد، و في نفس الحقل يمكن إختيار عميل مسجل مسبقا في النظام، أو إضافة عميل جديد، 

أو فقط يمكن إدخال اسم العميل يدويا إذا كانت الفاتورة نقدية، أو تم قبض مبلغ الفاتورة بالكامل

في حال تم إختيار عميل مسجل في النظام سيقوم النظام بإدخال رقم و اسم العميل المسجل آليا. 

إذا تم تفعيل خيار (السماح بإدخال اسم العميل يدويا في فاتورة المبيعات) ، سيظهر النظام حقل اسم العميل بشكل مستقل عن حقل رقم العميل - و يمكن اختيار رقم حساب العميل، إلى جانب إدخال اسم العميل

]]>
     </content>
     <linked>sales_cov_no</linked>
   </Entry>


   <Entry>
     <id>purch_cov_name</id>
     <title>اسم المورد</title>
     <content>
       <![CDATA[
اسم المورد في فواتير المشتروات ، وهو الاسم الذي سوف يطبع في الفاتورة و يظهر في القوائم.

.هذا الحقل يظهر مرتبط بحقل رقم المورد، حيث يظهران وكأنها حقل واحد، و في نفس الحقل يمكن إختيار مورد مسجل مسبقا في النظام، أو إضافة مورد جديد، 

أو فقط يمكن إدخال اسم المورد يدويا إذا كانت الفاتورة نقدية، أو تم دفع مبلغ الفاتورة بالكامل

في حال تم إختيار مورد مسجل في النظام سيقوم النظام بإدخال رقم و اسم المورد المسجل آليا. 


]]>
     </content>
     <linked>purch_cov_no</linked>
   </Entry>


   




   

   <Entry>
     <id>ex_data_cov_bal</id>
     <title>الرصيد السابق للعميل</title>
     <content>
       <![CDATA[
       الرصيد السابق هو رصيد العميل عند حفظ الفاتورة. و يقوم النظام بقراءة الرصيد من حساب العميل.
       هذا الحقل لا يظهر بشكل تلقائي حيث يجب تفعيل الخيار ( إظهار حقل الرصيد السابق للعميل في الفاتورة ) لإظهار هذا الحقل في فواتير المبيعات وذلك من إعدادات نظام المبيعات.
      و لا يتم تغيير هذا الرصيد عند تعديل الفاتورة إلا إذا تم تفعيل الخيار : تحديث حقل الرصيد السابق للعميل في الفاتورة عند تعديل الفاتورة. وسيقوم النظام بإصدار تنبيه للمستخدم.
      إذا تم تفعيل خيار (طباعة رصيد العميل) فسوف يقوم النظام بطباعة الرصيد السابق وكذلك الرصيد الأجمالي شاملا المبلغ المتبقي على العميل في الفاتورة
       
       يجب التنبيه في حال طباعة حساب العميل في الفاتورة:
       - إنه يجب الإلتزام بترحيل مستندات العميل أولا بأول و بالترتيب كي يطابق الرصيد السابق في الفاتورة الرصيد الفعلي للعميل 
       - في حال عدم القدرة على الإلتزام - يفضل تعطيل هذا الخيار كي لا يسبب سوء فهم للعميل - فمثلا عند إصدار فاتورتين بدون ترحيل فإن الرصيد السابق سيظهر نفسه في الفاتورتين وسيعتقد العميل أن هناك خطأ
       
       

]]>
     </content>
   </Entry>


<!-- ************************ FI Acc Common ************************************** -->



   
   
   <Entry>
     <id>pnet_id</id>
     <title>شبكة الدفع</title>
     <content>
       <![CDATA[
     شبكات الدفع المقدمة من خلال البنوك و الصرافين، وكذلك شركات التأمين الطبية، و بطاقات الدفع الإلكترونية
     
     يمكن تقييد مبلغ غير نقدي على هذه الشبكة - مثل عند القبض او الدفع بشيك بنكي. يمكن تقييد المبلغ هنا بدلا من عمل سند قبض أو صرف.
]]>
     </content>
     <linked>enable_pay_networks</linked>
   </Entry>
   
   
        <Entry>
     <id>items</id>
     <title>جدول الأصناف في الفاتورة</title>
     <content>
       <![CDATA[

]]>
     </content>
   </Entry>
   
   
   
      <Entry>
     <id>price</id>
     <title>الإجمالي</title>
     <content>
       <![CDATA[
مبلغ إجمالي قيمة الأصناف المحددة في جدول الأصناف في الفاتورة
]]>
     </content>
   </Entry>
   
   

   
     <Entry>
     <id>net_amount</id>
     <title>المبلغ الصافي</title>
     <content>
       <![CDATA[
المبلغ الصافي المستحق فعلا على الفاتورة بعد خصم التخفيض  و إضافة الأعباء 
]]>
     </content>
   </Entry>

	 <Entry>
		 <id>es-sale-frd:paid</id>
		 <title>المبلغ المدفوع نقدا</title>
		 <content>
			 <![CDATA[
المبلغ المقبوض نقدا في فاتورة المبيعات ، في حال تقييد المبلغ هنا لا داعي لعمل سند قبض 
]]>
		 </content>
	 </Entry>
   
   
     <Entry>
     <id>paid</id>
     <title>المبلغ المدفوع نقدا</title>
     <content>
       <![CDATA[
في فاتورة المبيعات: 	   
المبلغ المقبوض نقدا من العميل ، المبلغ المقيد هنا لا يتطلب عمل سند قبض
إذا كان هناك حاجة لعمل سند قبض يجب أن يتم ترك هذا الحقل فارغا، و يتم قبض المبلغ بواسطة سند قبض. 

في فاتورة المشتروات:
المبلغ المدفوع نقدا للمورد، المبلغ المقيد هنا لا يتطلب عمل سند صرف
]]>
     </content>
   </Entry>
   
   
   
     <Entry>
     <id>rem_amount</id>
     <title> المبلغ المتبقي</title>
     <content>
       <![CDATA[
المبلغ الآجل وهو المبلغ الذي سيتم تقييده على العميل في فاتورة المبيعات  أو للمورد في فاتورة المشتروات
]]>
     </content>
   </Entry>
   
   
   <Entry>
     <id>pnet_amount</id>
     <title></title>
     <content>
       <![CDATA[
في فاتورة المبيعات : مبلغ غير نقدي تم قبضه بواسطة شيك أو تقييده على شركة التأمين الصحي أو على شركة الصرافة

في فاتورة المشتروات : مبلغ غير نقدي تم دفعه بواسطة شيك او حوالة نقدية

]]>
     </content>
   </Entry>
   
 
 
   <Entry>
     <id>doc_status</id>
     <title></title>
     <content>
       <![CDATA[
الحقل الذي يحدد حالة المستند أو الفاتورة  مثل جديد ، تمت الموافقة، او مرحل وخلافه
]]>
     </content>
   </Entry>
   
   
      <Entry>
     <id>print_times</id>
     <title>مرات الطباعة</title>
     <content>
       <![CDATA[
هذا الحقل يحدد عدد مرات طباعة المستند او الفاتورة. و يتم طباعته في اسفل الفاتورة.

هذا الرقم يزداد بمجرد الضغط على زر الطباعة - حتى لو لم تكتمل عملية الطباعة فعلا.. 

يمكن للمستخدم استخدام معاينة الطباعة لتجنب زيادة هذا الرقم
]]>
     </content>
   </Entry>
   
   
   
   
   
      <Entry>
     <id>ref_doc_type</id>
     <title>نوع المستند الرئيسي</title>
     <content>
       <![CDATA[
نوع المستند الرئيسي
]]>
     </content>
        <linked>ref_doc_no</linked>
   </Entry>
   
   
    <Entry>
     <id>ref_doc_no</id>
     <title>رقم المستند الرئيسي</title>
     <content>
       <![CDATA[
رقم المستند الرئيسي. يستخدم عند ربط المستند بمستند آخر.. 
مثلا عند عمل سند قبض على فاتورة مبيعات فإن النظام يحتفظ بهذه العلاقة بين المستندين..
و يمكن عرض قائمة المستندات المرتبط من قائمة الأدوات

هذا الحقل يكون إجباريا في كثير من الحالات مثل عند عمل مرتجع مبيعات من فاتورة معينه.. أو عند تحويل المستندات مثلا عند تحويل طلب مبيعات إلى فاتورة مبيعات - و ذلك لمراقبة الكميات..
فمثلا النظام يمنع عمل مرتجع بكميات أكبر من  الكميات المحددة في فاتورة المبيعات. حتى ولو كان هناك عدة مرتجعات على نفس الفاتورة..

وهذا ينطبق على كل المستندات المرتبطة بشكل إجباري

]]>
     </content>
   </Entry>

   <Entry>
     <id>amount</id>
     <title>المبلغ</title>
     <content>
       <![CDATA[
اجمالي مبلغ المستند بعملة المستند
]]>
     </content>
   </Entry>
   

   
   <!--   
   <Entry>
     <id></id>
     <title></title>
     <content>
       <![CDATA[

]]>
     </content>
   </Entry>
-->   
 </items>
</SystemHelp>

