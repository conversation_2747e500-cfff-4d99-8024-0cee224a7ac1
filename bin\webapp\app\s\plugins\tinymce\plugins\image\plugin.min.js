tinymce.PluginManager.add("image",function(a){function b(a,b){function c(a,c){d.parentNode&&d.parentNode.removeChild(d),b({width:a,height:c})}var d=document.createElement("img");d.onload=function(){c(Math.max(d.width,d.clientWidth),Math.max(d.height,d.clientHeight))},d.onerror=function(){c()};var e=d.style;e.visibility="hidden",e.position="fixed",e.bottom=e.left=0,e.width=e.height="auto",document.body.appendChild(d),d.src=a}function c(a,b,c){function d(a,c){return c=c||[],tinymce.each(a,function(a){var e={text:a.text||a.title};a.menu?e.menu=d(a.menu):(e.value=a.value,b(e)),c.push(e)}),c}return d(a,c||[])}function d(b){return function(){var c=a.settings.image_list;"string"==typeof c?tinymce.util.XHR.send({url:c,success:function(a){b(tinymce.util.JSON.parse(a))}}):"function"==typeof c?c(b):b(c)}}function e(d){function e(){var a,b,c,d;a=l.find("#width")[0],b=l.find("#height")[0],a&&b&&(c=a.value(),d=b.value(),l.find("#constrain")[0].checked()&&o&&p&&c&&d&&(o!=c?(d=Math.round(c/o*d),isNaN(d)||b.value(d)):(c=Math.round(d/p*c),isNaN(c)||a.value(c))),o=c,p=d)}function f(){function b(b){function c(){b.onload=b.onerror=null,a.selection&&(a.selection.select(b),a.nodeChanged())}b.onload=function(){s.width||s.height||!u||t.setAttribs(b,{width:b.clientWidth,height:b.clientHeight}),c()},b.onerror=c}var c,d;j(),e(),s=tinymce.extend(s,l.toJSON()),s.alt||(s.alt=""),s.title||(s.title=""),""===s.width&&(s.width=null),""===s.height&&(s.height=null),s.style||(s.style=null),s={src:s.src,alt:s.alt,title:s.title,width:s.width,height:s.height,style:s.style,caption:s.caption,"class":s["class"]},a.undoManager.transact(function(){function e(b){return a.schema.getTextBlockElements()[b.nodeName]}if(!s.src)return void(m&&(t.remove(m),a.focus(),a.nodeChanged()));if(""===s.title&&(s.title=null),m?t.setAttribs(m,s):(s.id="__mcenew",a.focus(),a.selection.setContent(t.createHTML("img",s)),m=t.get("__mcenew"),t.setAttrib(m,"id",null)),a.editorUpload.uploadImagesAuto(),s.caption===!1&&t.is(m.parentNode,"figure.image")&&(c=m.parentNode,t.insertAfter(m,c),t.remove(c)),s.caption!==!0)b(m);else if(!t.is(m.parentNode,"figure.image")){d=m,m=m.cloneNode(!0),c=t.create("figure",{"class":"image"}),c.appendChild(m),c.appendChild(t.create("figcaption",{contentEditable:!0},"Caption")),c.contentEditable=!1;var f=t.getParent(d,e);f?t.split(f,d,c):t.replace(c,d),a.selection.select(c)}})}function g(a){return a&&(a=a.replace(/px$/,"")),a}function h(c){var d,e,f,g=c.meta||{};q&&q.value(a.convertURL(this.value(),"src")),tinymce.each(g,function(a,b){l.find("#"+b).value(a)}),g.width||g.height||(d=a.convertURL(this.value(),"src"),e=a.settings.image_prepend_url,f=new RegExp("^(?:[a-z]+:)?//","i"),e&&!f.test(d)&&d.substring(0,e.length)!==e&&(d=e+d),this.value(d),b(a.documentBaseURI.toAbsolute(this.value()),function(a){a.width&&a.height&&u&&(o=a.width,p=a.height,l.find("#width").value(o),l.find("#height").value(p))}))}function i(a){if(a.margin){var b=a.margin.split(" ");switch(b.length){case 1:a["margin-top"]=a["margin-top"]||b[0],a["margin-right"]=a["margin-right"]||b[0],a["margin-bottom"]=a["margin-bottom"]||b[0],a["margin-left"]=a["margin-left"]||b[0];break;case 2:a["margin-top"]=a["margin-top"]||b[0],a["margin-right"]=a["margin-right"]||b[1],a["margin-bottom"]=a["margin-bottom"]||b[0],a["margin-left"]=a["margin-left"]||b[1];break;case 3:a["margin-top"]=a["margin-top"]||b[0],a["margin-right"]=a["margin-right"]||b[1],a["margin-bottom"]=a["margin-bottom"]||b[2],a["margin-left"]=a["margin-left"]||b[1];break;case 4:a["margin-top"]=a["margin-top"]||b[0],a["margin-right"]=a["margin-right"]||b[1],a["margin-bottom"]=a["margin-bottom"]||b[2],a["margin-left"]=a["margin-left"]||b[3]}delete a.margin}return a}function j(){function b(a){return a.length>0&&/^[0-9]+$/.test(a)&&(a+="px"),a}if(a.settings.image_advtab){var c=l.toJSON(),d=t.parseStyle(c.style);d=i(d),c.vspace&&(d["margin-top"]=d["margin-bottom"]=b(c.vspace)),c.hspace&&(d["margin-left"]=d["margin-right"]=b(c.hspace)),c.border&&(d["border-width"]=b(c.border)),l.find("#style").value(t.serializeStyle(t.parseStyle(t.serializeStyle(d))))}}function k(){if(a.settings.image_advtab){var b=l.toJSON(),c=t.parseStyle(b.style);l.find("#vspace").value(""),l.find("#hspace").value(""),c=i(c),(c["margin-top"]&&c["margin-bottom"]||c["margin-right"]&&c["margin-left"])&&(c["margin-top"]===c["margin-bottom"]?l.find("#vspace").value(g(c["margin-top"])):l.find("#vspace").value(""),c["margin-right"]===c["margin-left"]?l.find("#hspace").value(g(c["margin-right"])):l.find("#hspace").value("")),c["border-width"]&&l.find("#border").value(g(c["border-width"])),l.find("#style").value(t.serializeStyle(t.parseStyle(t.serializeStyle(c))))}}var l,m,n,o,p,q,r,s={},t=a.dom,u=a.settings.image_dimensions!==!1;m=a.selection.getNode(),n=t.getParent(m,"figure.image"),n&&(m=t.select("img",n)[0]),m&&("IMG"!=m.nodeName||m.getAttribute("data-mce-object")||m.getAttribute("data-mce-placeholder"))&&(m=null),m&&(o=t.getAttrib(m,"width"),p=t.getAttrib(m,"height"),s={src:t.getAttrib(m,"src"),alt:t.getAttrib(m,"alt"),title:t.getAttrib(m,"title"),"class":t.getAttrib(m,"class"),width:o,height:p,caption:!!n}),d&&(q={type:"listbox",label:"Image list",values:c(d,function(b){b.value=a.convertURL(b.value||b.url,"src")},[{text:"None",value:""}]),value:s.src&&a.convertURL(s.src,"src"),onselect:function(a){var b=l.find("#alt");(!b.value()||a.lastControl&&b.value()==a.lastControl.text())&&b.value(a.control.text()),l.find("#src").value(a.control.value()).fire("change")},onPostRender:function(){q=this}}),a.settings.image_class_list&&(r={name:"class",type:"listbox",label:"Class",values:c(a.settings.image_class_list,function(b){b.value&&(b.textStyle=function(){return a.formatter.getCssText({inline:"img",classes:[b.value]})})})});var v=[{name:"src",type:"filepicker",filetype:"image",label:"Source",autofocus:!0,onchange:h},q];a.settings.image_description!==!1&&v.push({name:"alt",type:"textbox",label:"Image description"}),a.settings.image_title&&v.push({name:"title",type:"textbox",label:"Image Title"}),u&&v.push({type:"container",label:"Dimensions",layout:"flex",direction:"row",align:"center",spacing:5,items:[{name:"width",type:"textbox",maxLength:5,size:3,onchange:e,ariaLabel:"Width"},{type:"label",text:"x"},{name:"height",type:"textbox",maxLength:5,size:3,onchange:e,ariaLabel:"Height"},{name:"constrain",type:"checkbox",checked:!0,text:"Constrain proportions"}]}),v.push(r),a.settings.image_caption&&tinymce.Env.ceFalse&&v.push({name:"caption",type:"checkbox",label:"Caption"}),a.settings.image_advtab?(m&&(m.style.marginLeft&&m.style.marginRight&&m.style.marginLeft===m.style.marginRight&&(s.hspace=g(m.style.marginLeft)),m.style.marginTop&&m.style.marginBottom&&m.style.marginTop===m.style.marginBottom&&(s.vspace=g(m.style.marginTop)),m.style.borderWidth&&(s.border=g(m.style.borderWidth)),s.style=a.dom.serializeStyle(a.dom.parseStyle(a.dom.getAttrib(m,"style")))),l=a.windowManager.open({title:"Insert/edit image",data:s,bodyType:"tabpanel",body:[{title:"General",type:"form",items:v},{title:"Advanced",type:"form",pack:"start",items:[{label:"Style",name:"style",type:"textbox",onchange:k},{type:"form",layout:"grid",packV:"start",columns:2,padding:0,alignH:["left","right"],defaults:{type:"textbox",maxWidth:50,onchange:j},items:[{label:"Vertical space",name:"vspace"},{label:"Horizontal space",name:"hspace"},{label:"Border",name:"border"}]}]}],onSubmit:f})):l=a.windowManager.open({title:"Insert/edit image",data:s,body:v,onSubmit:f})}a.on("preInit",function(){function b(a){var b=a.attr("class");return b&&/\bimage\b/.test(b)}function c(a){return function(c){function d(b){b.attr("contenteditable",a?"true":null)}for(var e,f=c.length;f--;)e=c[f],b(e)&&(e.attr("contenteditable",a?"false":null),tinymce.each(e.getAll("figcaption"),d))}}a.parser.addNodeFilter("figure",c(!0)),a.serializer.addNodeFilter("figure",c(!1))}),a.addButton("image",{icon:"image",tooltip:"Insert/edit image",onclick:d(e),stateSelector:"img:not([data-mce-object],[data-mce-placeholder]),figure.image"}),a.addMenuItem("image",{icon:"image",text:"Insert/edit image",onclick:d(e),context:"insert",prependToContext:!0}),a.addCommand("mceImage",d(e))});