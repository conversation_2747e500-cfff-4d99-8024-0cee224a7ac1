﻿
var map = null;

// var mobile_coords = null; 

var map_center_coords = [15.2799307, 44.2107259]; // default
              


function initMap() {
    map = new google.maps.Map(document.getElementById('map'), {
        center: new google.maps.LatLng(map_center_coords[0], map_center_coords[1]),
        zoom: 15,
        gestureHandling: "cooperative"
        //, mapTypeId: 'terrain'

    });
}

// ------------------------------           

function drawMarkers(mobile_coords) {

    for (i = 0; i < mobile_coords.length; i++) {

        var latLng = new google.maps.LatLng(mobile_coords[i][0], mobile_coords[i][1]);

        var markTitle = mobile_coords[i][2]; 
        var markUrl = mobile_coords[i][3];

        var marker = new google.maps.Marker({
            position: latLng,
            map: map,
            title: markTitle
        });
        
        if (!hs.isEmpty(markUrl)) {
            marker.url = markUrl;
            marker.addListener("click", function () {
                ShowDlg(this.url);
            })
        }

    };
}

// ---------------------------

function drawPath(pathCoords) {
    var flightPath = new google.maps.Polyline({
        path: pathCoords,
        geodesic: true,
        strokeColor: '#FF0000',
        strokeOpacity: 1.0,
        strokeWeight: 2
    });

    flightPath.setMap(map);
};




function panMapTo(lat, lng) {
    if (map == null)
        return;

    map.panTo(new google.maps.LatLng(lat, lng));

}


function ToRadians(degrees) {
    return degrees * Math.PI / 180;
}

// ---------------------------

// level, (1 = no fraction; 10; 100;1000;...)
function roundDecimal(num, level) {
    return Math.round(num * level) / level;
}

function calcDistance(lat1, lon1, lat2, lon2) {
    try
    {
        var R = 6371; // km, earth radius
        var dLat = ToRadians(lat2 - lat1);
        var dLon = ToRadians(lon2 - lon1);
        var lat1 = ToRadians(lat1);
        var lat2 = ToRadians(lat2);

        var a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.sin(dLon / 2) * Math.sin(dLon / 2) * Math.cos(lat1) * Math.cos(lat2);
        var c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        var d = R * c;

        return roundDecimal(d, 1000);  // in km
    }
    catch(ex)
    {
        return "Err:" + ex.message;
    }
}

var ePickId = null;

function pickCallback() {

    // map_center_coords = [15.2799307, 44.2107259]; // ymn 

    map_center_coords = [21.606097, 39.154304]; // sanaa

    const myLatlng = { lat: map_center_coords[0], lng: map_center_coords[1] };

       

    let infoWindow = new google.maps.InfoWindow({
        content: "Click the map to get Lat/Lng!",
        position: myLatlng,
    });

    infoWindow.open(map);
    // Configure the click listener.
    map.addListener("click", (ev) => {
        // Close the current InfoWindow.
        infoWindow.close();
        // Create a new InfoWindow.
        infoWindow = new google.maps.InfoWindow({
            position: ev.latLng,
        });

        var loc = ev.latLng.lat() + ", " + ev.latLng.lng();

        $("#" + ePickId, parent.document.body).val(loc);

        infoWindow.setContent(
            loc + "<hr/>" + hs.link("OK", "javascript:pickClose()", null)     
            //JSON.stringify(ev.latLng.toJSON(), null, 2)
        );
        infoWindow.open(map);
    });
}




function pickClose() {
    $("#" + ePickId, parent.document.body).trigger("change");
    CloseDlg();
}




