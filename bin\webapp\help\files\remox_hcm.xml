﻿<?xml version="1.0" encoding="utf-8"?>
<SystemHelp xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <items>

    <Entry>
      <id>hcm-init</id>
      <title>تهيئة نظام الموارد البشرية</title>
      <content>
        <![CDATA[

<div class='inline-menu'><div style='padding: 10px 20px;'>
<div class='hint '>لبدء العمل على النظام يفضل تعريف المكونات التالية في النظام، وذلك حسب نوعية نشاط  أو حاجة المنشأة.</div>

<a class='ui-bold ' href='javascript:ShowDlgF("/app/?hs-sys-db&cmd=update")'>فحص و تحديث مكونات قاعدة البيانات</a>
<span class='note'>إذا كان يتم إعداد النظام لأول مرة يجب عمل فحص و تحديث مكونات قاعدة البيانات </span>

<a class='ui-bold ' href='javascript:ShowDlgF("/app/?fi-act&cmd=init")'>تهئية نظام الأستاذ العام</a>
<span class='note'> يجب تهئية نظام الأستاذ العام قبل نظام الموارد البشرية</span>

<a class='ui-bold ' href='javascript:ShowDlgF("/app/fms/?fm=sys-cfg&id=hr-sys")'>إعدادات النظام</a>
<span class='note'>تعريف الإعدادات الأساسية لعمل النظام، مثل تعريف الحسابات المطلوبة للربط مع نظام الإستاذ العام، تعريف الإجازات السنوية، تعريف نسبة الضريبة على الإستحقاقات و غيرها </span>

<a class='ui-bold ' href='javascript:ShowDlgF("/app/fms/?fm=hr-wd-def&cmd=init")'>تعريف الإستحقاقات  والإستقطاعات الأساسية</a>
<span class='note'>تعريف الإستحقاقات  والإستقطاعات الأساسية لعمل النظام</span>

<a class='ui-bold ' href='javascript:ShowDlgF("/app/fms/?fm=hr-wd-def&cmd=list")'>تعريف الإستحقاقات/الإستقطاعات الثابتة</a>
<span class='note'>تعريف الإستحقاقات/الإستقطاعات بمبلغ ثابت أو نسبة محددة من الراتب الأساسي - و يمكن تعيينها للموظف بنفس المبلغ أو النسبة </span>

<a class='ui-bold ' href='javascript:ShowDlgF("/app/fms/?fm=hr-wd-def&cmd=list&fm-mode=emp-var")'>الإستحقاقات/الإستقطاعات المتغيرة حسب الموظف</a>
<span class='note'>تعريف الإستحقاقات/الإستقطاعات التي يمكن إعدادها بشكل مختلف لكل موظف</span>


<a class='ui-bold ' href='javascript:ShowDlgF("")'> </a>
<span class='note'></span>

</div></div>


]]>
      </content>
     
    </Entry>



	  <Entry>
		  <id>ts-close</id>
		  <title>خطوات معالجة وإغلاق الحافظة</title>
		  <content>
			  <![CDATA[

<div class='inline-menu'><div style='padding: 10px 20px;'>
<div class='hint '>الخطوات التالية يمكن أن يتم تنفيذها في أي وقت خلال الشهر.</div>

<a class='ui-bold ' target='F' href='/app/fms/?fm=hr-ta-mach&cmd=list'>سحب سجلات الحافظة من أجهزة مراقبة الدوام</a>
<span class='note'>يجب التأكد من أنه تم سحب كل السجلات من الأجهزة المعرفة في النظام .يمكن السحب من عدة أجهزة بإستخدام الإجراءات الجماعية</span>

<a class='ui-bold' target='F' href='/app/fms/?fm=hr-ta-proc&fm-mode=chk'>فحص سجلات الحافظة</a>
<span class='note'>
سيقوم النظام بتحديد نوع السجل (حضور/إنصراف) وكذلك الفترة و مقدار التأخير أو التبكير ونوع المخالفة والجزاء  - إن وجد. 
و سيتم تحديد السجلات الغير صالحة كونها مكررة أو أخذت خارج الوقت المسموح. وسوف يتم السماح بتعديل أو حذف السجلات - في إن إعدادات النظام تسمح بذلك
</span>

<a class='ui-bold ' href='/app/fms/?fm=hr-ta-log&cmd=find' target='F'>إستعراض سجلات الحافظة مع نتيجة الفحص</a>
<span class='note'>يمكن إستعراض سجلات الحافظة بما في ذلك نتيجة الفحص والمخالفة التي سجلها النظام</span>

<a class='ui-bold ' target='F' href='/app/fms/?fm=hr-ts-work&fm-mode=mi'>تسجيل المهام الخارجية</a>
<span class='note'>يمكن تسجيل المهام الخارجية أي وقت قبل معالجة الحافظة</span>

<a class='ui-bold ' target='F' href='/app/fms/?fm=hr-ts-leave'>تسجيل الإجازات</a>
<span class='note'>يمكن تسجيل الإجازات في أي وقت قبل معالجة الحافظة</span>

<a class='ui-bold' target='F' href='/app/fms/?fm=hr-ts-perm'>تسجيل أذونات التأخير والإنصراف المبكر</a>
<span class='note'>يجب تسجيل طلبات الإذن ليوم معين قبل معالجة الحافظة لذلك اليوم</span>

<div class='hint '>الخطوات التالية يمكن أن يتم تنفيذها في أي وقت خلال الشهر بشرط إن يكون قد تم تنفيذ الخطوات السابقة كلها.</div>

<a class='ui-bold' target='F' href='/app/fms/?fm=hr-ta-proc&fm-mode=proc'>معالجة سجلات الحافظة للأيام السابقة</a>
<span class='note'>
قبل تنفيذ المعالجة النهائية يجب التأكد من سحب كل السجلات من أجهزة البصمة، وكذلك يجب التأكد من إدخال الإجازات والأذونات والمهمات الخارجية للموظفين خلال الفترة لتجنب تسجيل غياب ومخالفات.
بناءا على مرحلة فحص السجلات - سيقوم النظام بإنشاء مستندات الحضور والإنصراف والمخالفات. ولن يسمح بتعديل هذه السجلات بعد المعالجة</span>

<a class='ui-bold' target='F' href='/app/fms/?fm=hr-ts-dets&fm-mode=viols&cmd=find'>إستعراض المخالفات و تطبيق الإعفاءات</a>
<span class='note'>
في هذه الشاشة يمكن استعراض مخالفات الحضور والإنصراف التي قيدها النظام نتيجة معالجة سجلات الحافظة، و يمكن إيضا تطبيق الإعفاءات التي تقرها الإدارة
</span>

<div class='hint '>الخطوات التالية يجب أن يتم تنفيذها في نهاية الشهر قبل إصدار كشف الراتب.</div>

<a class='ui-bold' target='_blank' href='/app/fms/?fm=hr-py-run'>إغلاق الحافظة وتقييد إستحقاقات الرواتب آليا</a>
<span class='note'>بناءا على بيانات الموظف و الإستحقاقات والإستقطاعات ومستندات الحضور والإنصراف والمخالفات،
سوف يقوم النظام بإنشاء كشف الراتب وإيصالات الرواتب و إضافة القيود المحاسبية.
يمكن عمل محاكاة للعملية مع عرض تفاصيل دقيقة
</span>

</div></div>


]]>
		  </content>

	  </Entry>


	  <Entry>
		  <id>hr-shift</id>
		  <title>فترات الدوام</title>
		  <content>
			  <![CDATA[
أوقات الدوام هي التي تحدد الأوقات التي يتم إعتماد البصمة خلالها على مسنوى الأيام. ومن خلالها يتم تحديد نوع المخالفة..
سياسة الدوام هي التي تحدد نوعية الجزاء للمخالفة.

بشكل عام يقوم النظام بتسجيل التأخير في الحضور أو الإنصراف المبكر بالدقيقة و لكن سياسة الدوام هي التي توجه النظام لتسجيل مخالفة أو لا وتحدد أيضا الجزاء حسب المخالفة. 
			  
يمكن تحديد أوقات دوام مختلفه حسب الموظف وحسب الأيام ولفترة أو فترتين. 

إذا تم ترك اليوم فارغا في فترة الدوام فهذا يعني إن هذا يطبق على كل الأيام التي لم تحدد بفترة الدوام، مثلا إذا كان الخميس يختلف بالدوام، 
يمكن فقط تحديد دوام الخميس، و ثم تحديد دوام آخر لبقية الأيام، حيث يتم ترك اليوم فارغا في هذه الحالة
			  
بداية الحضور:
بداية الوقت الذي يتم إعتماد بصمة الحضور فيه، لن يتم إعتماد بصمة الحضور قبل هذا الوقت، و بالتالي سوف يتم تقييد مخالفة (حضور بدون بصمة أو غياب)، يفضل أن يترك وقت كافي (ساعة أو ساعتين مثلا) قبل الدوام الفعلي كي يتم إعتماد بصمات الموظفين المبكرين جدا.

نهاية الحضور:
لن يتم إعتماد بصمة الحضور التي بعد هذا الوقت، و بالتالي سوف يتم تقييد مخالفة (حضور بدون بصمة أو غياب).  يفضل أن يتم ترك وقت كافي للحضور (مثلا ساعتين) بعد بداية الدوام، ويمكن تحديد مخالفات وجزاءات التأخير من سياسة الدوام


بداية الإنصراف:
لن يتم إعتماد بصمة الإنصراف التي قبل هذا الوقت، و بالتالي سوف يتم تقييد مخالفة (إنصراف بدون بصمة أو غياب).  يمكن أن يتم ترك بعض الوقت للإنصراف المبكر (مثلا نصف ساعة) قبل نهاية الدوام، ويمكن تحديد مخالفات وجزاءات الإنصراف المبكر من سياسة الدوام

نهاية الإنصراف:
لن يتم إعتماد بصمة الإنصراف التي بعد هذا الوقت، وبالتالي سوف يتم تقييد مخالفة (إنصراف بدون بصمة أو غياب). يفضل أن يتم ترك وقت كافي (مثلا ساعة) للموظفين للتأخر بعد الدوام




]]>
		  </content>
	  </Entry>


	  <Entry>
		  <id>s1_late_attend_tolm</id>
		  <title>التأخير المسموح للفترة الأولى</title>
		  <content>
			  <![CDATA[
الزمن بالدقيقة الذي يسمح للموظف بالتأخير بدون تسجيل أي مخالفة. و لكن أي تأخير ولو دقيقة واحدة بعد هذا الوقت سوف يحسب النظام وقت التأخير كاملا.
مثلا إذا كانت بداية الدوام الساعة 8:00 صباحا و كان وقت التأخير المسموح هو 20 دقيقة، 
فلو حضر الموظف الساعة 8:20 سوف يتم تسجيل التأخير 20 دقيقة ولكن لن يتم تسجيل مخالفة ولن يتم تطبيق أي جزاء أو خصم على الموظف
ولكن لو حضر الموظف الساعة 8:21 سوف يتم تسجيل مخالفة تأخير 21 دقيقة وسوف يتم تطبيق الجزاء المحدد

يمكن إدخال القيمة 0 لتطبيق سياسة حضور صارمة، و سيقوم النظام بتسجيل مخالفة تأخير ولو لدقيقة واحدة.

]]>
		  </content>
	  </Entry>

    <!--   
   <Entry>
     <id></id>
     <title></title>
     <content>
       <![CDATA[

]]>
     </content>
   </Entry>
-->
  </items>
</SystemHelp>