﻿<?xml version="1.0" encoding="utf-8"?>
<SystemHelp xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <items>

    <Entry>
      <id>home</id>
      <title>النظام - عام</title>
      <linked>fm-comps;hs-cms;date-fld</linked>

      
    </Entry>


    <Entry>
      <id>fm-comps</id>
      <title>مكونات شاشة بيانات السجل</title>
      <content>
        <![CDATA[
        <center>        
        <img src='/help/ar/fm_item_view.png' />
        </center>
       

]]>
      </content>
    </Entry>

    

    <Entry>
      <id>crtd_by</id>
      <title>المستخدم الذي قام بإضافة السجل</title>
      <content>
        <![CDATA[
        
        اسم المستخدم الذي قام بإضافة أو إنشاء السجل  في النظام، 
        ولمعرفة المستخدمين الذين قاموا بالتعديل على بيانات السجل/أو تنفيذ إجراءات معينة على السجل مثل التوقيف أو الترحيل وغيرها - يمكن الإطلاع على سجل التغييرات المرتبط بهذا السجل، و يمكن معرفة ذلك من قائمة الأدوات الخاصة بالسجل 
       

]]>
      </content>
    </Entry>


    <Entry>
      <id>crtd_date</id>
      <title>تاريخ إضافة السجل</title>
      <content>
        <![CDATA[
       
        تاريخ إضافة أو إنشاء السجل  في النظام 
        
        وهذا قد يختلف عن تاريخ المستند

]]>
      </content>
    </Entry>
    
        <Entry>
      <id>crtd_time</id>
      <title>وقت إضافة السجل</title>
      <content>
        <![CDATA[
      
        الوقت بالساعة والدقيقة والثانية، الذي يحدد زمن إضافة أو إنشاء السجل  في النظام، 
        
        

]]>
      </content>
    </Entry>


    <Entry>
      <id>date-fld</id>
      <title>حقل التاريخ</title>
      <content>
        <![CDATA[
يمكن إدخال التاريخ يدويا أو إختياره من التقويم        

يمكن عرض التقويم باحدى الطرق
الضعط على مفتاح F3 
النقر المزدوج على حقل التاريخ
استخدام الإيقونة التي بجانب الحقل

        
عند إدخال التاريخ يدويا يمكن استخدام حروف متعددة للفصل بين اليوم والشهر و السنة مثل
30.1.2019 
30-1-2019
30/1/2019
30 1 2019

وسيقوم النظام بإستبدال هذه الأحرف بالصيغة المحددة في إعدادات النظام، أي أن التواريخ السابقة سوف تحول للصيغة 30/1/2019 

وللتسهيل ايضا يمكن ادخال اليوم وضغط مفتاح Enter وسيقوم النظام بإضافة الفاصلة آليا، و هكذا بالنسبة للشهر والسنة
ملاحظة: هذه المفاتيح تعطل عند إظهار التقويم الخاص بالحقل
       
صيغة حقل التاريخ يحددها مدير النظام من إعدادات النظام
]]>
      </content>
    </Entry>

    

    <Entry>
      <id>photo-fld</id>
      <title>حقل الصورة</title>
      <content>
        <![CDATA[
        يمكن تحديد رابط الصورة  وذلك لتحميل الصورة  من اي موقع أو من جهاز آخر في نفس الشبكة. يتم اللجوء لهذه الخطوة لتجنب تحميل السرفر بملفات الصورة و خاصة عندما تكون الملفات ذات أحجام كبيرة. و لا يوجد أي تحديد لحجم الصورة في هذه الحالة.
        
        يمكن رفع ملف الصورة من الجهاز المحلي (يمكن ذلك في وضع التعديل – حيث سيظهر رابط إضافة صورة ).و يمكن تحديد الحد الأعلى لحجم الصورة من خلال إعدادات النظام.



       ]]>
      </content>
    </Entry>

    <Entry>
      <id>hs-cms</id>
      <title>إدارة المحتوى</title>
      <content>
        <![CDATA[
يجب أن يكون
رمز  محتوى الصفحة الرئيسية home
رمز محتوى قالب الصفحات الداخلية template

للإشارة إلى بيانات المحتوى في القالب يتم استخدام المتغيرات التالية داخل التصميم الخاص بالقالب

<span class='lft'>[#cont-title]</span> عنوان الصفحة/المحتوى
<span class='lft'>[#cont-date]</span> تاريخ إنشاء الصفحة/المحتوى
<span class='lft'>[#cont-data]</span> النص الكامل للصفحة/المحتوى



       ]]>
      </content>
    </Entry>

    <Entry>
      <id>ex_list_use</id>
      <title>استخدام قائمة الإختيار المتقدمة</title>
      <content>
        <![CDATA[
مميزات القائمة المتقدمة:
- توفر القائمة المتقدمة آلية فعالة وسهلة للبحث عن البيانات التي تحتويها القائمة بإستخدام جزء من اسم العنصر أو رقمه أو كليهما، 
- يتم عمل البحث بالكتابة في نفس الحقل، بدون الحاجة لفتح نافذة جديدة.


لإظهار القائمة يمكن ضغط مفتاح السهم للأسفل، أو النقر بالماوس على حقل القائمة.. وعند ظهور القائمة يمكن استخدام المفاتيح التالية:
- مفتاح السهم للأعلى والأسفل:  للتنقل بين العناصر في القائمة وتغيير العنصر المحدد (العنصر المؤشر باللون الأصفر).
- مفتاح الإدخال Enter : إختيار العنصر المحدد و إغلاق القائمة.
- مفتاح Esc :  لإغلاق قائمة العناصر المفتوحة، وعدم تغيير قيمة الحقل.  
- مفتاح الحذف Delete :  سيقوم بإلغاء الإختيار الحالي وجعل قيمة الحقل فارغة.


يمكن البحث عن العناصر في القائمة :

- بكتابه الأحرف الأولى من أسم أو رقم العنصر، حيث سيتم تأشير العنصر الأول تلقائيا في حال وجودة مطابقة، و يمكن استخدام الأسهم لتغيير الإختيار.

- بكتابة جزء من إسم أو رقم العنصر ويمكن إستخدام الأسهم لإختياره العنصر المطلوب

- يمكن كتابة عدة أجزاء من الإسم مفصولة بفراغ ولا يشترط الترتيب مثلا عند كتابة: مح مص
تظهر النتائج 
محمد مصلح 
مصطفى محمود
المحمودي لإدارة المصادر

- عند عملية البحث سيقوم النظام بعرض كل العناصر التي تبدأ بـالرقم أو الإسم المدخل أولا ثم عرض العناصر التي تحوي جزء من الرقم أو الإسم.

- لا داعي لكتابة حروف * أو % 

]]>
      </content>
    </Entry>

    <Entry>
      <id>js-err</id>
      <title>خطأ أثناء تحميل أو عرض الصفحة</title>
      <content>
        <![CDATA[

الخطأ ناتج عن الشفرات البرمجية التي تعمل ضمن بيئة المتصفح، قد تكون ملفات الشفرات المحفوظة قديمة لذا حاول تحديث الصفحة، وقد يكون إصدار المتصفح قديم أو أن النظام غير متوافق مع هذا الإصدار

- يمكن استخدام المفاتيح ctrl+F5 لتحديث الملفات المحفوظة في كاش المتصفح

- يمكن الحصول على معلومات أكثر حول الخطأ من خلال أدوات المطور Developer Tools المرفقة بالمتصفح

ويمكن عرض أدوات المطور من خلال قائمة المتصفح أو بواسطة المفاتيح

Firefox: Ctrl + Shift + K
Chrome: Ctrl + Shift + J
Internet Explorer: F12
Opera: Ctrl + Shift + I

- في حال استمرار ظهور رسالة الخطأ يمكن إرسال صورة للخطأ ونسخة من سجل أدوات المطور إلى الدعم الفني

]]>
      </content>
    </Entry>


    <Entry>
      <id>sys-trash</id>
      <title>شاشة السجلات المحذوفة</title>
      <content>
        <![CDATA[
من خلال هذه الشاشة يمكن البحث و إستعراض السجلات التي تم حذفها من قبل المستخدمين

للبحث عن سجلات شاشة معينة: يفضل الذهاب إل

ملاحظة:
 هذه النسخة لا تدعم ميزة إسترجاع البيانات المحذوفة
]]>
      </content>
    </Entry>


    <Entry>
      <id>db-update</id>
      <title>تحديث قاعدة البيانات</title>
      <content>
        <![CDATA[
أداة تحديث قاعدة البيانات  تعتبر من أهم الأدوات التي يستخدمها مدير النظام لحل المشاكل المتعلقة بقاعة البيانات.

 تقوم هذه الأداة بإضافة الجداول والحقول المفقودة (غالبا يتم تنفيذ هذه الأداة بعد عمل تحديث للنظام) و قد يتطلب الأمر أن يتم تنفيذ التحديث أكثر من مرة لإستكمال العملية.
 
 في حال ظهور أخطاء أثناء عملية التحديث فإن النظام سوف يواصل عملية التحديث.. و سيقوم بعرض قائمة المكونات التي فشلت.
 
 بإمكان المستخدم تنفيذ التحديث مرة أخرى و في حال أن نفس الأخطاء ظهرت يفضل أن يقوم بعمل إعادة تشغيل النظام و المحاولة مرة أخرى
 
]]>
      </content>
    </Entry>
    
       <Entry>
     <id>clt-adv-mode</id>
     <title>الوضع المتقدم - الإدارة والإعدادات</title>
     <content>
       <![CDATA[
يمكن تفعيل هذا الوضع من بيانات نظام العميل عند الحاجة فقط و يتم تعطيله بعد إنتهاء الحاجة

سوف يسمح النظام بتعديل الإعدادات المتقدمة لذا يجب الإنتباه و يجب أن يتم تعديل هذه الإعدادات فقط بعد إستشارة الدعم الفني للنظام. 
]]>
     </content>
   </Entry>
    
    
 


    <Entry>
      <id>gb_0</id>
      <title>حقول الإحصاء - حقول سطور التقرير</title>
      <content>
        <![CDATA[
       
تحديد الحقول التي سوف تظهر في سطور التقرير. يمكن إختيار عدة حقول و ستظهر بنفس الترتيب الذي تم إختيارها به.


]]>
      </content>
      <linked>pivot_fld</linked>
    </Entry>


    <Entry>
      <id>pivot_fld</id>
      <title>حقل التحليل الإحصائي</title>
      <content>
        <![CDATA[
        و يطلق عليه أيضا: حقل تحليل (تلخيص) قيم التقرير الإحصائي أفقيا  أو حقل أعمدة التقرير
        
        .عند تحديد هذا الحقل سيقوم النظام بإنشاء جدول محوري مبني على التقرير الإحصائي الذي تم إنشاؤه بناءا على حقول خانة (إحصاء حسب) حيث سيقوم النظام بتلخيص القيم الإحصائية الإجمالية حسب هذا الحقل
       
 ستظهر قيم حقل التحليل كعناوين لأعمدة التقرير التحليلي. ويمكن فقط إختيار حقل واحد، وهو الحقل الذي سيتمحور حوله التقرير الإحصائي. 
 
 مثلا تقرير المبيعات اليومية  هو تقرير إحصائي  يحوي عمود التاريخ و عمود إجمالي مبلغ المبيعات اليومية
 
 إذا أردنا أن يتم تفصيل مبالغ المبيعات اليومية حسب الصناديق، سيكون علينا تحليل المبيعات اليومية حسب الصناديق. أي يجب إختيار حقل الصندوق في خانة (تحليل حسب). 
 حيث ستظهر الصناديق الموجودة كأعمدة في التقرير.. أي أن التقرير التحليلي سيحوي عمود التاريخ و من ثم سيكون هناك عمود لكل صندوق و هو يمثل مبيعات الصندوق في التاريخ المقابل..
 
 
]]>
      </content>
      <linked>gb_0</linked>
    </Entry>

    <!--   
   <Entry>
     <id></id>
     <title></title>
     <content>
       <![CDATA[

]]>
     </content>
   </Entry>
-->
  </items>
</SystemHelp>

