﻿
// [lat, lng]

var map = null;

// var mobile_coords = null; 

var map_center_coords = [15.3364338, 44.1840764]; // default
              


function initMap() {

    map_center_coords = map_center_coords.reverse();
   
    mapboxgl.accessToken = "pk.eyJ1IjoiaG93YmFuaSIsImEiOiJjbHJwODh1NDgwMmFzMnRsZjdqbzBxem9nIn0.gJ66Pl0A62ml88PDJsYdtA";
     map = new mapboxgl.Map({
        container: 'map',
        // Choose from Mapbox's core styles, or make your own style with Mapbox Studio
       // style: 'mapbox://styles/mapbox/satellite-streets-v11',
         center: map_center_coords,
        zoom: 8
    });
}

// ------------------------------           

function drawMarkers(mobile_coords) {
  //  return;

    for (i = 0; i < mobile_coords.length; i++) {

       // 15.279409, 44.211094

        var markTitle = mobile_coords[i][2]; 
        var markUrl = mobile_coords[i][3];
        var lnglat = [mobile_coords[i][1], mobile_coords[i][0]];

        var markerPopup = "<div class='c' style='padding:1rem;'>" + markTitle + "<hr/><a tabindex='-1'  style='border: none' href='javascript:ShowDlg(\"" + markUrl + "\")'>View</a></div>"

        const marker = new mapboxgl.Marker()
            .setLngLat(lnglat)
            .setPopup(new mapboxgl.Popup().setHTML(markerPopup))
            .addTo(map);

       
        
        if (!hs.isEmpty(markUrl)) {
            marker.url = markUrl;
            marker.on("click", function () {
                ShowDlg(this.url);
            })
        }

    };
}

// ---------------------------

function drawPath(pathCoords) {
    return;

    var flightPath = new google.maps.Polyline({
        path: pathCoords,
        geodesic: true,
        strokeColor: '#FF0000',
        strokeOpacity: 1.0,
        strokeWeight: 2
    });

    flightPath.setMap(map);
};




function panMapTo(lat, lng) {
    return;

    if (map == null)
        return;

    map.panTo(new google.maps.LatLng(lat, lng));

}


function ToRadians(degrees) {
    return degrees * Math.PI / 180;
}

// ---------------------------

// level, (1 = no fraction; 10; 100;1000;...)
function roundDecimal(num, level) {
    return Math.round(num * level) / level;
}

function calcDistance(lat1, lon1, lat2, lon2) {
    try
    {
        var R = 6371; // km, earth radius
        var dLat = ToRadians(lat2 - lat1);
        var dLon = ToRadians(lon2 - lon1);
        var lat1 = ToRadians(lat1);
        var lat2 = ToRadians(lat2);

        var a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.sin(dLon / 2) * Math.sin(dLon / 2) * Math.cos(lat1) * Math.cos(lat2);
        var c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        var d = R * c;

        return roundDecimal(d, 1000);  // in km
    }
    catch(ex)
    {
        return "Err:" + ex.message;
    }
}

var ePickId = null;

function pickCallback() {
   

    // map_center_coords = [15.2799307, 44.2107259]; // ymn 

 

    

       


    // Configure the click listener.
    map.on("click", (ev) => {
     
      

        var loc = ev.lngLat.lat + ", " + ev.lngLat.lng;

        $("#" + ePickId, parent.document.body).val(loc);

        pickClose();

        
    });
}




function pickClose() {
    $("#" + ePickId, parent.document.body).trigger("change");
    CloseDlg();
}




