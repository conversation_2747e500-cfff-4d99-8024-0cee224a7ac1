﻿/* 
HowbaniSoft Payment Gateway: Online Channels (Net + Counter)
2016-2017
*/

/*
var sabafon_units_arr = {
    '136.89': '11',
    '372.80': '32',
    '570.85': '49',
    '966.95': '83',
    '1,456.25': '125',
    '2,434.85': '209',
    '3,495.00': '300',
    '5,883.25': '505',
    '14,795.50': '1270'
};

var sabafon_units_counter_arr = {
    '136.89': '11 وحدة بمبلغ 150 ريال',
    '372.80': '32 وحدة بمبلغ 400 ريال',
    '570.85': '49 وحدة بمبلغ 600 ريال',
    '966.95': '83 وحدة بمبلغ 1000 ريال',
    '1,456.25': '125 وحدة بمبلغ 1500 ريال',
    '2,434.85': '209 وحدة بمبلغ 2450 ريال',
    '3,495.00': '300 وحدة بمبلغ 3550 ريال',
    '5,883.25': '505 وحدة بمبلغ 5900 ريال',
    '14,795.50': '1270 وحدة بمبلغ 14850 ريال'
};
*/

var sabafon_units_arr = null;
var sabafon_units_counter_arr = null;


function on_pg_service_changed(e_svc_id, isCounter) {
    var cur_val = $('#' + e_svc_id).val();

    $('#amnt').val('');

    show_elements('r_amnt;r_units;r_main_id', false);
    if (cur_val == null || cur_val.length == 0) return;

    // MTN units

    if (cur_val == 'mtn01' || cur_val == 'mtn02') {
        show_element('r_units', true);
        if (isCounter)
            fill_dropdown_list('units',
                {
                    '155': '200',
                    '240': '300',
                    '410': '500',
                    '575': '700',
                    '830': '1000',
                    '1250': '1500',
                    '2500': '2950',
                    '5000': '5850'
                });
        else
            fill_dropdown_list('units',
                {
                    '155': '155',
                    '240': '240',
                    '410': '410',
                    '575': '575',
                    '830': '830',
                    '1250': '1250',
                    '2500': '2500',
                    '5000': '5000'
                });

        $('#units').prepend($('<option />').val('').html(' ---- اختر المبلغ ----- '));
        $('#units').val('');
        return;
    };

    // Sabafon units

    if (cur_val == 'sf02') {

        show_element('r_units', true);

        if (isCounter) {
            fill_dropdown_list('units', sabafon_units_counter_arr);
            /*    {
                    '136.89': '11 وحدة بمبلغ 150 ريال',
                    '372.80': '32 وحدة بمبلغ 400 ريال',
                    '570.85': '49 وحدة بمبلغ 600 ريال',
                    '966.95': '83 وحدة بمبلغ 1000 ريال',
                    '1456.25': '125 وحدة بمبلغ 1500 ريال',
                    '2434.85': '209 وحدة بمبلغ 2450 ريال',
                    '3495.00': '300 وحدة بمبلغ 3550 ريال',
                    '5883.25': '505 وحدة بمبلغ 5900 ريال',
                    '14795.5': '1270 وحدة بمبلغ 14850 ريال'
                }); */
        }
        else {
            fill_dropdown_list('units', sabafon_units_arr);
                /*{
                    '30.05': 'bad', // bad - test only

                    '136.89': '11', 
                    '372.80': '32',
                    '570.85': '49',
                    '966.95': '83',
                    '1456.25': '125',
                    '2434.85': '209',
                    '3495.00': '300',
                    '5883.25': '505'
                });*/
        }

        $('#units').prepend($('<option />').val('').html(' ---- اختر عدد الوحدات ----- '));
        $('#units').val('');
        return;
    };

    show_element('r_amnt', true);
    $('#amnt').val('');
    $('#units').val('');

    // -----------

    

    if (cur_val == 'yp003') // elect
    {
        fill_dropdown_list('main_id',
            {
                '11': 'الامانة_المنطقة_الاولى',
                '20': 'الأمانة_المنطقة_الثانية',
                '13': 'الأ مانة_المنطقة_الثالثة',
                '14': 'الأ مانة_المنطقة_الرابعة',
                '30': 'منطقة تعز',
                '12': 'منطقة ذمار',
                '16': 'منطقة وادي حضرموت',
                '17': 'منطقة ساحل حضرموت',
                '18': 'منطقة ابين',
                '19': 'منطقة لودر',
                '21': 'منطقة يريم',
                '22': 'منطقة شبوة',
                '24': 'منطقة المهرة',
                '26': 'منطقة اب',
                '35': 'منطقة الضالع',
                '36': 'منطقة الحديدة',
                '40': 'منطقة حجة',
                '46': 'منطقة صعدة',
                '50': 'منطقة مأرب',
                '54': 'منطقة رداع',
                '56': 'منطقة المحويت',
                '58': 'منطقة البيضاء',
                '60': 'منطقة محافظة صنعاء الثالثة',
                '61': 'منطقة عمران',
                '62': 'منطقة الجوف',
                '63': 'منطقة محافظة صنعاء الا ولى',
                '64': 'منطقة محافظة صنعاء الثانية',
                '15': 'عدن المنطقة الا ولى',
                '23': 'عدن المنطقة الثانية',
                '25': 'عدن المنطقة الثالثة',
                '27': 'منطقة لحج',
                '33': 'محافظة ريمة',
                '1': 'كهرباء المحطة',
                '4': 'كهرباء الزاوية',
                '3': 'كهرباء السهيلة',
                '2': 'كهرباء الزيلعي',
                '34': 'منطقة كهرباء الحداء'
            });

        show_element('r_main_id', true);
        $('#main_id').prepend($('<option />').val('').html(' ---- اختر المنطقة ----- '));
        $('#main_id').val('');
       
        return;
    };



    if (cur_val == 'yp004') // water
    {
        fill_dropdown_list('main_id',
            {
                '5': 'مؤسسة المياه (الأمانة)',
                '6': 'محافظة تعز',
                '7': 'محافظة الحديدة',
                '31': 'محافظة عدن',
                '35': 'منطقة مياه المكلا',
                '45': 'منطقة مياه الشحر',
                '55': 'منطقة مياه اغيل باوزير',
                '65': 'منطقة مياه الديس الشرقية',
                '68': 'منطقة مياه سقطرة',
                '69': 'منطقة مياه السريدة وقصيعر',
                '70': 'منطقة مياه ميفع',
                '28': 'مؤسسة المياه (عمران )',
                '14': 'محافظة الحديدة (الصليف )',
                '22': 'محافظة الحديدة(المراوعة)',
                '64': 'مياه مدينة التربه',
                '53': 'مؤسسة المياه (القطن -سيئون)',
                '8': 'مؤسسة المياه (محافظة اب)',
                '38': 'مؤسسة المياه (عتق -شبوة )',
                '43': 'مؤسسة المياه (شبام -سيئون )',
                '9': 'مؤسسة المياه (حجة)',
                '41': 'مؤسسة المياه ( سيئون )',
                '42': 'مؤسسة المياه ( ساه )',
                '66': 'مياه مدينة ذبحان',
                '34': 'مؤسسة المياه ( تريم )',
                '10': 'مياه منطقة ذمار'
            });

        show_element('r_main_id', true);
        $('#main_id').prepend($('<option />').val('').html(' ---- اختر المنطقة ----- '));
        $('#main_id').val('');

        return;
    };


    

};


// ----  //

function retrieve_cust_name() {
    var _url = "/app/fms/?fm=pg-cust&sind=y&cmd=ret-cust-name&cust-id=" + $("#cust_id").val();

    if (getUrlParameter("cmd") != "add")
        return;
    

    $.ajax({
        url: _url,
        success: function (result) {
            eval(result);
           // window.alert(result);
        }
    });
}
