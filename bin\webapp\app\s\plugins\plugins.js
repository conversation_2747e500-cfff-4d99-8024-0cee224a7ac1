// This file contains multi JS plugins

var h_AM = "AM", h_PM = "PM", h_am = "am", h_pm = "pm";
var h_a = "a", h_p = "p";


// jquery.timepicker.min.js
!function (e) { "object" == typeof module && "object" == typeof module.exports ? e(require("jquery"), window, document) : "undefined" != typeof jQuery && e(jQuery, window, document) }(function (e, t, i, n) {
    !function () {
        function t(e, t, i) { return new Array(i + 1 - e.length).join(t) + e } function n() { if (1 === arguments.length) { var t = arguments[0]; return "string" == typeof t && (t = e.fn.timepicker.parseTime(t)), new Date(0, 0, 0, t.getHours(), t.getMinutes(), t.getSeconds()) } return 3 === arguments.length ? new Date(0, 0, 0, arguments[0], arguments[1], arguments[2]) : 2 === arguments.length ? new Date(0, 0, 0, arguments[0], arguments[1], 0) : new Date(0, 0, 0) } e.TimePicker = function () { var t = this; t.container = e(".ui-timepicker-container"), t.ui = t.container.find(".ui-timepicker"), 0 === t.container.length && (t.container = e("<div></div>").addClass("ui-timepicker-container").addClass("ui-timepicker-hidden ui-helper-hidden").appendTo("body").hide(), t.ui = e("<div></div>").addClass("ui-timepicker").addClass("ui-widget ui-widget-content ui-menu").addClass("ui-corner-all").appendTo(t.container), t.viewport = e("<ul></ul>").addClass("ui-timepicker-viewport").appendTo(t.ui), e.fn.jquery >= "1.4.2" && t.ui.delegate("a", "mouseenter.timepicker", function () { t.activate(!1, e(this).parent()) }).delegate("a", "mouseleave.timepicker", function () { t.deactivate(!1) }).delegate("a", "click.timepicker", function (i) { i.preventDefault(), t.select(!1, e(this).parent()) })) }, e.TimePicker.count = 0, e.TimePicker.instance = function () { return e.TimePicker._instance || (e.TimePicker._instance = new e.TimePicker), e.TimePicker._instance }, e.TimePicker.prototype = {
            keyCode: { ALT: 18, BLOQ_MAYUS: 20, CTRL: 17, DOWN: 40, END: 35, ENTER: 13, HOME: 36, LEFT: 37, NUMPAD_ENTER: 108, PAGE_DOWN: 34, PAGE_UP: 33, RIGHT: 39, SHIFT: 16, TAB: 9, UP: 38 }, _items: function (t, i) { var r, a, o = this, s = e("<ul></ul>"), c = null; for (-1 === t.options.timeFormat.indexOf("m") && t.options.interval % 60 !== 0 && (t.options.interval = 60 * Math.max(Math.round(t.options.interval / 60), 1)), r = i ? n(i) : t.options.startTime ? n(t.options.startTime) : n(t.options.startHour, t.options.startMinutes), a = new Date(r.getTime() + 864e5) ; a > r;) o._isValidTime(t, r) && (c = e("<li>").addClass("ui-menu-item").appendTo(s), e("<a>").addClass("ui-corner-all").text(e.fn.timepicker.formatTime(t.options.timeFormat, r)).appendTo(c), c.data("time-value", r)), r = new Date(r.getTime() + 60 * t.options.interval * 1e3); return s.children() }, _isValidTime: function (e, t) { var i = null, r = null; return t = n(t), null !== e.options.minTime ? i = n(e.options.minTime) : null === e.options.minHour && null === e.options.minMinutes || (i = n(e.options.minHour, e.options.minMinutes)), null !== e.options.maxTime ? r = n(e.options.maxTime) : null === e.options.maxHour && null === e.options.maxMinutes || (r = n(e.options.maxHour, e.options.maxMinutes)), null !== i && null !== r ? t >= i && r >= t : null !== i ? t >= i : null !== r ? r >= t : !0 }, _hasScroll: function () { var e = "undefined" != typeof this.ui.prop ? "prop" : "attr"; return this.ui.height() < this.ui[e]("scrollHeight") }, _move: function (e, t, i) { var n = this; if (n.closed() && n.open(e), !n.active) return void n.activate(e, n.viewport.children(i)); var r = n.active[t + "All"](".ui-menu-item").eq(0); r.length ? n.activate(e, r) : n.activate(e, n.viewport.children(i)) }, register: function (t, i) { var n = this, r = {}; r.element = e(t), r.element.data("TimePicker") || (r.options = e.metadata ? e.extend({}, i, r.element.metadata()) : e.extend({}, i), r.widget = n, e.extend(r, { next: function () { return n.next(r) }, previous: function () { return n.previous(r) }, first: function () { return n.first(r) }, last: function () { return n.last(r) }, selected: function () { return n.selected(r) }, open: function () { return n.open(r) }, close: function () { return n.close(r) }, closed: function () { return n.closed(r) }, destroy: function () { return n.destroy(r) }, parse: function (e) { return n.parse(r, e) }, format: function (e, t) { return n.format(r, e, t) }, getTime: function () { return n.getTime(r) }, setTime: function (e, t) { return n.setTime(r, e, t) }, option: function (e, t) { return n.option(r, e, t) } }), n._setDefaultTime(r), n._addInputEventsHandlers(r), r.element.data("TimePicker", r)) }, _setDefaultTime: function (t) { "now" === t.options.defaultTime ? t.setTime(n(new Date)) : t.options.defaultTime && t.options.defaultTime.getFullYear ? t.setTime(n(t.options.defaultTime)) : t.options.defaultTime && t.setTime(e.fn.timepicker.parseTime(t.options.defaultTime)) }, _addInputEventsHandlers: function (t) { var i = this; t.element.bind("keydown.timepicker", function (e) { switch (e.which || e.keyCode) { case i.keyCode.ENTER: case i.keyCode.NUMPAD_ENTER: e.preventDefault(), i.closed() ? t.element.trigger("change.timepicker") : i.select(t, i.active); break; case i.keyCode.UP: t.previous(); break; case i.keyCode.DOWN: t.next(); break; default: i.closed() || t.close(!0) } }).bind("focus.timepicker", function () { t.open() }).bind("blur.timepicker", function () { setTimeout(function () { t.element.data("timepicker-user-clicked-outside") && t.close() }) }).bind("change.timepicker", function () { t.closed() && t.setTime(e.fn.timepicker.parseTime(t.element.val())) }) },
            select: function (t, i) { var n = this, r = t === !1 ? n.instance : t; n.setTime(r, e.fn.timepicker.parseTime(i.children("a").text())), n.close(r, !0) }, activate: function (e, t) { var i = this, n = e === !1 ? i.instance : e; if (n === i.instance) { if (i.deactivate(), i._hasScroll()) { var r = t.offset().top - i.ui.offset().top, a = i.ui.scrollTop(), o = i.ui.height(); 0 > r ? i.ui.scrollTop(a + r) : r >= o && i.ui.scrollTop(a + r - o + t.height()) } i.active = t.eq(0).children("a").addClass("ui-state-hover").attr("id", "ui-active-item").end() } }, deactivate: function () { var e = this; e.active && (e.active.children("a").removeClass("ui-state-hover").removeAttr("id"), e.active = null) }, next: function (e) { return (this.closed() || this.instance === e) && this._move(e, "next", ".ui-menu-item:first"), e.element }, previous: function (e) { return (this.closed() || this.instance === e) && this._move(e, "prev", ".ui-menu-item:last"), e.element }, first: function (e) { return this.instance === e ? this.active && 0 === this.active.prevAll(".ui-menu-item").length : !1 }, last: function (e) { return this.instance === e ? this.active && 0 === this.active.nextAll(".ui-menu-item").length : !1 }, selected: function (e) { return this.instance === e && this.active ? this.active : null }, open: function (t) { var n = this, r = t.getTime(), a = t.options.dynamic && r; if (!t.options.dropdown) return t.element; switch (t.element.data("timepicker-event-namespace", Math.random()), e(i).bind("click.timepicker-" + t.element.data("timepicker-event-namespace"), function (e) { t.element.get(0) === e.target ? t.element.data("timepicker-user-clicked-outside", !1) : t.element.data("timepicker-user-clicked-outside", !0).blur() }), (t.rebuild || !t.items || a) && (t.items = n._items(t, a ? r : null)), (t.rebuild || n.instance !== t || a) && (e.fn.jquery < "1.4.2" ? (n.viewport.children().remove(), n.viewport.append(t.items), n.viewport.find("a").bind("mouseover.timepicker", function () { n.activate(t, e(this).parent()) }).bind("mouseout.timepicker", function () { n.deactivate(t) }).bind("click.timepicker", function (i) { i.preventDefault(), n.select(t, e(this).parent()) })) : (n.viewport.children().detach(), n.viewport.append(t.items))), t.rebuild = !1, n.container.removeClass("ui-helper-hidden ui-timepicker-hidden ui-timepicker-standard ui-timepicker-corners").show(), t.options.theme) { case "standard": n.container.addClass("ui-timepicker-standard"); break; case "standard-rounded-corners": n.container.addClass("ui-timepicker-standard ui-timepicker-corners") } n.container.hasClass("ui-timepicker-no-scrollbar") || t.options.scrollbar || (n.container.addClass("ui-timepicker-no-scrollbar"), n.viewport.css({ paddingRight: 40 })); var o = n.container.outerHeight() - n.container.height(), s = t.options.zindex ? t.options.zindex : t.element.offsetParent().css("z-index"), c = t.element.offset(); n.container.css({ top: c.top + t.element.outerHeight(), left: c.left }), n.container.show(), n.container.css({ left: t.element.offset().left, height: n.ui.outerHeight() + o, width: t.element.outerWidth(), zIndex: s, cursor: "default" }); var u = n.container.width() - (n.ui.outerWidth() - n.ui.width()); return n.ui.css({ width: u }), n.viewport.css({ width: u }), t.items.css({ width: u }), n.instance = t, r ? t.items.each(function () { var i, a = e(this); return i = e.fn.jquery < "1.4.2" ? e.fn.timepicker.parseTime(a.find("a").text()) : a.data("time-value"), i.getTime() === r.getTime() ? (n.activate(t, a), !1) : !0 }) : n.deactivate(t), t.element }, close: function (t) { var n = this; return n.instance === t && (n.container.addClass("ui-helper-hidden ui-timepicker-hidden").hide(), n.ui.scrollTop(0), n.ui.children().removeClass("ui-state-hover")), e(i).unbind("click.timepicker-" + t.element.data("timepicker-event-namespace")), t.element }, closed: function () { return this.ui.is(":hidden") }, destroy: function (e) { var t = this; return t.close(e, !0), e.element.unbind(".timepicker").data("TimePicker", null) }, parse: function (t, i) { return e.fn.timepicker.parseTime(i) }, format: function (t, i, n) { return n = n || t.options.timeFormat, e.fn.timepicker.formatTime(n, i) }, getTime: function (t) { var i = this, n = e.fn.timepicker.parseTime(t.element.val()); return n instanceof Date && !i._isValidTime(t, n) ? null : n instanceof Date && t.selectedTime ? t.format(n) === t.format(t.selectedTime) ? t.selectedTime : n : n instanceof Date ? n : null }, setTime: function (t, i, r) { var a = this, o = t.selectedTime; if ("string" == typeof i && (i = t.parse(i)), i && i.getMinutes && a._isValidTime(t, i)) { if (i = n(i), t.selectedTime = i, t.element.val(t.format(i, t.options.timeFormat)), r) return t } else t.selectedTime = null; return null === o && null === t.selectedTime || (t.element.trigger("time-change", [i]), e.isFunction(t.options.change) && t.options.change.apply(t.element, [i])), t.element }, option: function (t, i, n) { if ("undefined" == typeof n) return t.options[i]; var r, a, o = t.getTime(); "string" == typeof i ? (r = {}, r[i] = n) : r = i, a = ["minHour", "minMinutes", "minTime", "maxHour", "maxMinutes", "maxTime", "startHour", "startMinutes", "startTime", "timeFormat", "interval", "dropdown"], e.each(r, function (i) { t.options[i] = r[i], t.rebuild = t.rebuild || e.inArray(i, a) > -1 }), t.rebuild && t.setTime(o) }
        }, e.TimePicker.defaults = { timeFormat: "hh:mm p", minHour: null, minMinutes: null, minTime: null, maxHour: null, maxMinutes: null, maxTime: null, startHour: null, startMinutes: null, startTime: null, interval: 30, dynamic: !0, theme: "standard", zindex: null, dropdown: !0, scrollbar: !1, change: function () { } }, e.TimePicker.methods = { chainable: ["next", "previous", "open", "close", "destroy", "setTime"] }, e.fn.timepicker = function (t) { if ("string" == typeof t) { var i, n, r = Array.prototype.slice.call(arguments, 1); return i = "option" === t && arguments.length > 2 ? "each" : -1 !== e.inArray(t, e.TimePicker.methods.chainable) ? "each" : "map", n = this[i](function () { var i = e(this), n = i.data("TimePicker"); return "object" == typeof n ? n[t].apply(n, r) : void 0 }), "map" === i && 1 === this.length ? e.makeArray(n).shift() : "map" === i ? e.makeArray(n) : n } if (1 === this.length && this.data("TimePicker")) return this.data("TimePicker"); var a = e.extend({}, e.TimePicker.defaults, t); return this.each(function () { e.TimePicker.instance().register(this, a) }) }, e.fn.timepicker.formatTime = function (e, i) { var n = i.getHours(), r = n % 12, a = i.getMinutes(), o = i.getSeconds(), s = { hh: t((0 === r ? 12 : r).toString(), "0", 2), HH: t(n.toString(), "0", 2), mm: t(a.toString(), "0", 2), ss: t(o.toString(), "0", 2), h: 0 === r ? 12 : r, H: n, m: a, s: o, p: n > 11 ? h_PM : h_AM }, c = e, u = ""; for (u in s) s.hasOwnProperty(u) && (c = c.replace(new RegExp(u, "g"), s[u])); return c = c.replace(new RegExp(h_a, "g"), n > 11 ? h_pm : h_am) },
        e.fn.timepicker.parseTime = function () {
            var t = [[/^(\d+)$/, "$1"], [/^:(\d)$/, "$10"], [/^:(\d+)/, "$1"], [/^(\d):([7-9])$/, "0$10$2"], [/^(\d):(\d\d)$/, "$1$2"], [/^(\d):(\d{1,})$/, "0$1$20"], [/^(\d\d):([7-9])$/, "$10$2"], [/^(\d\d):(\d)$/, "$1$20"], [/^(\d\d):(\d*)$/, "$1$2"], [/^(\d{3,}):(\d)$/, "$10$2"], [/^(\d{3,}):(\d{2,})/, "$1$2"], [/^(\d):(\d):(\d)$/, "0$10$20$3"], [/^(\d{1,2}):(\d):(\d\d)/, "$10$2$3"]], i = t.length; return function (r) {
                var a = n(new Date), o = !1, s = !1, c = !1, u = !1, l = !1; if ("undefined" == typeof r || !r.toLowerCase) return null; r = r.toLowerCase(),
                    o = r.contains(h_a), s = o ? !1 : r.contains(h_p), r = r.replace(/[^0-9:]/g, "").replace(/:+/g, ":");
                for (var m = 0; i > m; m += 1) if (t[m][0].test(r)) { r = r.replace(t[m][0], t[m][1]); break } return r = r.replace(/:/g, ""), 1 === r.length ? c = r : 2 === r.length ? c = r : 3 === r.length || 5 === r.length ? (c = r.substr(0, 1), u = r.substr(1, 2), l = r.substr(3, 2)) : (4 === r.length || r.length > 5) && (c = r.substr(0, 2), u = r.substr(2, 2), l = r.substr(4, 2)), r.length > 0 && r.length < 5 && (r.length < 3 && (u = 0), l = 0), c === !1 || u === !1 || l === !1 ? !1 : (c = parseInt(c, 10), u = parseInt(u, 10), l = parseInt(l, 10), o && 12 === c ? c = 0 : s && 12 > c && (c += 12), c > 24 ? r.length >= 6 ? e.fn.timepicker.parseTime(r.substr(0, 5)) : e.fn.timepicker.parseTime(r + "0" + (o ? "a" : "") + (s ? "p" : "")) : (a.setHours(c, u, l), a))
            }
        }()
    }()
});


/*!
 Copyright 2018 Google Inc. All Rights Reserved.
 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
*/
/*! lifecycle.es5.js v0.1.1 */
!function (e, t) { "object" == typeof exports && "undefined" != typeof module ? module.exports = t() : "function" == typeof define && define.amd ? define(t) : e.lifecycle = t() }(this, function () { "use strict"; var e = void 0; try { new EventTarget, e = !1 } catch (t) { e = !1 } var t = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (e) { return typeof e } : function (e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, n = function (e, t) { if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function") }, i = function () { function e(e, t) { for (var n = 0; n < t.length; n++) { var i = t[n]; i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i) } } return function (t, n, i) { return n && e(t.prototype, n), i && e(t, i), t } }(), r = function (e, t) { if ("function" != typeof t && null !== t) throw new TypeError("Super expression must either be null or a function, not " + typeof t); e.prototype = Object.create(t && t.prototype, { constructor: { value: e, enumerable: !1, writable: !0, configurable: !0 } }), t && (Object.setPrototypeOf ? Object.setPrototypeOf(e, t) : e.__proto__ = t) }, a = function (e, t) { if (!e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return !t || "object" != typeof t && "function" != typeof t ? e : t }, s = function () { function e() { n(this, e), this._registry = {} } return i(e, [{ key: "addEventListener", value: function (e, t) { this._getRegistry(e).push(t) } }, { key: "removeEventListener", value: function (e, t) { var n = this._getRegistry(e), i = n.indexOf(t); i > -1 && n.splice(i, 1) } }, { key: "dispatchEvent", value: function (e) { return e.target = this, Object.freeze(e), this._getRegistry(e.type).forEach(function (t) { return t(e) }), !0 } }, { key: "_getRegistry", value: function (e) { return this._registry[e] = this._registry[e] || [] } }]), e }(), o = e ? EventTarget : s, u = e ? Event : function e(t) { n(this, e), this.type = t }, f = function (e) { function t(e, i) { n(this, t); var r = a(this, (t.__proto__ || Object.getPrototypeOf(t)).call(this, e)); return r.newState = i.newState, r.oldState = i.oldState, r.originalEvent = i.originalEvent, r } return r(t, u), t }(), c = "active", h = "passive", d = "hidden", l = "frozen", p = "terminated", v = "object" === ("undefined" == typeof safari ? "undefined" : t(safari)) && safari.pushNotification, y = ["focus", "blur", "visibilitychange", "freeze", "resume", "pageshow", "onpageshow" in self ? "pagehide" : "unload"], g = function (e) { return e.preventDefault(), e.returnValue = "Are you sure?" }, _ = [[c, h, d, p], [c, h, d, l], [d, h, c], [l, d], [l, c], [l, h]].map(function (e) { return e.reduce(function (e, t, n) { return e[t] = n, e }, {}) }), b = function () { return document.visibilityState === d ? d : document.hasFocus() ? c : h }; return new (function (e) { function t() { n(this, t); var e = a(this, (t.__proto__ || Object.getPrototypeOf(t)).call(this)), i = b(); return e._state = i, e._unsavedChanges = [], e._handleEvents = e._handleEvents.bind(e), y.forEach(function (t) { return addEventListener(t, e._handleEvents, !0) }), v && addEventListener("beforeunload", function (t) { e._safariBeforeUnloadTimeout = setTimeout(function () { t.defaultPrevented || t.returnValue.length > 0 || e._dispatchChangesIfNeeded(t, d) }, 0) }), e } return r(t, o), i(t, [{ key: "addUnsavedChanges", value: function (e) { !this._unsavedChanges.indexOf(e) > -1 && (0 === this._unsavedChanges.length && addEventListener("beforeunload", g), this._unsavedChanges.push(e)) } }, { key: "removeUnsavedChanges", value: function (e) { var t = this._unsavedChanges.indexOf(e); t > -1 && (this._unsavedChanges.splice(t, 1), 0 === this._unsavedChanges.length && removeEventListener("beforeunload", g)) } }, { key: "_dispatchChangesIfNeeded", value: function (e, t) { if (t !== this._state) for (var n = function (e, t) { for (var n, i = 0; n = _[i]; ++i) { var r = n[e], a = n[t]; if (r >= 0 && a >= 0 && a > r) return Object.keys(n).slice(r, a + 1) } return [] }(this._state, t), i = 0; i < n.length - 1; ++i) { var r = n[i], a = n[i + 1]; this._state = a, this.dispatchEvent(new f("statechange", { oldState: r, newState: a, originalEvent: e })) } } }, { key: "_handleEvents", value: function (e) { switch (v && clearTimeout(this._safariBeforeUnloadTimeout), e.type) { case "pageshow": case "resume": this._dispatchChangesIfNeeded(e, b()); break; case "focus": this._dispatchChangesIfNeeded(e, c); break; case "blur": this._state === c && this._dispatchChangesIfNeeded(e, b()); break; case "pagehide": case "unload": this._dispatchChangesIfNeeded(e, e.persisted ? l : p); break; case "visibilitychange": this._state !== l && this._state !== p && this._dispatchChangesIfNeeded(e, b()); break; case "freeze": this._dispatchChangesIfNeeded(e, l) } } }, { key: "state", get: function () { return this._state } }, { key: "pageWasDiscarded", get: function () { return document.wasDiscarded || !1 } }]), t }()) });



    lifecycle.addEventListener('statechange', function(ev) {
        console.log(ev.oldState, "=>", ev.newState);

       // if (ev.newState == "passive")
         //   $("#loading").show();

       // if (event.newState == 'frozen') { document.forms[0].reset(); };
  });




/*
 
 LazyLoad
 MIT License
Copyright (c) 2015 Andrea Verlicchi

*/


!function(n,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(n="undefined"!=typeof globalThis?globalThis:n||self).LazyLoad=t()}(this,(function(){"use strict";function n(){return n=Object.assign||function(n){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(n[i]=e[i])}return n},n.apply(this,arguments)}var t="undefined"!=typeof window,e=t&&!("onscroll"in window)||"undefined"!=typeof navigator&&/(gle|ing|ro)bot|crawl|spider/i.test(navigator.userAgent),i=t&&"IntersectionObserver"in window,o=t&&"classList"in document.createElement("p"),a=t&&window.devicePixelRatio>1,r={elements_selector:".lazy",container:e||t?document:null,threshold:300,thresholds:null,data_src:"src",data_srcset:"srcset",data_sizes:"sizes",data_bg:"bg",data_bg_hidpi:"bg-hidpi",data_bg_multi:"bg-multi",data_bg_multi_hidpi:"bg-multi-hidpi",data_poster:"poster",class_applied:"applied",class_loading:"loading",class_loaded:"loaded",class_error:"error",class_entered:"entered",class_exited:"exited",unobserve_completed:!0,unobserve_entered:!1,cancel_on_exit:!0,callback_enter:null,callback_exit:null,callback_applied:null,callback_loading:null,callback_loaded:null,callback_error:null,callback_finish:null,callback_cancel:null,use_native:!1},c=function(t){return n({},r,t)},u=function(n,t){var e,i="LazyLoad::Initialized",o=new n(t);try{e=new CustomEvent(i,{detail:{instance:o}})}catch(n){(e=document.createEvent("CustomEvent")).initCustomEvent(i,!1,!1,{instance:o})}window.dispatchEvent(e)},l="src",s="srcset",f="sizes",d="poster",_="llOriginalAttrs",g="loading",v="loaded",b="applied",p="error",h="native",m="data-",E="ll-status",I=function(n,t){return n.getAttribute(m+t)},y=function(n){return I(n,E)},A=function(n,t){return function(n,t,e){var i="data-ll-status";null!==e?n.setAttribute(i,e):n.removeAttribute(i)}(n,0,t)},k=function(n){return A(n,null)},L=function(n){return null===y(n)},w=function(n){return y(n)===h},x=[g,v,b,p],O=function(n,t,e,i){n&&(void 0===i?void 0===e?n(t):n(t,e):n(t,e,i))},N=function(n,t){o?n.classList.add(t):n.className+=(n.className?" ":"")+t},C=function(n,t){o?n.classList.remove(t):n.className=n.className.replace(new RegExp("(^|\\s+)"+t+"(\\s+|$)")," ").replace(/^\s+/,"").replace(/\s+$/,"")},M=function(n){return n.llTempImage},z=function(n,t){if(t){var e=t._observer;e&&e.unobserve(n)}},R=function(n,t){n&&(n.loadingCount+=t)},T=function(n,t){n&&(n.toLoadCount=t)},G=function(n){for(var t,e=[],i=0;t=n.children[i];i+=1)"SOURCE"===t.tagName&&e.push(t);return e},D=function(n,t){var e=n.parentNode;e&&"PICTURE"===e.tagName&&G(e).forEach(t)},V=function(n,t){G(n).forEach(t)},F=[l],j=[l,d],P=[l,s,f],S=function(n){return!!n[_]},U=function(n){return n[_]},$=function(n){return delete n[_]},q=function(n,t){if(!S(n)){var e={};t.forEach((function(t){e[t]=n.getAttribute(t)})),n[_]=e}},H=function(n,t){if(S(n)){var e=U(n);t.forEach((function(t){!function(n,t,e){e?n.setAttribute(t,e):n.removeAttribute(t)}(n,t,e[t])}))}},B=function(n,t,e){N(n,t.class_loading),A(n,g),e&&(R(e,1),O(t.callback_loading,n,e))},J=function(n,t,e){e&&n.setAttribute(t,e)},K=function(n,t){J(n,f,I(n,t.data_sizes)),J(n,s,I(n,t.data_srcset)),J(n,l,I(n,t.data_src))},Q={IMG:function(n,t){D(n,(function(n){q(n,P),K(n,t)})),q(n,P),K(n,t)},IFRAME:function(n,t){q(n,F),J(n,l,I(n,t.data_src))},VIDEO:function(n,t){V(n,(function(n){q(n,F),J(n,l,I(n,t.data_src))})),q(n,j),J(n,d,I(n,t.data_poster)),J(n,l,I(n,t.data_src)),n.load()}},W=["IMG","IFRAME","VIDEO"],X=function(n,t){!t||function(n){return n.loadingCount>0}(t)||function(n){return n.toLoadCount>0}(t)||O(n.callback_finish,t)},Y=function(n,t,e){n.addEventListener(t,e),n.llEvLisnrs[t]=e},Z=function(n,t,e){n.removeEventListener(t,e)},nn=function(n){return!!n.llEvLisnrs},tn=function(n){if(nn(n)){var t=n.llEvLisnrs;for(var e in t){var i=t[e];Z(n,e,i)}delete n.llEvLisnrs}},en=function(n,t,e){!function(n){delete n.llTempImage}(n),R(e,-1),function(n){n&&(n.toLoadCount-=1)}(e),C(n,t.class_loading),t.unobserve_completed&&z(n,e)},on=function(n,t,e){var i=M(n)||n;nn(i)||function(n,t,e){nn(n)||(n.llEvLisnrs={});var i="VIDEO"===n.tagName?"loadeddata":"load";Y(n,i,t),Y(n,"error",e)}(i,(function(o){!function(n,t,e,i){var o=w(t);en(t,e,i),N(t,e.class_loaded),A(t,v),O(e.callback_loaded,t,i),o||X(e,i)}(0,n,t,e),tn(i)}),(function(o){!function(n,t,e,i){var o=w(t);en(t,e,i),N(t,e.class_error),A(t,p),O(e.callback_error,t,i),o||X(e,i)}(0,n,t,e),tn(i)}))},an=function(n,t,e){!function(n){n.llTempImage=document.createElement("IMG")}(n),on(n,t,e),function(n){S(n)||(n[_]={backgroundImage:n.style.backgroundImage})}(n),function(n,t,e){var i=I(n,t.data_bg),o=I(n,t.data_bg_hidpi),r=a&&o?o:i;r&&(n.style.backgroundImage='url("'.concat(r,'")'),M(n).setAttribute(l,r),B(n,t,e))}(n,t,e),function(n,t,e){var i=I(n,t.data_bg_multi),o=I(n,t.data_bg_multi_hidpi),r=a&&o?o:i;r&&(n.style.backgroundImage=r,function(n,t,e){N(n,t.class_applied),A(n,b),e&&(t.unobserve_completed&&z(n,t),O(t.callback_applied,n,e))}(n,t,e))}(n,t,e)},rn=function(n,t,e){!function(n){return W.indexOf(n.tagName)>-1}(n)?an(n,t,e):function(n,t,e){on(n,t,e),function(n,t,e){var i=Q[n.tagName];i&&(i(n,t),B(n,t,e))}(n,t,e)}(n,t,e)},cn=function(n){n.removeAttribute(l),n.removeAttribute(s),n.removeAttribute(f)},un=function(n){D(n,(function(n){H(n,P)})),H(n,P)},ln={IMG:un,IFRAME:function(n){H(n,F)},VIDEO:function(n){V(n,(function(n){H(n,F)})),H(n,j),n.load()}},sn=function(n,t){(function(n){var t=ln[n.tagName];t?t(n):function(n){if(S(n)){var t=U(n);n.style.backgroundImage=t.backgroundImage}}(n)})(n),function(n,t){L(n)||w(n)||(C(n,t.class_entered),C(n,t.class_exited),C(n,t.class_applied),C(n,t.class_loading),C(n,t.class_loaded),C(n,t.class_error))}(n,t),k(n),$(n)},fn=["IMG","IFRAME","VIDEO"],dn=function(n){return n.use_native&&"loading"in HTMLImageElement.prototype},_n=function(n,t,e){n.forEach((function(n){return function(n){return n.isIntersecting||n.intersectionRatio>0}(n)?function(n,t,e,i){var o=function(n){return x.indexOf(y(n))>=0}(n);A(n,"entered"),N(n,e.class_entered),C(n,e.class_exited),function(n,t,e){t.unobserve_entered&&z(n,e)}(n,e,i),O(e.callback_enter,n,t,i),o||rn(n,e,i)}(n.target,n,t,e):function(n,t,e,i){L(n)||(N(n,e.class_exited),function(n,t,e,i){e.cancel_on_exit&&function(n){return y(n)===g}(n)&&"IMG"===n.tagName&&(tn(n),function(n){D(n,(function(n){cn(n)})),cn(n)}(n),un(n),C(n,e.class_loading),R(i,-1),k(n),O(e.callback_cancel,n,t,i))}(n,t,e,i),O(e.callback_exit,n,t,i))}(n.target,n,t,e)}))},gn=function(n){return Array.prototype.slice.call(n)},vn=function(n){return n.container.querySelectorAll(n.elements_selector)},bn=function(n){return function(n){return y(n)===p}(n)},pn=function(n,t){return function(n){return gn(n).filter(L)}(n||vn(t))},hn=function(n,e){var o=c(n);this._settings=o,this.loadingCount=0,function(n,t){i&&!dn(n)&&(t._observer=new IntersectionObserver((function(e){_n(e,n,t)}),function(n){return{root:n.container===document?null:n.container,rootMargin:n.thresholds||n.threshold+"px"}}(n)))}(o,this),function(n,e){t&&window.addEventListener("online",(function(){!function(n,t){var e;(e=vn(n),gn(e).filter(bn)).forEach((function(t){C(t,n.class_error),k(t)})),t.update()}(n,e)}))}(o,this),this.update(e)};return hn.prototype={update:function(n){var t,o,a=this._settings,r=pn(n,a);T(this,r.length),!e&&i?dn(a)?function(n,t,e){n.forEach((function(n){-1!==fn.indexOf(n.tagName)&&function(n,t,e){n.setAttribute("loading","lazy"),on(n,t,e),function(n,t){var e=Q[n.tagName];e&&e(n,t)}(n,t),A(n,h)}(n,t,e)})),T(e,0)}(r,a,this):(o=r,function(n){n.disconnect()}(t=this._observer),function(n,t){t.forEach((function(t){n.observe(t)}))}(t,o)):this.loadAll(r)},destroy:function(){this._observer&&this._observer.disconnect(),vn(this._settings).forEach((function(n){$(n)})),delete this._observer,delete this._settings,delete this.loadingCount,delete this.toLoadCount},loadAll:function(n){var t=this,e=this._settings;pn(n,e).forEach((function(n){z(n,t),rn(n,e,t)}))},restoreAll:function(){var n=this._settings;vn(n).forEach((function(t){sn(t,n)}))}},hn.load=function(n,t){var e=c(t);rn(n,e)},hn.resetStatus=function(n){k(n)},t&&function(n,t){if(t)if(t.length)for(var e,i=0;e=t[i];i+=1)u(n,e);else u(n,t)}(hn,window.lazyLoadOptions),hn}));



/* tilt.js */
"use strict";
var _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (t) { return typeof t } : function (t) { return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t }; !function (t) { "function" == typeof define && define.amd ? define(["jquery"], t) : "object" === ("undefined" == typeof module ? "undefined" : _typeof(module)) && module.exports ? module.exports = function (i, s) { return void 0 === s && (s = "undefined" != typeof window ? require("jquery") : require("jquery")(i)), t(s), s } : t(jQuery) }(function (t) { return t.fn.tilt = function (i) { var s = function () { this.ticking || (requestAnimationFrame(g.bind(this)), this.ticking = !0) }, e = function () { var i = this; t(this).on("mousemove", o), t(this).on("mouseenter", a), this.settings.reset && t(this).on("mouseleave", l), this.settings.glare && t(window).on("resize", d.bind(i)) }, n = function () { var i = this; void 0 !== this.timeout && clearTimeout(this.timeout), t(this).css({ transition: this.settings.speed + "ms " + this.settings.easing }), this.settings.glare && this.glareElement.css({ transition: "opacity " + this.settings.speed + "ms " + this.settings.easing }), this.timeout = setTimeout(function () { t(i).css({ transition: "" }), i.settings.glare && i.glareElement.css({ transition: "" }) }, this.settings.speed) }, a = function (i) { this.ticking = !1, t(this).css({ "will-change": "transform" }), n.call(this), t(this).trigger("tilt.mouseEnter") }, r = function (i) { return "undefined" == typeof i && (i = { pageX: t(this).offset().left + t(this).outerWidth() / 2, pageY: t(this).offset().top + t(this).outerHeight() / 2 }), { x: i.pageX, y: i.pageY } }, o = function (t) { this.mousePositions = r(t), s.call(this) }, l = function () { n.call(this), this.reset = !0, s.call(this), t(this).trigger("tilt.mouseLeave") }, h = function () { var i = t(this).outerWidth(), s = t(this).outerHeight(), e = t(this).offset().left, n = t(this).offset().top, a = (this.mousePositions.x - e) / i, r = (this.mousePositions.y - n) / s, o = (this.settings.maxTilt / 2 - a * this.settings.maxTilt).toFixed(2), l = (r * this.settings.maxTilt - this.settings.maxTilt / 2).toFixed(2), h = Math.atan2(this.mousePositions.x - (e + i / 2), -(this.mousePositions.y - (n + s / 2))) * (180 / Math.PI); return { tiltX: o, tiltY: l, percentageX: 100 * a, percentageY: 100 * r, angle: h } }, g = function () { return this.transforms = h.call(this), this.reset ? (this.reset = !1, t(this).css("transform", "perspective(" + this.settings.perspective + "px) rotateX(0deg) rotateY(0deg)"), void (this.settings.glare && (this.glareElement.css("transform", "rotate(180deg) translate(-50%, -50%)"), this.glareElement.css("opacity", "0")))) : (t(this).css("transform", "perspective(" + this.settings.perspective + "px) rotateX(" + ("x" === this.settings.disableAxis ? 0 : this.transforms.tiltY) + "deg) rotateY(" + ("y" === this.settings.disableAxis ? 0 : this.transforms.tiltX) + "deg) scale3d(" + this.settings.scale + "," + this.settings.scale + "," + this.settings.scale + ")"), this.settings.glare && (this.glareElement.css("transform", "rotate(" + this.transforms.angle + "deg) translate(-50%, -50%)"), this.glareElement.css("opacity", "" + this.transforms.percentageY * this.settings.maxGlare / 100)), t(this).trigger("change", [this.transforms]), void (this.ticking = !1)) }, c = function () { var i = this.settings.glarePrerender; if (i || t(this).append('<div class="js-tilt-glare"><div class="js-tilt-glare-inner"></div></div>'), this.glareElementWrapper = t(this).find(".js-tilt-glare"), this.glareElement = t(this).find(".js-tilt-glare-inner"), !i) { var s = { position: "absolute", top: "0", left: "0", width: "100%", height: "100%" }; this.glareElementWrapper.css(s).css({ overflow: "hidden", "pointer-events": "none" }), this.glareElement.css({ position: "absolute", top: "50%", left: "50%", "background-image": "linear-gradient(0deg, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 100%)", width: "" + 2 * t(this).outerWidth(), height: "" + 2 * t(this).outerWidth(), transform: "rotate(180deg) translate(-50%, -50%)", "transform-origin": "0% 0%", opacity: "0" }) } }, d = function () { this.glareElement.css({ width: "" + 2 * t(this).outerWidth(), height: "" + 2 * t(this).outerWidth() }) }; return t.fn.tilt.destroy = function () { t(this).each(function () { t(this).find(".js-tilt-glare").remove(), t(this).css({ "will-change": "", transform: "" }), t(this).off("mousemove mouseenter mouseleave") }) }, t.fn.tilt.getValues = function () { var i = []; return t(this).each(function () { this.mousePositions = r.call(this), i.push(h.call(this)) }), i }, t.fn.tilt.reset = function () { t(this).each(function () { var i = this; this.mousePositions = r.call(this), this.settings = t(this).data("settings"), l.call(this), setTimeout(function () { i.reset = !1 }, this.settings.transition) }) }, this.each(function () { var s = this; this.settings = t.extend({ maxTilt: t(this).is("[data-tilt-max]") ? t(this).data("tilt-max") : 20, perspective: t(this).is("[data-tilt-perspective]") ? t(this).data("tilt-perspective") : 300, easing: t(this).is("[data-tilt-easing]") ? t(this).data("tilt-easing") : "cubic-bezier(.03,.98,.52,.99)", scale: t(this).is("[data-tilt-scale]") ? t(this).data("tilt-scale") : "1", speed: t(this).is("[data-tilt-speed]") ? t(this).data("tilt-speed") : "400", transition: !t(this).is("[data-tilt-transition]") || t(this).data("tilt-transition"), disableAxis: t(this).is("[data-tilt-disable-axis]") ? t(this).data("tilt-disable-axis") : null, axis: t(this).is("[data-tilt-axis]") ? t(this).data("tilt-axis") : null, reset: !t(this).is("[data-tilt-reset]") || t(this).data("tilt-reset"), glare: !!t(this).is("[data-tilt-glare]") && t(this).data("tilt-glare"), maxGlare: t(this).is("[data-tilt-maxglare]") ? t(this).data("tilt-maxglare") : 1 }, i), null !== this.settings.axis && (console.warn("Tilt.js: the axis setting has been renamed to disableAxis. See https://github.com/gijsroge/tilt.js/pull/26 for more information"), this.settings.disableAxis = this.settings.axis), this.init = function () { t(s).data("settings", s.settings), s.settings.glare && c.call(s), e.call(s) }, this.init() }) }, t("[data-tilt]").tilt(), !0 });