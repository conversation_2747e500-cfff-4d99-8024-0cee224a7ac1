﻿// *************** Foreign Students Enrol ************* //

var YemenCountryCode = '56';
var suggested = 'has-suggested-value';

function safeParseInt(sVal, defVal) {
    var iVal = parseInt(sVal);

    if (isNaN(iVal))
        iVal = defVal;

    return iVal;
}

function pad_zeros(num, pad) {
    var str = "" + num;
    return pad.substring(0, pad.length - str.length) + str
}


function fs_copy_value(idSrc, idDst) {
    $('#' + idDst).val($('#' + idSrc).val()).addClass(suggested);
}

function setFieldValue(fldName, fldValue) {
    $('#' + fldName).val(fldValue).addClass(suggested);
    $('#_' + fldName).val(fldValue); // for disabled/hidden // we should check if the fld is disabled
}

function suggestValue(id, val) {
    if ($('#' + id).val().length == 0)
        $('#' + id).val(val).addClass(suggested);
}

function forceSuggestValue(id, val) {
    setFieldValue(id, val);
}

function isSuggestEnabled() {

    var cmd = getUrlParameter("cmd");

    return cmd == "add"; // || cmd == "edit";
}

function on_edu_level_changed_show_fields(_id) {
    var sel_val = $('#' + _id).val();
    if (sel_val == '01') {
        $('#last_edu_level,#last_edu_country').val('');
        $("#r_stud_entry_date, #r_stud-last-edu, #r_last_edu_level, #r_last_seat_no").hide();

        $('#last_edu_level2,#last_edu_country2').val('');
        $("#r_last_edu_level2").hide();
    }
    else if (sel_val == '02') {
        $("#r_stud_entry_date, #r_stud-last-edu, #r_last_edu_level, #r_last_seat_no").show();

        $('#last_edu_level2,#last_edu_country2').val('');
        $("#r_last_edu_level2").hide();
    }
    else {
        $("#r_stud_entry_date, #r_stud-last-edu, #r_last_edu_level, #r_last_seat_no").show();
        $("#r_last_edu_level2").show();
    }

    show_element('r_last_seat_no', sel_val == '09' || sel_val == '12');
    show_element('r_edu_section', sel_val == '11' || sel_val == '12');
}





function on_edu_level_changed_adjust_values(_id) {
    var sel_val = $('#' + _id).val();

    var level = safeParseInt(sel_val, 0);
    var year = safeParseInt($("#_edu_year").val(), 0);
    var date = new Date();

    on_edu_level_changed_show_fields(_id);

    if (getUrlParameter("cmd") == "find"  || getUrlParameter("fm-mode") == "renew")
        return;

    //$('#edu_stage').val(sel_val <= '09' ? '1' : '2');
    //$('#edu_section').val(sel_val <= '10' ? '' : '1').addClass(suggested);


    setFieldValue('edu_stage', sel_val <= '09' ? '1' : '2');
  //  setFieldValue('edu_section', sel_val <= '10' ? '' : '1'); // set section to 1=sci by def


    if (sel_val != '01') {

        var prev_level = (level - 1).toString();
        if (prev_level < 10)
            prev_level = "0" + prev_level;

        //  $('#last_edu_level').val(prev_level.toString());
        setFieldValue('last_edu_level', prev_level.toString());
    };

    if (level > 2) {

        var prev_level2 = (level - 2).toString();
        if (prev_level2 < 10)
            prev_level2 = "0" + prev_level2;
                
        setFieldValue('last_edu_level2', prev_level2.toString());
    };

    //suggestValue
    if (isSuggestEnabled())
        setFieldValue('stud_dob', '01/01/' + (date.getFullYear() - (level + 6)));

}

//--------------

function on_edu_gov_changed() {
    cascade_coding('edu_gov', 'edu_dist', 'fs-dists');
    $("#live_addr_gov,#resp_addr_gov").val($("#edu_gov").val());

    document.getElementById("live_addr_gov").onchange();
    document.getElementById("resp_addr_gov").onchange();

}

function on_edu_dist_changed() {
    cascade_coding('edu_dist', 'edu_school', 'fs-schol');
    $("#live_addr_dist,#resp_addr_dist").val($("#edu_dist").val());
}

// ----------------------

function on_student_name_changed() {
    //if (!isSuggestEnabled()) return;


    var father_name = $('#name2').val() + ' ' + $('#name3').val() + ' ' + $('#name4').val() + ' ' + $('#lname').val(); 
    var rel_type = $("#resp_relation").val();

    if (rel_type != "01")
        return;

    // $("#resp_name").val(s_name.substring(s_name.indexOf(' ') + 1, 64)).addClass(suggested);;

    father_name.replace('  ', ' '); // remove extra spaces

    $("#resp_name").val(father_name);



    
}

function on_relation_changed() {
    var rel_type = $("#resp_relation").val();

    if (rel_type != "01")
        $('#resp_name').val('').focus();
    else
        on_student_name_changed();
}

function on_student_pob_changed(eId) {

    if (!isSuggestEnabled())
        return;

    var s_pob = $('#' + eId).val();
    show_element('stud_entry_date', s_pob != YemenCountryCode); // hide if pob is yemen // we may rethink about it
}

function on_student_nat_changed(eId) {

    if (!isSuggestEnabled())
        return;

    var s_nat = $('#' + eId).val();

    if (s_nat == YemenCountryCode) {
        window.alert('لا يمكن ان يكون الطالب الوافد يحمل جنسية : اليمن');
        $('#' + eId).val('').focus();
        return;
    };

    $("#pob_country, #stud_doc_issuer, #last_edu_country,#last_edu_country2, #resp_doc_issuer").val(s_nat).addClass(suggested);;

    if (s_nat == '3' || s_nat == '4' || s_nat == '16') { // somalia, ethiubia, erteria
        $('#stud_cat').val('2'); // refugee
        document.getElementById('stud_cat').onchange();
    }
    else {
        $('#stud_cat').val('1'); // not refugee
        document.getElementById('stud_cat').onchange();
    }

    
}

function on_student_cat_changed(eId) {
    

    var s_cat = $('#' + eId).val();

    $("#stud_doc_type").val(s_cat).addClass(suggested); //.attr('readonly','readonly');
    $("#resp_doc_type").val(s_cat).addClass(suggested); //.attr('readonly','readonly');

    if (!isSuggestEnabled())
        return;

    if (s_cat == '2') { //  refugee
        $("#stud_doc_issuer").val(YemenCountryCode).addClass(suggested);
        $("#receipt_no").val('');
        $("#receipt_date").val('');
        $("#r_receipt_no").hide();
        $("#resp_job").val('لاجئ');
        
        if ($("#stud_nat").val() == "3") // somalia
            $("#stud_doc_no").val("247-");
        else
            $("#stud_doc_no").val("677-");

    }
    else {
        var stud_nat = $('#stud_nat').val();
        $("#stud_doc_issuer").val(stud_nat).addClass(suggested);
        $("#r_receipt_no").show();
        fs_copy_value("edu_gov","stud_res_issuer");
        $("#stud_doc_no").val('');
        $("#resp_job").val('');
    }
}


function on_resp_cat_changed(eId) {
    if (!isSuggestEnabled())
        return;

    var s_cat = $('#' + eId).val();

    $("#resp_doc_type").val(s_cat).addClass(suggested);

    if (s_cat == '2') { //  refugee
        $("#resp_doc_issuer").val(YemenCountryCode).addClass(suggested);
    }
    else {
        var stud_nat = $('#stud_nat').val();
        $("#resp_doc_issuer").val(stud_nat).addClass(suggested);
    }
}


function on_validity_years_changed(valid_years_id, start_date_id, exp_date_id) {

    var years = safeParseInt($('#' + valid_years_id).val(), 0);
    if (years == 0) return;
    if ($('#' + start_date_id).val().length < 1) return;
    var start_date_parts = $('#' + start_date_id).val().split('/');
    var date = new Date(parseInt(start_date_parts[2]) + years, parseInt(start_date_parts[1]) - 1, parseInt(start_date_parts[0]) - 1);
    $('#' + exp_date_id).val(pad_zeros(date.getDate(), "00") + "/" + pad_zeros((date.getMonth() + 1), "00") + "/" + date.getFullYear());
}

function copy_stud_res_doc() {
    fs_copy_value('stud_res_no', 'resp_res_no');
    fs_copy_value('stud_res_issuer', 'resp_res_issuer');
    fs_copy_value('stud_res_date', 'resp_res_date');
    fs_copy_value('stud_res_valid', 'resp_res_valid');
    fs_copy_value('stud_res_expiry', 'resp_res_expiry');

    fs_copy_value('stud_nat', 'resp_nat');
}

function copy_stud_entry_doc() {

   // fs_copy_value('stud_cat', 'resp_cat');
   // fs_copy_value('stud_doc_type', 'resp_doc_type');
    fs_copy_value('stud_doc_no', 'resp_doc_no');
    fs_copy_value('stud_doc_issuer', 'resp_doc_issuer');
    fs_copy_value('stud_doc_date', 'resp_doc_date');
    fs_copy_value('stud_doc_valid', 'resp_doc_valid');
    fs_copy_value('stud_doc_expiry', 'resp_doc_expiry');
    fs_copy_value('stud_entry_date', 'resp_entry_date');

   // show_on_list_value('resp_cat', '2', 'r_resp-res-doc;r_resp_res_no;r_resp_res_issuer;r_resp_res_date;r_resp_res_expiry;', 'r_resp-res-doc;r_resp_res_no;r_resp_res_issuer;r_resp_res_date;r_resp_res_expiry;', 'resp_cat', false);
   // on_resp_cat_changed('resp_cat');
}


function auto_copy_stud_data_to_resp() {
    if (!isSuggestEnabled())
        return;

    copy_stud_entry_doc();
    copy_stud_res_doc();
/*
    fs_copy_value("edu_gov", "resp_addr_gov");
    fs_copy_value("edu_gov", "live_addr_gov");

    document.getElementById("live_addr_gov").onchange();
    document.getElementById("resp_addr_gov").onchange();

    fs_copy_value("edu_dist", "resp_addr_dist");
    fs_copy_value("edu_dist", "live_addr_dist");
*/
    
}