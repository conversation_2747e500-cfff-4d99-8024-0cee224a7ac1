﻿var js_ver=1.170; // should be sync-ed with value in master-page
var hsUiDateFormat = "dd/mm/yy";
var hsUiTimeFormat="HH:mm:ss"; // p= AM/PM
var hsUiLang = "en";
//-----
var req_js_ver = js_ver;
var is_page_loaded = !1; // is page loading completed
var isEditModeProtected = !0; // warn if the user leaves edit-mode without saving 
var isAutoFormSubmit=!1; // auto submit the form if <Enter> is clicked on the last field
var flds_order=null;
var JS="javascript:";
var is_dirty = !1, submitted = !1, dlg_callback = null;
var en_arrows = true; // to move
var fm_rc = 0, fm_msg = null;

// -----------------------

var K = {
    BS:8,TAB:9,DLE:16,
    LFT: 37, UP: 38, RGT: 39, DOWN: 40,
   

    A: 65,B: 66,F: 70, P: 80,T: 84,
    
    F1: 112,F2: 113,F3: 114,F4: 115,
    F5: 116,F6: 117,F7: 118,F8: 119,

    F9: 120,
    F10: 121,
    F11: 122,
    F12: 123,

    DEL: 46,
   
    isF: function (k) {
        return k >= K.F1 && k <= K.F12;
    },

    br: "<br/>",
    hr: "<hr/>",
    nbsp: "&nbsp;",
    pipe: "&nbsp;|&nbsp;"
}

// ------------------------

var enRes = {
    OK: "OK",
    cancel:"Cancel",
    find:"Find",
    help: "Help",
    print:"Print",
    done: "Done",
    failed: "Failed",
    success: "completed successfully",
    warnings: "Warnings",
    refresh: "Refresh",
    refresh_list: "Refresh List",
    sending: "Sending",
    send_complete: "Sending complete..",
    send_offline_data: "Send offline data",
    remove_offline_data: "Remove offline data",
    switch_offline: "Switch to offline mode",
    js_err:"Error during loading the page",
    conn_err: "Connection Error",
    wait: "Please wait..",
    not_allowed: "Not allowed",
    sure: "Sure",
    fld_is_blank: "Field is blank",
    fld_is_required: "Field is required",
    incorrect_value: "Incorrect value",
    must_refresh_warn: "Warning: Some cached files are not up to date, please <a href='javascript:RefreshPage()'>click here to refresh the files</a>, or press Ctrl+F5",
    exit_no_save: "Leave the page without saving?",
    press_ctrl_help: "",
    press_ctrl_help_extra: "",
    auto_fld: "Leave blank to get auto number",
    find_dld_file: "If the file is not opened automatically, you can find the file in browser downloads folder",
    all: "All"

};

var arRes = {
    OK: "موافق",
    cancel: "تراجع",
    find: "بحث",
    help: "مساعدة",
    print: "طباعة",
    done: "تم",
    failed: "فشلت العملية",
    success: "تمت العملية بنجاح",
    warnings: "تحذيرات",
    refresh: "تحديث",
    refresh_list: "تحديث القائمة",
    sending: "إرسال",
    send_complete: "تم الإرسال",
    send_offline_data: "إرسال البيانات المؤقتة",
    remove_offline_data: "حذف البيانات المؤقتة",
    switch_offline: "التحويل إلى وضع عدم الإتصال - الحفظ المؤقت على هذا الجهاز",
    js_err:"حصل خطأ أثناء عرض الصفحة",
    conn_err: "غير قادر على الإتصال",
    wait: "الرجاء الإنتظار..",
    not_allowed: "غير مسموح",
    sure: "متأكد",
    fld_is_blank: "الحقل فارغ",
    fld_is_required: "الحقل مطلوب",
    incorrect_value :"قيمة غير مناسبة",     
    refresh_warn: "بعض ملفات النظام تحتاج إلى تحديث، <a href='javascript:RefreshPage()'>اضعط هنا لتحديث الملفات</a>,  إذا استمرت هذه الرسالة بالظهور اضغط Ctrl+F5 للتحديث",
    exit_no_save: "الخروج من الصفحة بدون حفظ أي تعديلات على القيد؟",
    press_ctrl_help: "",
    press_ctrl_help_extra: "",
    auto_fld: "يمكن ترك الحقل فارغا للحصول على الرقم آليا",
    find_dld_file :"إذا لم يتم فتح الملف آليا - يمكن العثور على الملف في مجلد التنزيلات الخاص بالمتصفح",
    all : "الكل"
};

var frRes = {
    OK: "OK",
    cancel: "Annuler",
    find: "Trouver",
    help: "Assistance",
    print: "imprimé",
    done: "Done",
    failed: "L'opération a échoué",
    success: "Opération terminée avec succès",
    warnings: "Attention",
    refresh: "Refresh",
    refresh_list: "Refresh List",
    sending: "Sending",
    send_complete: "Sending complete..",
    send_offline_data: "Send offline data",
    remove_offline_data: "Remove offline data",
    switch_offline: "Switch to offline mode",
    js_err: "Error during loading the page",
    conn_err: "Connection Error",
    wait: "Veuillez patienter..",
    not_allowed: "Not allowed",
    sure: "Sure",
    fld_is_blank: "Field is blank",
    fld_is_required: "Le champ est obligatoire",
    incorrect_value: "Incorrect value",
    must_refresh_warn: "Warning: Some cached files are not up to date, please <a href='javascript:RefreshPage()'>click here to refresh the files</a>, or press Ctrl+F5",
    exit_no_save: "Leave the page without saving?",
    press_ctrl_help: "",
    press_ctrl_help_extra: "",
    auto_fld: "Leave blank to get auto number",
    find_dld_file: "If the file is not opened automatically, you can find the file in browser downloads folder",
    all: "All"

};

function h$(e, p) { //  get jQry obj of the elem
    if (arguments.length < 2) p = null;
    if ("string" != typeof e)
        return $(e);
    else {
        if (e.startsWith("#"))
            return $(e, p);
        else
            return $("#" + e, p);
    };
}

window.addEventListener('error', function (e) {
    var stack = e.error.stack;
    var m = hs.R.js_err + K.hr + e.error.toString();

    m += K.br + hs.link(hs.R.help, "js:hs.help('js-err')");

    if (stack) 
        m += K.hr + stack;
    
    if (hs.fm.hasUiFlag(0x0100))
      hs.msg.error(m);
});




/*
window.addEventListener('popstate', function(event)  {
    var m = "location: " + document.location + ", state: " + JSON.stringify(event.state);
  log(m);
    hs.msg.toast(m);
});

window.addEventListener('hashchange', function () {
    log('The hash has changed!')
}, false);
*/


String.prototype.format = String.prototype.f = function () { // C# like format
    var args = arguments;
    return this.replace(/\{\{|\}\}|\{(\d+)\}/g, function (m, n) {
        if (m == "{{") return "{"; 
        if (m == "}}") return "}";
        return args[n] == null ? "" : args[n];
    });
};

String.prototype.fmtEx = function () { // HsEasyForm FormatEx
    return this.replace(/\[\#(\w+)\#\]/g, function (m, w) {
        var e = $("#" + w);
        if (e.is("select"))
            return e.val() + " " + e.find(":selected").text();
        else
            return e.val();
    });
};

var enKeys = "abcdefghijklmnopqrstuvwxyz.,[];'`";
var arKeys = "شلؤيثبلاهتنمةىخحضقسفعرصءغئزوجدكطذ";
var arKeysLen = arKeys.length, enKeysLen = enKeys.length;

// [ cause exception

function keyTrans(s) {
    if (hs.isEmpty(s))
        return "";

    var t = "";
    for (var i = 0; i < s.length; i++) {
        var idx = enKeys.indexOf(s.charAt(i));

        if (idx >= 0 && idx < arKeysLen)
            t = t + arKeys.charAt(idx);

        // hs.msg.toast(idx + " " + t);
    };

   // hs.msg.toast("s='" + s + "' t=" + t)

    return t;
};


function keyTransToEng(s) {
    if (hs.isEmpty(s))
        return "";

    var t = "";
    for (var i = 0; i < s.length; i++) {
        var idx = arKeys.indexOf(s.charAt(i));

        if (idx >= 0 && idx < enKeysLen)
            t = t + enKeys.charAt(idx);

        // hs.msg.toast(idx + " " + t);
    };

    // hs.msg.toast("s='" + s + "' t=" + t)

    return t;
};



String.prototype.startsWithEx = function (s) {
    return this.startsWith(s) || this.startsWith(keyTrans(s));
};


String.prototype.containsEx = function (s) {
    return this.contains(s) || this.contains(keyTrans(s));
};

String.prototype.containsAllEx = function (s) {
    return this.containsAll(s) || this.containsAll(keyTrans(s));
};

String.prototype.startsWith = function (s) {
    if (hs.isEmpty(s) || hs.isEmpty(this))
        return false;
    
     s = s.toLowerCase();

     return this.toLowerCase().indexOf(s) == 0;
};

String.prototype.EndsWith = function (s) {
    if (hs.isEmpty(s) || hs.isEmpty(this))
        return false;

    s = s.toLowerCase();

    var len = this.length;

    if (len == 0)
        return false;
    len--;

    return this.toLowerCase().lastIndexOf(s) == len;
};

String.prototype.contains = function (s) {

    if (hs.isEmpty(s)) return false;

     s = s.toLowerCase();
    return this.toLowerCase().indexOf(s) >= 0;
};

String.prototype.containsAll = function (s) {
    if (hs.isEmpty(s)) return false;
    s = s.replace('[', '')
    s = s.replace(/[*]/gi, ' ')
    var sa = s.split(' ');
    var i, regex_str = '^';
    for (i = 0; i < sa.length; i++)
        if (!hs.isEmpty(sa[i]))
            regex_str += "(?=.*" + sa[i] + "+)";

    var regex = new RegExp(regex_str,'i'); 
    return this.search(regex) >= 0;
};

String.prototype.count = function (s) {
    return (this.match(new RegExp(s, "g")) || []).length;
}

function noop() {
}




// show elm html content in dlg
function popup(e) {
    e = h$(e);
    if (e.length > 0) {
        hs.ui.showToolsDlg(e.html());
    };
}

function isTouch() {
    return hs.pref("touch") == "1";
   // || ('ontouchstart' in window) || (navigator.maxTouchPoints > 0) || (navigator.msMaxTouchPoints > 0));
}

function click_hover(s) { // s is url or e
    if (isTouch())
        return;
    if (s.contains("/") || s.contains("?") || s.contains("="))
        go(s) // s is url
    else
        popup(s); // s is e

}




function showEx(fmt_ex) { // testing only
    if (arguments.length < 1)
        fmt_ex = "Transfer Amount ([#amount#]) <br/> from acc [#acc_id#] <br/> to account [#to_acc_id#]";

    hs.msg.box(fmt_ex.fmtEx());
}



function GetElm(e_id) {
    if (hs.isEmpty(e_id))
        return null;

    var oE = document.getElementById(e_id);
   if (oE != null)
       return oE;

   return oE;
};

function GetElmInParent(e_id) {
    return window.parent.document.getElementById(e_id);
};


function HideElm(id) {
    $('#' + id).hide();
}


function ShowElm(id) {
    $('#' + id).show();
}

function SilentHideElm(id) {
    $('#' + id).hide();
}

function toggleHide(id, a) {
    if (a != null) {
        if (hs.e.isVisible(id))
            $("#a" + id).removeClass("expanded").addClass("collapsed");
        else
            $("#a" + id).removeClass("collapsed").addClass("expanded");
    };

    

    h$(id).slideToggle({ duration: 0 }); // $("#" + id)
}

function delayHideElm(eId, delay_ms) {
    window.setTimeout("SilentHideElm('" + eId + "')", delay_ms);
}


function isIE()
{
    var ua=window.navigator.userAgent;
    return ua.indexOf("MSIE ") > 0 || !!navigator.userAgent.match(/Trident.*rv\:11\./);
};

 
 //---------------
function kv(k, v) {
    return "<span class='kv'><b class='k'>" + k + "</b><span class='v'>" + v + "</span></span>";
}


//--------------------



//*******************************

var dlg_box_div_id="dlg_box";
var dlg_parent_cover="dlg_parent_cover";

function HideCover(topWnd) {
   
    var d=topWnd.document;
    var cover_div=d.getElementById(dlg_parent_cover);
    if (cover_div != null) 
      d.body.removeChild(cover_div);
    
};



function ShowCover(opacity) {
    
    var cover_div = GetElm(dlg_parent_cover);
    if (cover_div != null)
        return cover_div;
  
   
  var page_size=getPageSize();
  var page_height=(page_size[1] + 'px');
  
  cover_div=document.createElement("div");
  cover_div.id=dlg_parent_cover;
  cover_div.style.height =page_height; 
  cover_div.style.disply = 'block';
    document.body.appendChild(cover_div);
  
  $(cover_div).css("z-index", "1009").css('opacity',opacity);


  return cover_div;
}


// FCK funcs - borrowed fom FCK editor
function FCK_GetScrollPosition(relativeWindow )
{
    if (!isIE())
      return { X : relativeWindow.pageXOffset, Y : relativeWindow.pageYOffset } ;
        
	var oDoc = relativeWindow.document ;

	// Try with the doc element.
	var oPos = { X : oDoc.documentElement.scrollLeft, Y : oDoc.documentElement.scrollTop } ;

	if ( oPos.X > 0 || oPos.Y > 0 )
		return oPos ;

	// If no scroll, try with the body.
	return { X : oDoc.body.scrollLeft, Y : oDoc.body.scrollTop } ;
	
}


function FCK_GetViewPaneSize( win )
{
    if (!isIE())
       return { Width : win.innerWidth, Height : win.innerHeight } ;
    
    
	var oSizeSource ;

	var oDoc = win.document.documentElement ;
	if ( oDoc && oDoc.clientWidth )				// IE6 Strict Mode
		oSizeSource = oDoc ;
	else
		oSizeSource = win.document.body ;		// Other IEs

	if ( oSizeSource )
		return { Width : oSizeSource.clientWidth, Height : oSizeSource.clientHeight } ;
	else
		return { Width : 0, Height : 0 } ;
}


//
// getPageSize()
// Returns array with page width, height and window width, height
// Core code from - quirksmode.org
// Edit for Firefox by pHaez
//
function getPageSize(){
	
	var xScroll, yScroll;
	
	if (window.innerHeight && window.scrollMaxY) {	
		xScroll = document.body.scrollWidth;
		yScroll = window.innerHeight + window.scrollMaxY;
	} else if (document.body.scrollHeight > document.body.offsetHeight){ // all but Explorer Mac
		xScroll = document.body.scrollWidth;
		yScroll = document.body.scrollHeight;
	} else { // Explorer Mac...would also work in Explorer 6 Strict, Mozilla and Safari
		xScroll = document.body.offsetWidth;
		yScroll = document.body.offsetHeight;
	}
	
	var windowWidth, windowHeight;
	if (self.innerHeight) {	// all except Explorer
		windowWidth = self.innerWidth;
		windowHeight = self.innerHeight;
	} else if (document.documentElement && document.documentElement.clientHeight) { // Explorer 6 Strict Mode
		windowWidth = document.documentElement.clientWidth;
		windowHeight = document.documentElement.clientHeight;
	} else if (document.body) { // other Explorers
		windowWidth = document.body.clientWidth;
		windowHeight = document.body.clientHeight;
	}	
	
	// for small pages with total height less then height of the viewport
	if(yScroll < windowHeight){
		pageHeight = windowHeight;
	} else { 
		pageHeight = yScroll;
	}

	// for small pages with total width less then width of the viewport
	if(xScroll < windowWidth){	
		pageWidth = windowWidth;
	} else {
		pageWidth = xScroll;
	}


	arrayPageSize = new Array(pageWidth,pageHeight,windowWidth,windowHeight) 
	return arrayPageSize;
}

// *** dialog **** //

var HsDialog =  function(dlg_inst_id) {
    this.inst_id=dlg_inst_id;
    HsDialog.Instance=this;
};

//-----------

HsDialog.Instance = null;
HsDialog.m_div = null;

HsDialog.ShowErr = function(m){
   window.alert(m);
};

//-----------------

function get_dlg_title() {
   if (typeof window.dlg_title == "undefined")
    return '';
   else
    return window.dlg_title
}
    

function dlg_load() {
    HideElm('dlg_loading_img');
}

function getDlgLevel() {
    var l = 0;
    var w = window;
    while (w != window.top) {
        l++;
        w = w.parent;
        if (l > 5) break;
    };

    return l;
}

function openNewTab(_url, _close_dlg) {
    window.open(_url, '_blank');
    if (_close_dlg)
        CloseTopDlg();
}

HsDialog.ShowDialog = function(url, d_class, d_width, d_height) {
    var org_url = url;
    var isTopWin = window == window.top;

    if (HsDialog.Instance==null) {
       HsDialog.ShowErr("Instance was not created");
       return;
    };
    
    url = sind(url);
    
    var topWindow=window;
    
    
    
    var viewSize = FCK_GetViewPaneSize( topWindow ) ;
	var scrollPosition = FCK_GetScrollPosition( topWindow ) ;
	var iTop  = Math.max( scrollPosition.Y + ( viewSize.Height - d_height - 20 ) / 2, 0 ) ;
	var iLeft = Math.max(scrollPosition.X + (viewSize.Width - d_width - 20) / 2, 0);
		
   
   
	var dlg_title = ""; 
   var vu;
   var fe_div;
   var frame_style = "";
     
   vu=""; 
    vu += "<table width='100%' cellpadding='0' cellspacing='0'><tr><td nowrap class='head'>&nbsp;&nbsp;&nbsp;";

   

   // vu += "<a class='cmd icon icon_close' href='javascript:HsDialog.CloseDialog()'>&nbsp;</a>&nbsp;";

    vu += "<a class='cmd icon icon_close' id='dlg_close' href='javascript:HsDialog.CloseDialog();' title='" + hs.L('Close', 'إغلاق') + "'>&nbsp;</a>&nbsp;";

   if (isTopWin)
       vu += "<a class='cmd icon icon_maximize' id='dlg_maxz' href='javascript:HsDialog.Maximize(window);' title='" + hs.L('Maximize','تكبير') + "'>&nbsp;</a>&nbsp;";

    
   vu += "<a class='cmd icon icon_refresh' href='javascript:HsDialog.Refresh();' title='" + hs.L('Refresh', 'تحديث') + "'>&nbsp;</a>&nbsp;";
   vu += "<a class='cmd icon icon_reopen-in-new-tab' href='javascript:openNewTab(\"" + org_url + "\",true)' title='" + hs.L('Reopen in a new tab', 'فتح هذه الصفحة في نافذة جديدة') + "' >&nbsp;</a>";
    

   // Dialog Popup Menu
   vu += "<span class='popup-box'><a style='border-width: 0;' class='cmd link_icon_only icon-windows'>&nbsp;</a><span class='popup-menu' style='width: 400px'>";
   vu += "<a class='cmd icon icon_home' href='/app' target='_blank'>" + hs.L("New page", "نافذة جديدة") + "</a>";
 
   vu += "</span></span>";
   // 

    vu += "<a class='cmd icon icon-check mir-flt' onmousedown='$(\".dlg_box\").draggable()' title='"+ hs.L('Move', 'تحريك') + "'>&nbsp;</a>";

   vu += "&nbsp;&nbsp;&nbsp;<span id='dlg_title'>" + dlg_title + "</span>&nbsp;</td></tr>";
   vu += "<tr><td class='content'><img class='mem_icon dlg_loading' src='/app/s/images/loading.gif' id='dlg_loading_img'/></td></tr>";
   vu += "</table>";
     
  // vu = "";

   d_height -=  30 ; 

   if (isTopWin)
       frame_style = "height:" + d_height + "px";
   else
       frame_style = "height: 100%;  border: solid 1px green";

   frame_style = "height:" + d_height + "px";

   vu += "<iframe id='dlg_frame' onload='dlg_load()' frameborder='0' scrolling='auto' marginwidth='0' style='" + frame_style +"' src='" + url + "'></iframe>";
    
   
   
   window.dlg_title = ''; 

   fe_div=document.getElementById(dlg_box_div_id);
   if (fe_div==null)        
   {
      fe_div=document.createElement("div"); //document.getElementById("fileeditor");
      fe_div.id = dlg_box_div_id;
     
      document.body.appendChild(fe_div);
   };
  
   if (fe_div==null)
   {
      // window.alert(msg);
       return;
   }
   
   
   fe_div.className = "dlg_box";
   
   iTop -= 10;
   d_height += 30;
  
   if (iTop < 0) iTop = 0;
   if (iLeft < 0) iLeft = 0;

   if (!isTopWin)
       fe_div.style.cssText = "position:absolute; left: 0px; right: 0px; top: 0px; bottom:0px; visibility: visible;";
   else
       fe_div.style.cssText = "width:" + d_width + "px; height:" + d_height + "px; top:" + iTop + "px; left:" + iLeft + "px;";
      
   fe_div.innerHTML=vu;
   
   fe_div.style.visibility="visible";
   
   
   HsDialog.m_div=fe_div;
   
   ShowCover(0.7); // increase the number to darken the cover
  
  
 
    $(".dlg_box").addClass("ui-corner-all");//.draggable(); //.resizable();

   $("#dlg_frame").focus();

};

//----------------



ShowContentDialog = function (cont, opt, tit) {
    var page_size = getPageSize();
      
    var d_width = opt.width;
    var d_height = opt.height;
    var isTopWin = window == window.top;
    var topWindow = window;

    if (page_size[0] < d_width) d_width = page_size[0];
    if (page_size[3] < d_height) d_height = page_size[3];

    

    isTopWin = true; // this to avoid maximize the dlg

    var viewSize = FCK_GetViewPaneSize(topWindow);
    var scrollPosition = FCK_GetScrollPosition(topWindow);
    var iTop = Math.max(scrollPosition.Y + (viewSize.Height - d_height - 20) / 2, 0);
    var iLeft = Math.max(scrollPosition.X + (viewSize.Width - d_width - 20) / 2, 0);
    
    var dlg_title = arguments.length >= 3 ? tit : ""; 
    var vu;
    var fe_div;
    var frame_style = "";
    

    vu = "";
    vu += "<table width='100%' cellpadding='0' cellspacing='0'><tr><td nowrap class='head'>&nbsp;&nbsp;&nbsp;";



    vu += "<a class='cmd icon icon_close' href='javascript:HsDialog.CloseDialog()'>&nbsp;</a>&nbsp;";


    hs.page.addContent

    if (isTopWin)
        vu += "<a class='cmd icon icon_maximize' id='dlg_maxz' href='javascript:HsDialog.Maximize(window);' title='" + hs.L('Maximize', 'تكبير') + "'>&nbsp;</a>&nbsp;";


    vu += "&nbsp;&nbsp;&nbsp;<span id='dlg_title'>" + dlg_title + "</span>&nbsp;</td></tr>";
    vu += "<tr><td class='content'></td></tr>";
    vu += "</table>";

    // vu = "";

    d_height -= 30;

    if (isTopWin)
        frame_style = "height:" + d_height + "px";
    else
        frame_style = "height: 100%;  border: solid 1px green";

    frame_style = "height:" + d_height + "px";

    vu += "<div id='dlg_frame' class='scrollable-container' style='padding: 0px;" + frame_style + "'>" + cont + "</div>";
    

    window.dlg_title = '';

    fe_div = document.getElementById(dlg_box_div_id);
    if (fe_div == null) {
        fe_div = document.createElement("div"); //document.getElementById("fileeditor");
        fe_div.id = dlg_box_div_id;

        document.body.appendChild(fe_div);
    };

    if (fe_div == null) {
        // window.alert(msg);
        return;
    }


    fe_div.className = "dlg_box";

    iTop -= 10;
    d_height += 30;



    if (!isTopWin)
        fe_div.style.cssText = "position:absolute; left: 0px; right: 0px; top: 0px; bottom:0px; visibility: visible;";
    else
        fe_div.style.cssText = "width:" + d_width + "px; height:" + d_height + "px; top:" + iTop + "px; left:" + iLeft + "px;";

    fe_div.innerHTML = vu;

    fe_div.style.visibility = "visible";


    HsDialog.m_div = fe_div;

    ShowCover(0.3); 
    
   

    $(".dlg_box").addClass("ui-corner-all has-shadow"); //.draggable(); //.resizable();




};


HsDialog.CloseDialog=function() {
    if (HsDialog.m_div == null)
        return;
        

    $(HsDialog.m_div).remove();
    //hide(HsDialog.m_div, true);
   
   HideCover(window);
   HsDialog.m_div = null;
    
   if (!hs.isEmpty(window.focus_id) && hs.e.isInView(window.focus_id))
        $("#" + window.focus_id).focus();

    dlg_callback && dlg_callback()
    dlg_callback = null;

    /*
    if (dlg_callback != null) {
        dlg_callback();
        dlg_callback = null;
    }
 */    
      
}

//------------------------

//---------------




// dlg self maximize
function MaximizeMe()
{
    
    
   // if (HsDialog.m_div == null) return;
    
    if (window.parent == window)
        return; // not dialog

    var max_but_e = $('#dlg_maxz', window.parent.document);
    if (!hs.e.isVisible(max_but_e))
        return;

    hs.msg.toast("Maximize me..");

    var o = GetElmInParent("dlg_box");

    o.style.cssText = "position:fixed; left:0;right:0;top:1px; width: 100%; height: 100%; visibility: visible;";
    
    var dlg_box_h = $(o).height() - 32;

    

    var f = GetElmInParent("dlg_frame");

    
    f.style.height = dlg_box_h + "px";
    
    $(max_but_e).hide();

    
    
 
}

var auto_maximize = false;

HsDialog.Maximize = function(topWindow)
{
    
   if (HsDialog.m_div==null) return;
   
   auto_maximize = true;

   var o = GetElm("dlg_box");

   o.style.cssText = "position:fixed; left:0;right:0;top:1px; width: 100%; height: 100%; visibility: visible;";

   var dlg_box_h = $("#dlg_box").height() - 32;

  // dlg_box_h = "100%";

   var f = GetElm("dlg_frame");
   
 //  window.alert(dlg_box_h);
   f.style.height = dlg_box_h + "px";
   // f.style.position = "static";
 
   

   HideElm('dlg_maxz');

};

HsDialog.Refresh = function () {
    if (HsDialog.m_div == null) return;

    
    var iFrame = $("#dlg_frame")[0];
    var src = iFrame.src;

    iFrame.src = '';
    iFrame.src = src;
    
    
   
}

var d_dlg = new HsDialog("hsApps"); // d

function ForceDlg(url, d_class, d_width, d_height)
{
   HsDialog.ShowDialog(url,d_class, d_width, d_height);
}

//-----

function RefreshPage()
{
    window.location.reload(true);
}

//----

function AutoCloseDlg(auto_refresh)
{
    if (window.parent == window)
        return; // not dialog

    hs.msg.toast("closing dlg..");

    if (arguments.length < 1)
        auto_refresh = true;

   window.setTimeout(function () {
       if ($("div.ui-warn, div.warn-msg").length == 0) { // close if no warn msgs

           window.parent.CloseDlg();

           if (auto_refresh)
            window.parent.hs.page.refresh(false,false);
       }
   }, 500);
   
}

   



// ******************************************** //
// ******** Ajax *******************************//



function createRequestObject()
{
    var request_;
    var browser = navigator.appName;
    if(browser == "Microsoft Internet Explorer"){
        request_ = new ActiveXObject("Microsoft.XMLHTTP");
    }
    else{
        request_ = new XMLHttpRequest();
    }
    return request_;
}

//---------------------
var http=createRequestObject();

function Asamii_Ajax(ajx_inst_id)
{
   this.inst_id=ajx_inst_id;
   this.request_id="";
   this.src_elem_id="";
   this.dst_div_id="";
   this.url="";
   this.result="";
   this.err_code="";
   this.is_error_result=false;
   this.action="";
}

//----

Asamii_Ajax.prototype.send=function()
{
   // window.alert(this.url);
    
    http.open('get',this.url);
    http.onreadystatechange = this.handleInfo;
    http.send(null); 
    
    //window.alert("ajax.send:end"); 
}

//----------------



Asamii_Ajax.prototype.handleInfo=function()
{
    //window.alert("ajax.handle_info:beg");
    
    if(http.readyState == 1)
    {
        //window.alert("ajax.handle_info:stat=1");
        process_ajax_progress(ajax);
        return;
    };
    
    if(http.readyState == 4)
    {
        //window.alert(http.responseText);  return;
               
        var response=http.responseText;
        ajax.is_error_result=response.charAt(0)=='*';
        
        ajax.result=response;
        process_ajax_result(ajax);     
       //window.show_ajax_result(ajax);
       //window.alert(this.result); 
       return;
    }
    
   
}

//--------------






//----
var ajax=new Asamii_Ajax("inline_imsg");






function do_ajax(url, dst_div_id, callback, e) {
    hs.trace('do_ajax:' + url);
    var al = arguments.length;

    if (al < 4) e = null; 

    hs.e.loading(e);

    $.ajax({
        url: url,
        cache: false
    })
        .done(function (result, textStatus, jqXHR) {
            var rc = 0;
            if (hs.ajax.isRejected(jqXHR)) {
                rc = 1; // err
                log('ajax-rejected: ' + url);
                hs.msg.toast(result);
            };

            

            if (dst_div_id != null)
                $("#" + dst_div_id).html(result);

            //  log('ajax.success before callback:' + url);

            if (typeof callback == "function")
                callback(result, rc);

            // log('ajax.success after callback:' + url);
        }).fail(function (jqXHR, textStatus, errorThrown) {
            var err_msg = hs.ajax.getErrMsg(jqXHR);

            hs.msg.toast(err_msg);

            if (typeof callback == "function") 
                callback(err_msg, 2); 
            
        }).always(function () {
            hs.e.loading(e, 0);
        });

}


function do_ajax_callout(url, width) {

    url = appendUrlParam(url, "ajax=y");
   // hs.msg.toast(url);
    if (arguments.length < 2) width = 400;
    do_ajax(url, null, function (result, rc) { if (rc == 0) hs.msg.callout(result, null, 0, null, width); });
}

function do_ajax_refresh(url) {

    url = appendUrlParam(url, "ajax=y");
    hs.msg.wait();
    do_ajax(url, null, function (result, rc) { if (rc == 0) hs.page.refresh(); else hs.msg.callout(result); hs.msg.endWait(); });
}

function do_ajax_remove(url, e) {


    if ($(e).attr("cfmd") != "y") {
        $(e).attr("cfmd", "y").addClass("light-shadow").css("background-color", "#cde").attr("title","حذف");
        hs.msg.toast("اضغط مرة أخرى لتأكيد الحذف");
        return;
    };

    url = appendUrlParam(url, "ajax=y");

    //hs.msg.toast(url,10);
   

    do_ajax(url, null, function (result, rc) { if (rc == 0) closeParent(e); else hs.msg.error(result); });
}

function do_ajax_add_content(url) {

    url = appendUrlParam(url, "ajax=y");

    //hs.msg.toast(url,10);


    do_ajax(url, null, function (res, rc) { if (rc == 0) hs.addContent(res); else hs.msg.error(res); });
}

// did not work well
function do_ajax_load_page(url) {

    url = appendUrlParam(url, "ajax=y");

    hs.msg.toast(url,10);


    do_ajax(url, null, function (res, rc) { $("#page").html(res); on_document_ready(); });
}

function process_ajax_progress(oAjx)
{
    if (oAjx.action=="show_in_div")
    {
       return; 
    };
        

}
//-------------

function process_ajax_result(oAjx)
{
    if (oAjx.action == "run-script") {

      //  window.alert(oAjx.result);

        eval(oAjx.result);
        
        return;
    };

    if (oAjx.action=="show_in_div")
    {
      var oDiv=GetElm(oAjx.dst_div_id);
      if (oDiv != null) 
      { 
        oDiv.innerHTML =oAjx.result; 

       // window.alert(oAjx.result);
      }
      else
        window.alert(oAjx.result); 
      
      
      return;
    };
    
    window.show_ajax_result(oAjx);

}
//-------------





//-----------

function AddElm(eId, eTag )
{
   var Elm=GetElm(eId);
   if (Elm != null) return Elm;
      
   Elm=document.createElement(eTag); 
   if (Elm==null) return null;
   
   Elm.id=eId;
   document.body.appendChild(Elm);
   return Elm;
}

function DelElm(eId)
{
    $('#' + eId).remove();
}

function delayDelElm(eId, delay_ms) {
    window.setTimeout("DelElm('" + eId + "')", delay_ms);
}





//---------------------

//---------

// show percent of the page
function ShowDlgP(pct, url, min_width, min_hight) {
    var page_size = getPageSize();

   

    pct = pct / 100;
    page_size[0] = page_size[0] * pct;
    page_size[3] = page_size[3] * pct;

    if (page_size[0] < min_width) page_size[0] = min_width;
    if (page_size[3] < min_hight) page_size[3] = min_hight;

    if (url.contains("dlg-auto-close=y"))
        dlg_callback = hs.page.refresh;

    ForceDlg(url, null, page_size[0], page_size[3]);
};

function ShowDlg(url) {

    // hs.msg.removeAll();

    


    if (window.top != window) {
        ShowDlgF(url);
        return;
    };
    
    var page_size = getPageSize();

    if ((page_size[0] <= 1000 || page_size[3] <= 500) || url.contains("dlg=F") || auto_maximize) {
        ShowDlgF(url);
        
        return;
    };

    ShowDlgP(90, url, 100, 100);
}


function ShowDlgB(url) { 
    ShowDlgP(95, url, 700, 500);
}

function ShowDlgM(url) {
    ShowDlgP(60, url, 600, 400);
}

function ShowDlgS(url) {
    ShowDlgP(40, url, 400, 300);
}

function ShowDlgF(url) {
    ShowDlgP(100, url, 100, 100);
   // HsDialog.Maximize(window);
}

function ShowDlgT(url) { // tools
    ShowDlgP(30, url, 710, 900);
}

function ShowDlgH(hight, url) {
    ForceDlg(url, null, 700, hight);
}

function ShowDlgW(url) {
    // hsWin.show(url, null);

    var winName = url;

    if (url.contains("cmd=add"))
        winName = null; // always open new window
    
    url = appendUrlParam(url, "sind=y");

    var win = window.open(url, winName, "scrollbars=yes,menubar=yes,fullscreen=yes,chrome=y");
    win.focus();
}

function appendUrlParam(url, param) {
    if (!url.contains("?"))
        url += "?";
    
    if (!url.contains(param))
        url += "&" + param;

    return url;
}

// dialog set its title
function SetDlgTitle(t) {
    var fm_tit$ = $("div.fm-header span.fm-title");
    if (hs.isEmpty(t))
        t = fm_tit$.html();

    if (!hs.isEmpty(t)) {
        var l = $("#dlg_title", parent.document.body).html(t).length;
        if (l > 0) // if dlg title is set then hide fm title
            fm_tit$.hide();
        hs.fm.title = t;
    }
}

// dialog close itself 
function CloseDlg() {
    var _e = GetElmInParent(dlg_parent_cover);
    if (_e != null)
        parent.document.body.removeChild(_e);

    _e = GetElmInParent(dlg_box_div_id);
    if (_e != null) {
        var focus_id = parent.focus_id;
        if (!hs.isEmpty(focus_id))
            $("#" + parent.focus_id, parent.document.body).focus(); // focus before remove
        parent.document.body.removeChild(_e);
    }
}

function CloseDlg_ShowParentMsg(m, tip_sel_e) {

    if (tip_sel_e == 'a.icon_refresh')
        m += K.hr + hs.link(hs.R.refresh, "js:DoSubmit('refresh')","link_icon icon_refresh");

    tip_sel_e = null;
    
    if ($(tip_sel_e, parent.document.body).length > 0) {
        hs.msg.tip($(tip_sel_e, parent.document.body), m, 30);
    }
    else
        hs.msg.boxAtParent(m);

    CloseDlg();
}

function delayCloseDlg(delay_ms) {
    window.setTimeout("CloseDlg()", delay_ms);
}


function CloseTopDlg() {
 
    if (hs.isInEditMode())
        return;

    if (HsDialog.m_div != null)
        HsDialog.CloseDialog();
    else
        CloseDlg();
}


function go(url, tgt, force_wait_msg) {
    if (arguments.length < 2) tgt = "";
    if (arguments.length < 3) force_wait_msg = !1;
    hs.fm.allowToLeave();

    hideConfirmBox();

    if (tgt == "itemdata")
        hs.ui.setFrameUrl(tgt, url);
    else {
        if (force_wait_msg || (!hs.fm.isMobile && hs.fm.hasUiFlag(0x0010)))  // suppress go-back
            hs.msg.wait(true);

        setTimeout(function () { document.location.href = url; }, 100);
    }
}

function hideConfirmBox() {

    DelElm("confirm_box");
    DelElm("confirm_box0");


}

// 2015.09.03 added

function ConfirmUrl(url, msg, tgt) { // must be void
    var html;
    var e;

    if (arguments.length < 2)
        msg = 'هل أنت متأكد؟';

    if (arguments.length < 3 || tgt==null)
        tgt = '';

    if (url.startsWith("js:")) url = url.replace("js:", JS);

    if (url.startsWith(JS))
    {
        url = url.replace(JS, "");
        url = JS + "hs.msg.removeAll();" + url + ";hs.fm.allowToLeave();";
    }
    else
        url = JS + 'go("' + url + '","'+ tgt +'")';

    html = "<center>" + msg;
    html += "<br/><hr/><a id='box-yes' style='width: 80px' class='link_cmd icon_yes' href='" + url + "'>" + hs.L("Yes", "نعم") + "</a>";
    html += "&nbsp;&nbsp;&nbsp;<a id='' style='width: 80px' class='link_cmd icon_no' href='javascript:hideConfirmBox();'>" + hs.L("No", "لا") + "</a><br/></center>";

    // e = AddElm('confirm_box', 'DIV');
    // e.innerHTML = html;

    $("#confirm_box0").remove();

    hs.msg.box(html, "confirm_box0");

    $("#box-yes").focus();
}

function scrollToHash(hash) {
    location.hash = "#" + hash;
    focus_first();
}


function OpenInPopupFrame(parent_div_id, url) // static
{
    

    var org_url = url;
    url = sind(url);
                

    //window.alert("wel");

    var dlg_title = ""; // "HowbaniSoft";
    var vu;
    var fe_div;

    vu = "";
    vu += "<iframe id='dlg_frame' width='100%' height='100%' frameborder='0' scrolling='auto' marginwidth='0' src='" + url + "'></iframe>";


    var parent_div = document.getElementById(parent_div_id);

    if (parent_div == null) {
        window.alert("parent div is null");
        return;
    }
   
    parent_div.innerHTML =  vu;
    
    window.alert(vu);
};

function decodeURI(u) {
    try
    {
        u = decodeURIComponent(u);
    }
    catch (e) {hs.logEx(e) }

    return u;
}

function getUrlParameter(sParam) {
    var sPageURL = decodeURI(window.location.search.substring(1)),
        sURLVariables = sPageURL.split('&'),
        sParameterName,
        i;

    for (i = 0; i < sURLVariables.length; i++) {
        sParameterName = sURLVariables[i].split('=');

        if (sParameterName[0] === sParam) {
            return sParameterName[1] === undefined ? true : sParameterName[1];
        }
    }
};



// the table must have th header tag
function hideTableCol_ORG(col) {
    $('.rep_tab td:nth-child(' + col + '),th:nth-child( ' + col + ')').hide();
};

function hideTableCol(th_col, td_col, tab_id) {
    if (th_col < 0)
        hs.msg.toast('not supported..');
    else
        $('#' + tab_id + '.rep_tab:not(.rep-title) td:nth-child(' + td_col + '), #' + tab_id + '.rep_tab th:nth-child( ' + th_col + ')').hide();
      //  $('.rep_tab:not(.rep-title) td:nth-child(' + td_col + '),th:nth-child( ' + th_col + ')').hide();
};





function fill_dropdown_list(list_id, theOptions, reset_val) {

    if (arguments.length < 3)
        reset_val = true;

    var saved_val = $("#" + list_id).val();

    var theList = $("#" + list_id).removeClass("loading").empty();

    if (theOptions == null)
        return;

    $.each(theOptions, function (val, text) {
        theList.append(
            $('<option />').val(val).html(text)
        );
    });

    if (reset_val)
        $(theList).val('');
    else
        $(theList).val(saved_val);

};



function log(s, level) {
    if (arguments.length < 2)
        level = 0;
    if (level <= hs.log_level) {
        var d = new Date();
        s = "{1}:{2}:{3}.{4} => {0}".format(s, d.getHours(), d.getMinutes(), d.getSeconds(), d.getMilliseconds());
        if (console && console.log)
            console.log(s);

        hs.logs += s + K.br;
    }
}

function cascade_coding(pa_id, ch_id, ch_coding_type) {
    var the_url = "/app/ajax/?f=get-child-codes";
    var pa_val = $("#" + pa_id).val();
    
  //  window.alert(the_url);

    if (pa_val.length < 1) {
        fill_dropdown_list(ch_id, { '': '' });
        return;
    };

    the_url = the_url + "&pa=" + pa_val;
    the_url = the_url + "&coding=" + ch_coding_type;
    the_url = the_url + "&ch_id=" + ch_id;

    $("#" + ch_id).addClass("loading");

    log(the_url);
        
    hs.ajax.get(the_url, null);
}

function XOR(a, b) { return a != b; }


function isChecked(a) {
    
    if ("string" != typeof a)
        return $(a).prop('checked');
    else
        return $("#" + a).prop('checked');
}

function setFocus(e_id) {
    if (e_id != null) 
        try { $("#" + e_id).focus(); } catch (e) { };
}

function show_element(e_id, show) {
    if (!hs.isEmpty(e_id)) {
        if (show)
            ShowElm(e_id);
        else
            SilentHideElm(e_id);
    }
}

function show_elements(csv_target_ids, is_show) {

    var target_ids_arr = csv_target_ids.split(';');
    var i;

    for (i = 0; i < target_ids_arr.length; i++) {

        show_element(target_ids_arr[i], is_show);
    }
}

function batch_check_all(chk_all_obj) {
    if ($(chk_all_obj).prop('checked'))
        $("input.batch").prop('checked', 'checked'); // select all
    else
        $("input.batch").prop('checked', null); // clear all
}

function do_check_all(chk_all_obj, data_id) {
    if ($(chk_all_obj).prop('checked'))
        $(".chk-list[data-id='" + data_id + "'] input[type='checkbox']").prop('checked', 'checked'); // select all
    else
        $(".chk-list[data-id='" + data_id + "'] input[type='checkbox']").prop('checked', null); // clear all

    do_show_selected_only(false, data_id);
}

// show/hide check box along with its label
function show_check_box(obj, show) {
    if (show) {
        $(obj).show();
        $("label[for='" + $(obj).prop("id") + "']").show();
        $("span[id='cb-" + $(obj).prop("id") + "']").show();
    }
    else {
        $(obj).hide();
        $("label[for='" + $(obj).prop("id") + "']").hide();
        $("span[id='cb-" + $(obj).prop("id") + "']").hide();
    };
}

function do_show_selected_only(a, data_id) {

    var show_selected_only = ("boolean" == typeof a ? a : isChecked(a));
       
    if (show_selected_only)
        $(".chk-list[data-id='" + data_id + "'] input[type='checkbox']").each(function () { if ($(this).prop('checked')) show_check_box(this, true); else show_check_box(this, false); });
    else
        $(".chk-list[data-id='" + data_id + "'] input[type='checkbox']").each(function () { show_check_box(this, true); });
}

function show_on_checked(chk_box_id, csv_target_ids, focus_id, is_show) {

    var target_ids_arr = csv_target_ids.split(';');
    var i;

    for (i = 0; i < target_ids_arr.length; i++) {
        show_element(target_ids_arr[i], !(XOR(is_show, $("#" + chk_box_id).prop('checked'))));
    }
}



function set_show_on_checked(chk_box_id, csv_target_ids, focus_id, is_show) {

    $("#" + chk_box_id).attr("onchange", "show_on_checked('" + chk_box_id + "','" + csv_target_ids + "','" + focus_id + "'," + is_show + ")");

    show_on_checked(chk_box_id, csv_target_ids, focus_id, is_show)
}




var tmrFocus = null;
// show_on_list_value('act_type','1','r_edu_subj_id;r_cov_no;r_;','r_cov_no;','r_cov_no;',true);
function show_on_list_value(list_box_id, target_list_val, csv_all_ids, csv_target_ids, focus_id, is_show) {

    var target_ids_arr = csv_target_ids.split(';');
    var sel_val = $("#" + list_box_id).val();
    var i;
    
   
    

    if (hs.fm.isFind() && !hs.fm.isActivity) { // 18.8.2019: in find mode, show all flds
        is_show = false;
        sel_val = null;
    };

   
    
    
    for (i = 0; i < target_ids_arr.length; i++) {
        show_element(target_ids_arr[i], !(XOR(is_show, (sel_val == target_list_val))));
    }

    return; // 15.3.2019 - no focus next
  /*

    if (is_page_loaded) {

        if (!isIE())
            focus_next(GetElm(list_box_id));

        window.clearTimeout(tmrFocus);
        tmrFocus = window.setTimeout("focus_next(GetElm('" + list_box_id + "'))", 100);
    };
*/
       
}

var tmrIdSuggest;






function hide_suggest_element(div_id) {
    var div_e = GetElm(div_id);

    if (div_e != null) {
        div_e.style.display = "none";
        div_e.style.visibility = "hidden";
        return;
    }
}

function create_suggest_element(txtbox_id, div_id, in_scrollable_container, max_height) {

    if (arguments.length < 4)
        max_height = 0; // use default

    if (max_height < 200 || max_height > 800)
        max_height = 200;

    if (div_id == null)
        div_id = txtbox_id + "_suggest";

    var div_e = GetElm(div_id);
    var txt_e = GetElm(txtbox_id);

    if (div_e != null) {
        div_e.style.display = "block";
        div_e.style.visibility = "visible";
        return div_e;
    }

    div_e = document.createElement('DIV');
    

    if (div_e != null) {
        div_e.id = div_id;

        var rect = txt_e.getBoundingClientRect();
       
        var top = rect.bottom + 0;
        var width = rect.width;// + 20;
        var left = rect.left;
        var style = '';
        var diff = 0;

        if (width < 400) {
            diff =  400 - width;
            width = 400;

            if (hs.isRTL())
                left = left - diff;
        };
          
                
        
       
        // document.body.appendChild(div_e);

        // $(div_e).css('margin-top', '-2px');

        $(div_e)
            .addClass('suggest-box inline-menu ui-corner-all has-shadow hide-on-esc')
            .insertAfter('#' + txtbox_id)
            .css({ width: width })
            .css("max-height", max_height.toString() +"px")  
        ;

        


        if (in_scrollable_container)
            $(div_e).css({ position: 'fixed', top: top })
        else
            $(div_e).css({ position: 'absolute' });


        

              

        
    };

   
}

function fix_suggest_element(objSugBox, under_id) {

    var objParent = GetElm(under_id);

    if (objParent == null || objSugBox == null)
        return;

    var rect = objParent.getBoundingClientRect();

    var top = rect.bottom + 0;
   
    h$(objSugBox).css({
        position: 'fixed',
        top: top
    });
}


function get_form_ajax_suggest(fm_id, fld_name, res_div) {

    

    var the_val = $("#" + fld_name).val();

    if (fld_name.charAt(0) == '_')
        fld_name = fld_name.substr(1);

    var fm_mode = hs.fm.getMode();
    if (!fm_id.contains("fm-mode") && !hs.isEmpty(fm_mode))
        fm_id += "&fm-mode=" + fm_mode;

    var the_url = "/app/ajax/?f=get-fm-suggest&fm=" + fm_id + "&fld=" + fld_name + "&val=" + escape(the_val);
    

    if (the_val.length == 0) {
        hide_suggest_element(res_div);
        return;
    };

    do_ajax(the_url, res_div, null);

}


function form_ajax_suggest(event, fm_id, fld_name, res_div, ms_wait, showInAdd) {

    //   window.alert("hi"); 
   

    var fm_cmd = hs.fm.getCmd();

    if ((fm_cmd == "add" && !showInAdd) || fm_cmd == "edit")
        return false;

    if (typeof event == "undefined") { event = window.event; }
    if (event == null) {
        //   window.alert("null event")
        return true;
    }


    create_suggest_element(fld_name, res_div, false);


    if (event.keyCode == 10 || event.keyCode == 13) {

        event.cancelBubble = true;


        return false;
    };

    window.clearTimeout(tmrIdSuggest);
    tmrIdSuggest = window.setTimeout("get_form_ajax_suggest('" + fm_id + "', '" + fld_name + "', '" + res_div + "')", ms_wait);


    return true;
    //getInfo(t);

};


function get_form_ajax_quick_find(fm_id, fld_name, res_div, out_fmt, match_type)
{
    var the_val = null;
    
    if (!hs.isEmpty(fld_name))
        the_val = $("#" + fld_name).val();

    if (fld_name.charAt(0) == '_')
        fld_name = fld_name.substr(1);

    var fm_mode = hs.fm.getMode();
    if (!fm_id.contains("fm-mode") && !hs.isEmpty(fm_mode))
        fm_id += "&fm-mode=" + fm_mode;


    var the_url = "/app/ajax/?f=get-fm-quick-find&fm=" + fm_id + "&fld=" + fld_name + "&val=" + escape(the_val) + "&out-fmt=" + out_fmt + "&match-type=" + match_type;
       

    if (hs.isEmpty(the_val)) {
       // hide_suggest_element(res_div);
        return;
    };
        

    do_ajax(the_url, res_div, onIndexListUpdated);

}

function form_ajax_qiuck_find(event, fm_id, fld_name, res_div, out_fmt, match_type, ms_wait) {

     // window.alert("hi"); 

    var fm_cmd = hs.fm.getCmd();

    if (fm_cmd == "add" || fm_cmd == "edit" )
        return false;

    if (typeof event == "undefined") { event = window.event; }
    if (event == null) {
        //   window.alert("null event")
        return true;
    }


    

    if (event.keyCode == 40 || event.keyCode == 39) {
        nextIndexListItem(1);
        return true;
    }

    if (event.keyCode == 38 || event.keyCode == 37) {
        nextIndexListItem(-1);
        return true;
    }


    if (event.keyCode == 10 || event.keyCode == 13) {

        hs.absorb(event);

        window.clearTimeout(tmrIdSuggest);
        get_form_ajax_quick_find(fm_id, fld_name, res_div, out_fmt, 0); // contains

        return false;
    };

    window.clearTimeout(tmrIdSuggest);
    tmrIdSuggest = window.setTimeout("get_form_ajax_quick_find('" + fm_id + "', '" + fld_name + "', '" + res_div + "', '" + out_fmt + "'," + match_type +")", ms_wait);


    return true;
    //getInfo(t);

};

function on_enter_form_ajax_qiuck_find(event, fm_id, fld_name, res_div, out_fmt, match_type, ms_wait) {

    var fm_cmd = hs.fm.getCmd();

    if (fm_cmd == "add" || fm_cmd == "edit")
        return false;

    if (typeof event == "undefined") { event = window.event; }

    if (event == null) {
        return true;
    };
    
    if (event.keyCode == 10 || event.keyCode == 13) {

        hs.absorb(event);

        get_form_ajax_quick_find(fm_id, fld_name, res_div, out_fmt, match_type); // contains

        return false;
    };

    return true;
};

// -----------------

function colorLuminance(hex, lum) { // make the color lighter or darker=> 20% lighter: ColorLuminance("#6699CC", 0.2); 50% darker: ColorLuminance("#6699CC", -0.5);	

    // validate hex string
    hex = String(hex).replace(/[^0-9a-f]/gi, '');
    if (hex.length < 6) {
        hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
    }
    lum = lum || 0;

    // convert to decimal and change luminosity
    var rgb = "#", c, i;
    for (i = 0; i < 3; i++) {
        c = parseInt(hex.substr(i * 2, 2), 16);
        c = Math.round(Math.min(Math.max(0, c + (c * lum)), 255)).toString(16);
        rgb += ("00" + c).substr(c.length);
    }

    return rgb;
}


function imageResize(obj, maxWidth, maxHeight) { // resize image keeping the aspect ratio
    

    if (obj == null)
        return;

    obj = h$(obj);

    var ratio = 0;  // aspect ratio
    var width = $(obj).width();    
    var height = $(obj).height();  

   // hs.msg.toast("riszie img w=" + width +" h=" + height );
    
    if (width > maxWidth) {
        ratio = maxWidth / width;   
        $(obj).css("width", maxWidth); 
        $(obj).css("height", height * ratio);  
        height = height * ratio;    
        width = width * ratio;

        
    }
        
    if (height > maxHeight) {
        ratio = maxHeight / height; 
        $(obj).css("height", maxHeight);   
        $(obj).css("width", width * ratio);    
        width = width * ratio;    
        height = height * ratio;

       
    }
}

// add sind=y (show in dlg) to the url, instructing the server to not send page hdr/menu/footer
function sind(url) {
    return hs.safe.appendUrlParam(url, "sind", "y");
}


function ShowInFrame(frm_id, url) {
    var frm = h$(frm_id);
    url = sind(url);
    if ($(frm).attr("src") == "") {

        DelElm('dlg_loading_img');
        
        $("<img id='dlg_loading_img' class='mem_icon dlg_loading' src='/app/s/images/loading.gif' />").insertBefore(frm);
       
        $(frm).attr("onload", "hs.ui.busy(false)").attr("src", url);

    };
}

// -------------------

function initGrid() {
    $(":input").on("change", function () { $(this).addClass("ui-changed"); });
}

function gridSave() {
   
}

function ajax_DoSubmit2(subId, subParam) {

    var al = arguments.length;

    if (al < 1) subId = '';
    if (al < 2) subParam = '';

    $("#_submitId_").val(subId);
    $("#_submitParam_").val(subParam);
        


    var _data = hs.fm.serialize();


    hs.msg.removeAll();

   // hs.msg.wait(true);



    var _url = window.location.href + "&ajax=y";

    var jqxhr = $.ajax({
        type: "POST",
        url: _url,
        data: _data

    })
        .done(function (result, textStatus, jqXHR) {
            act("ajax: done: " + result);
            $("#fm_results").html(result);
        })
        .fail(function (jqXHR, textStatus, errorThrown) {

            var err_msg = '';

            if (jqXHR.status == 0) //
                err_msg = hs.R.conn_err;
            else
                err_msg = jqXHR.statusText;

            hs.msg.error(err_msg);

        })
        .always(function () {
            hs.msg.wait(false);

            hsEasyForm.adjustLinks();
        });
}


function ajax_DoSubmit(subId, subParam) {
    $("#_submitId_").val(subId);
    $("#_submitParam_").val(subParam);

    if (hs.fm.isOffline()) {
        offline_print_pos();
        hs.fm.clearCurrentOfflineForm(); // avoid duplicating
        hs.offline.save(true, false);
        return;
    };

    

    var _data = hs.fm.serialize();
        

    hs.msg.removeAll();

    hs.msg.wait(true);

 

     var _url = window.location.href + "&ajax=y";

    var jqxhr = $.ajax({
        type: "POST",
        url: _url,
        data: _data
       
    })
        .done(function (result, textStatus, jqXHR) {
            act("ajax: done..");
            hs.eval(result); // execute the result as script
            if (!hs.ajax.isRejected(jqXHR))
                hs.fm.clearCurrentOfflineForm();
        })
        .fail(function (jqXHR, textStatus, errorThrown) {

            var err_msg='';

            if (jqXHR.status == 0) //
                err_msg = hs.R.conn_err;
            else
                err_msg = jqXHR.statusText;

            err_msg += "<hr/><a class='link_icon icon_save' href='javascript:hs.offline.save(true, true)'>" + hs.R.switch_offline + "</a>";

            hs.msg.error(err_msg);
                
        })
        .always(function () {
            hs.msg.wait(false);

           hsEasyForm.adjustLinks();
        });
}


function json_DoSubmit(subId, subParam) {

    var page_params = ''; // to be added to url

    if (!hs.isEmpty(subId))
        page_params += "&_submitId_=" + subId;

    if (!hs.isEmpty(subParam))
        page_params += "&_submitParam_=" + subParam;
        
    
    enableSubmitDisabled(true);

    var _data = hs.fm.serializeToJSON();

    
   

    hs.msg.removeAll();

    hs.msg.wait(true);

    
    var _url = window.location.href + page_params; // + "&ajax=y";

    log("Submit url:" + _url);
    log("Submit json data:" + _data);

  
    var jqxhr = $.ajax({
        type: "POST",
        url: _url,
        data: _data,
        dataType: "text", // as json is stringfied
        contentType: 'application/json'
    })
        .done(function (result, textStatus, jqXHR) {
            act("json: done..");
           
            if (result != null) {
                act("json response: " + result);
                var res_obj = hs.parseJson(result);
                console.log(res_obj);
                if (res_obj != null) {
                    var fm_rc = res_obj.fm_rc || '';
                    var fm_msg = res_obj.fm_msg || '';
                    var fm_res = res_obj.fm_res || '';

                    if (fm_rc == "0") { // success
                        hs.msg.success(hs.isEmpty(fm_msg) ? hs.R.success : fm_msg);
                        if (hs.fm.isAdd())
                            hs.fm.reset();
                    }
                    else
                    if (fm_rc == "2") { // requires confirmation
                        fm_msg = hs.R.warnings + K.hr + fm_msg + K.hr;
                        fm_msg += hs.link(hs.L("Continue anyway... ", "متابعة الحفظ... "), "javascript:DoSubmit('','ignore-warns')", "link_cmd cmd icon_ok auto-focus");
                        hs.msg.error(fm_msg);
                    }
                    else {
                        fm_msg = (hs.isEmpty(fm_msg) ? hs.R.failed : fm_msg);
                        fm_msg += "<hr/>Err Code: " + fm_rc;
                        hs.msg.error(fm_msg);
                    }
                    
                    $("#fm_results").html(fm_res);
                    hsEasyForm.onFormUpdated();

                    result = hs.ui.code(result);
                    hs.msg.callout(result); // comment me

                } else
                    hs.msg.callout(result);
            } else
                hs.msg.error("blank response, statusText:" + textStatus);

           // if (!hs.ajax.isRejected(jqXHR))
             //   hs.fm.clearCurrentOfflineForm();

        }) // done
        .fail(function (jqXHR, textStatus, errorThrown) {
            act("json: fail..");
            var err_msg = '';

            if (jqXHR.status == 0) //
                err_msg = hs.R.conn_err;
            else
                err_msg = jqXHR.status.toString() + jqXHR.statusText;

          //  err_msg += "<hr/><a class='link_icon icon_save' href='javascript:hs.offline.save(true, true)'>" + hs.R.switch_offline + "</a>";

            hs.msg.error(err_msg);

        })
        .always(function () {
            hs.msg.wait(false);
        });
}

// Form.Attributes.Add("onsubmit", "return onFormSubmit()");

function onFormSubmit() {
    enableSubmitDisabled(true);
    return true;
}

function enableSubmitDisabled(en) {
    if (en)
        $('.disabled, input[type="checkbox"]:disabled').prop('disabled', false); // enable lists/checkboxes to be submitted
    else
        $('.disabled').prop('disabled', true);
}

function DoSubmit_AfterXX(subId, subParam) {

    window.setTimeout(function () { ActualDoSubmit(subId, subParam); }, 1000);

}


function DoSubmit(subId, subParam) {
    
    if (submitted) {
        hs.msg.toast(hs.R.wait + " Already submitted", 15);
        return;
    }

    var al = arguments.length;

    if (al < 1) subId = '';
    if (al < 2) subParam = '';

    if (subId == "grid-save" && hs.fm.getCmdMode()=="grid-add") {
        ajax_DoSubmit(subId, subParam);
        return;
    }
    
    act();
    act("do submit..");
    if (!(subId == "refresh" || subId == "chkinput" || subId == "copyitems")) { // refresh client cached data & drop list content

        if (subParam != 'ignore-warns' && hs.fm.onSubmit != null && !hs.fm.onSubmit())
            return;
        
        if (hs.fm.hasFlag(hs.fm.Flags.EasyEntry) && hs.fm.isAdd()) {
            ajax_DoSubmit(subId, subParam);
            act("submitted via ajax..");
            return;
        };


        if (hs.fm.hasUiFlag(0x0400)) { // json posting enabled
            if (hs.fm.isAdd()) { // edit works - but needs improvements after saving
                json_DoSubmit(subId, subParam);
                act("submitted via json..");
                return;
            }
        }

    };

   

    hs.msg.removeAll();

   
    if (subId != "export-pdf") {
        submitted = true;
        hs.msg.wait(true);
        
        if ((hs.isInEditMode() || hs.fm.isActivity)) 
            $("#_sent_").val("1"); 
    }
    else
        hs.msg.toast(hs.R.wait + ", " + hs.R.find_dld_file, 30, !0);

    $("#_submitId_").val(subId);
    $("#_submitParam_").val(subParam);

   

    enableSubmitDisabled(true);

    
    hs.fm.allowToLeave();

    document.forms[0].submit();

    
       
}

// --------------------------



function fitFrameHeighToContent(f) {

   // act("fitFrameHeighToContent");

    var DEF_FRAME_HEIGHT = 400;

    var h = DEF_FRAME_HEIGHT;

    var f_page = f.contentWindow.document.getElementById("page");
    
    if (f_page != null)
        h = f_page.scrollHeight;

    var isPrint = getUrlParameter("printable");

    if (h < DEF_FRAME_HEIGHT && isPrint != "yes")
        h = DEF_FRAME_HEIGHT;

    h = h + 20;

    f.style.height = h + 'px';
}


// the below script make the fm-frames to be fit to the content without scroll, in plain and accordion ui


function adjustFormFramesHeight() {
    var iFrames = $('.fm-frame'); // this will auto-fit frames in all ui

    if ($.support.safari || $.support.opera) {
        iFrames.on("load",function () {
            setTimeout(fitAllFramesHeight, 0);
        });
    }
    else {
        iFrames.on("load",function () {
            fitFrameHeighToContent(this);
        });
    }
}

// **** Easy Entry **** //

function try_focus(e) {
    if (($(e).is(":hidden, :disabled, [readonly='readonly']"))
        || ($(e).attr("tabindex") == "-1")
        || (!$(e).is(":input, a"))) return false;

    var vlen = hs.len($(e).val());

    if (vlen > 0 && $(e).is("input[type='text']")) {
        $(e).focus().select();
      //  hs.e.getElem(e).setSelectionRange(vlen, vlen);
    }
    else
        $(e).focus().select();

    
    return true;
}

function set_focus_after(i) {
   
    var ii = i;

    if (flds_order == null) 
        return;
       
    while (++i < flds_order.length) {
        if (try_focus($('#' + flds_order[i])))
            return;
    };

   

    if ((ii > 0) && isAutoFormSubmit) {
        if (hs.isInEditMode()) 
            ConfirmUrl("javascript:DoSubmit()", hs.L("Save", "حفظ"));
        else
            DoSubmit();
    }
}


// ----------------

function set_focus_before(i) {

    var ii = i;

    if (flds_order == null)
        return;


    while (--i >= 0) {
        if (try_focus($('#' + flds_order[i])))
            return;
    };
}

function focus_prev(e) {

    if (e == null || flds_order == null)
        return;

    e = h$(e);


    var e_id = h$(e).attr("id");
    var i;


    for (i = 0; i < flds_order.length; i++) {
        if (flds_order[i] == e_id)
            break;
    };

    set_focus_before(i);

}

function focus_first() {
    

    var auto_focus = $(".auto-focus:first").focus().length;
    if (auto_focus != 0)
        return;
    
    var has_invalid_value_count = $(".has-invalid-value:first").focus().length;

    if (has_invalid_value_count == 0)
        set_focus_after(-1); // focus the first control as per the order list
}


function focus_next(e, chk_required) {

    if (e == null || flds_order == null)
        return;

    if (arguments.length < 2)
        chk_required = true;


    e = h$(e);

    if (chk_required) {
        if ($(e).is('.ui-date')) {
            var val = $(e).val();
            var l = hs.len(val);
            if (l > 0 && l < 6) {
                if (val.count('/') < 2) {
                    val = (val + "/").replace("//", "/");
                    $(e).val(val);
                }
                return;
            }
        }

        if ((hs.isInEditMode() || hs.fm.isActivity)) {
            var ml = h$(e).minLen();
            if (ml > -1) {
                var vl = e.is(".num") ? hs.safe.parseFloat(e.val()) : h$(e).val().length;
                var msg = ml > vl ? (vl <= 0 ? hs.R.fld_is_required : hs.R.incorrect_value + K.hr + hs.L("Min Value", "الحد الأدنى") + ": " + ml) : null;
                if (msg != null) {
                    hs.msg.tip(e, msg);
                    return;
                }
                else
                    $("#tip-msg").remove();

            }
        }
    }
   


    var e_id = h$(e).attr("id");
    var i;
        

    for (i = 0; i < flds_order.length; i++) {
        if (flds_order[i] == e_id)
            break;
    };

    set_focus_after(i);

}

function focus_left(e) {
    if (hs.isRTL())
        focus_next(e, false);
    else
        focus_prev(e);
}

function focus_right(e) {
    if (hs.isRTL())
        focus_prev(e);
    else
        focus_next(e, false);
}

function can_focus_right_or_left(e, ev, is_left) {

    if (!en_arrows)
        return false;

    if ($(e).is("select, input[type='checkbox']")) // || (ev.shiftKey && $(e).is("input, select, textarea")))
        return true;


    if ($(e).is("input[type='text']")) {
        var vlen = $(e).val().length;
        if (vlen == 0)
            return true;

        var elm = hs.e.getElem(e);
        var sel_start = elm.selectionStart, sel_end = elm.selectionEnd;

        hs.msg.toast("len=" + vlen + " sel= " + sel_start + " - " + sel_end, 5, true);

        if (is_left && sel_start == 0 && sel_end == 0)
            return true;

        if (!is_left && sel_start == vlen && sel_end == vlen)
            return true;

    }; 

    return false;
}

function focus_replace(e_id, new_id) {

    if (flds_order == null)
        return;

    hs.e.enable_focus_next(new_id);

    for (var i = 0; i < flds_order.length; i++) {
        if (flds_order[i] == e_id) {
            flds_order[i] = new_id;
            break;
        };
    };
}

function focus_insert(e_id, new_id, before) {

    if (flds_order == null)
        return;

    hs.e.enable_focus_next(new_id);

    var new_flds_order = new Array();
    var j = 0;

    for (var i = 0; i < flds_order.length; i++) {
        if (before && flds_order[i] == e_id)
            new_flds_order[j++] = new_id;

        new_flds_order[j++] = flds_order[i];

        if (!before && flds_order[i] == e_id)
            new_flds_order[j++] = new_id;
    };

    flds_order = new_flds_order;
}

// ----


function on_escape(obj) {
    
    var popup_msgs = $("#full_screen_cover"); 

    if (popup_msgs.length != 0) {
        $("#notify_box").remove();
        popup_msgs.remove();
        return;
    };

    $(".remove-on-esc").remove(); // on esc only

    hs.msg.removeAll();

    $(".popup-content-no-hover").hide();
    
    CloseTopDlg();

    // works - needs more test
    if (window.opener && window.opener != null) {
        var cmd = hs.fm.getCmd();
        if (cmd=='print' || cmd=='find')
            window.close();
    };
}


function add_list_tools() {
    $("table.child-items-list tr td.seq")
        .on("dblclick", function () {
            $(this).parent().toggleClass("hs-selected");
        })
        ;
}



function sticky_table_header() {
    add_list_tools();

    if (hs.fm.isPrint() || hs.fm.isMobile)
        return;

          
    var tbl = $("table.child-items-list.fix-head");

    if (!hs.e.isVisible(tbl))
        return; // did not work well if hidden
   
    
    tbl.find("thead th, tbody tr:first.item td, tfoot td").each(function () {
        var th = $(this);
        var width = th.width() + 8;
        th.css("width", width + "px");
    });
    

    tbl.find("thead").addClass("item-list-hdr");
    tbl.find("tbody").addClass("item-list-body");
    tbl.find("tfoot").addClass("item-list-foot");

    tbl.css("background-color", "#f8f8f8").css("box-shadow", "0px 2px 4px 0px rgba(0, 0, 0, 0.16), 0px 2px 10px 0px rgba(0, 0, 0, 0.12)");
};


var act_last_tick = 0;

function act(s) {
    
    var d = new Date();
    var t=d.getTime();
    var act_dur_ms = t - act_last_tick;
    act_last_tick = t;

    if (arguments.length < 1)
        s = null;

    if (s != null)
        log("[{0} ms] {1}".format(act_dur_ms,s));
        
};

function hide_page_header() {

    
    
        $("body").css("margin", "0px");
        $(".hide_on_child, .page_footer, .page_header").hide();
        $("div.full-screen-box").css("top", "0").css("bottom", "0");
       // $("#page").css("overflow-y", "auto"); // destory stick tab hdr
    
    
    
}


function useArabicDigits() {
    var map = ["&\#1776;", "&\#1777;", "&\#1778;", "&\#1779;", "&\#1780;", "&\#1781;", "&\#1782;", "&\#1783;", "&\#1784;", "&\#1785;"]
    document.body.innerHTML = document.body.innerHTML.replace(/\d(?=[^<>]*(<|$))/g, function ($0) { return map[$0] });
}


function disableBack() {
    window.history.forward()
}


function on_document_ready() {


    $(".pwd").on("keyup", function (event) {
       
        if (event.getModifierState("CapsLock"))
            hs.msg.toast("hala");
            // hs.msg.tip(this, "CapsLock");

    });
    

    /* // did not work well in IE, not tested yet
    window.addEventListener("beforeunload", function (event) {

        if (hs.isEntryProtected() && is_dirty) {
            hs.msg.warn("لم يتم حفظ التعديلات");
            // Cancel the event as stated by the standard.
            event.preventDefault();
            // Chrome requires returnValue to be set.
            event.returnValue = '';
        };
    });
    */


   




    if (!hs.fm.isMobile && hs.fm.hasUiFlag(0x0010)) { 
        window.addEventListener("beforeunload", function (event) {
            if (hs.isEntryProtected() && is_dirty) {
                hs.msg.warn("لم يتم حفظ التعديلات");
                // Cancel the event as stated by the standard.
                event.preventDefault();
                // Chrome requires returnValue to be set.
                event.returnValue = '';
            };


        });
    }

       

    act(); // 
    act("doc init..");
       
    hs.nav.detect();
       
    act("nav detect");

    window.focus_id = null; // last focused id 


    

    if (hsUiLang == "ar") {
        hs.R = arRes;
    }
    else
        if (hsUiLang == "fr")
            hs.R = frRes;
        else
            hs.R = enRes;

   

    if (!hs.cache.isAvailable())
      log("Local storage is not supported");

    try {
        if (hs.fm.onCaching)
            hs.fm.onCaching();
    }
    catch (ex) {
        hs.logEx(ex);
    }
    
    act("hs.fm.onCaching");

   

    try
    {
        if (hs.fm.onLoad)
            hs.fm.onLoad();
    }
    catch (ex) {
        hs.logEx(ex);
    }

    act("hs.fm.onLoad");
     
  
    $('.disabled').prop('disabled', true);

    if ((req_js_ver > js_ver) && (window.top == window))
        hs.msg.warn(hs.R.refresh_warn);
  
    if (window != window.top)
        hide_page_header();
    
    initGrid();

    act("initGrid");

    $("A.popup-box").attr("tabindex", "-1");

    $("div.side-panel").resizable({
        handles: "s, w",
        maxWidth: 600,
        minWidth: 300,
        ghost: false,

        resize: function (event, ui) {
            if (hs.isRTL())
                $(".fm-browse-item").css("right", ui.size.width);
            else
                $(".fm-browse-item").css("left", ui.size.width);
        }
    });

    act("div.side-panel");

    
    var fm_cmd = hs.fm.getCmd();

    

    // FS
    $('.flds, .lst')
        .on('focus', function () {$(this).addClass("focus").removeClass('has-suggested-value');})
        .on('blur', function () { $(this).removeClass('focus'); })
        .on('change', function () { hs.fm.dirty(true); })
    ;

    act(".flds, .lst");
    
   
    



   
   
   hsEasyForm.init();
   act("hsEasyForm.init");
    
   
   adjustFormFramesHeight();
     

    $(".chk-list-sortable").sortable({
        items: "li:not(.no-sort)",
        stop: generateSortedList
    }).on("click", generateSortedList)
        //.disableSelection()
        ; // obsolete;


    $(".chk-list-sortable").disableSelection(); // obsolete
    act(".chk-list-sortable");
    

    // hs.offline.showFormItems();

    if (hs.fm.isOffline())
        hs.msg.toast("وضع عدم الإتصال مفعل، سيتم تخزين كل البيانات مؤقتا على هذا الجهاز");

    if (!hs.fm.isView()) {
        $("select.ex-list").each(function () {
            var sel_id = $(this).attr("id");
            hs.select.toExList(sel_id);
        });

        act("select.ex-list");
    };

   


    hs.fm.applyFieldsConfig();
    act("applyFieldsConfig");

    var auto_refresh_interval = hs.safe.parseInt(getUrlParameter("auto-refresh"));
    if (auto_refresh_interval != 0)
        window.setTimeout(function () { hs.page.refresh(); }, 1000*auto_refresh_interval);

   
    hs.dg.adjustRepTable();
    act("adjustRepTable");

    markIndexListSelectedItem(true);
    act("markIndexListSelectedItem");

    hs.list.setupExFindAll();
    act("setupExFindAll");

   // $(".photo").draggable();

   // $("img.auto-size").trigger("click");

    $("#accord-menu").accordion({ collapsible: true, heightStyle: 'content' });



    if (hs.page.isHome()) 
        on_home_doc_ready();

    hs.page.mon();
    act("hs.page.mon");


    sticky_table_header();

    act("sticky_table_header");
       
    // focus_first();
    
    
    is_page_loaded = !0;

    act("page loading done.");


    $(".side-panel.auto-hide")
        .on("mouseover", function () { if ($(this).is(".auto-hide")) $(this).width(300); })
        .on("mouseout", function () { if ($(this).is(".auto-hide")) $(this).width(50); })
        .on("dblclick", function () { $(".auto-hide").removeClass("auto-hide"); })
    ;

    $(".box-at-center").position({
        my: "center",
        at: "center",
        of: "#page"
    });

    $("#fm-tm")
        .on("mouseover", function () {
            if ($(this).height() > ($(window).height()-200))
                $(this).addClass("dbl");

            
        });

    $(".popup-box")
        .on("mouseover", function () {
            hoverOnPopup(this, true);
            var lft = $(this).offset().left,
                max_lft = $(window).width() - 400;

            if (lft < 300 && lft < max_lft)
                $(this).addClass("at-left");
            else
                if (lft > max_lft)
                    $(this).addClass("at-right");

        }).on("mouseout", function () {
            hoverOnPopup(this, false);
        });

    $(".sub-menu-box")
        .on("mouseover", function () {
            hoverOnPopup(this, true);
            if ($(this).offset().top > ($(window).height() / 2))
                $(this).addClass("b"); // bottom popup

        })
        .on("mouseout", function () {
            hoverOnPopup(this, false);
        })
        .on("click", function () {
            $(this).toggleClass("clkd"); 
        })
        ;
   


   


    // define global funcs to be called from child iframes
    this.getExList = function (eId) {
        var wgt = $("#" + eId).exList("instance");
        if (wgt !== undefined) 
            return wgt;

        return null;
    };

  
    userNotifs();

    config_CodedListInputFieldDef();

    this.hsp = function () { return hs; };
      

    $(".page-status-bar").on("dblclick", function () {
        $(this).css("bottom", "15px");
    });



    

    if ($("#_sent_").val() == "1") {
        if (hs.fm.hasUiFlag(0x0010) && (hs.isInEditMode() || hs.fm.isActivity)) {  // suppress go-back

            var m = "تم إرسال هذه البيانات من قبل , إعادة إرسال بيانات هذه الصفحة قد يؤدي إلى تكرار للبيانات";
            m += K.br + K.br + K.hr + K.br;
            m += hs.link("اضغط هنا لفتح صفحة جديدة", window.location.href);
            m += " | " + hs.link("عودة للصفحة السابقة", "js:history.forward()");
            m += " | " + hs.link("فقط اغلق هذه الرسالة", "js:hs.page.cover(!1)");

            hs.page.cover(!0);

            hs.msg.fix(m, !0);

        }
        else {
            setTimeout(function () { hs.msg.wait(false); }, 1000);
        };
    }

    
   
    

    /*
   // useArabicDigits(); // did not work well

    $(window).on("resize", function () {
      var s = "";

       s += "Window Height=" + window.innerHeight + " Width=" + window.innerWidth;
      //s += "<br/>screen: width=" + screen.width + "  avail=" + screen.availWidth;
      //s += "<br/>screen: height=" + screen.height + "  avail=" + screen.availHeight;

      // hs.msg.toast(s, 5,true);
    });
    */


    setTimeout(function () { afterReady() }, 10);

    act("finish loading...");
}

// ----------------------

function afterReady() {
    focus_first();

    hs.page.autoPrint(true);


    if (hs.fm.hasUiFlag(0x0010)) { // suppress go-back // 18.12.2021 - beta
        if (hs.isInEditMode() || hs.fm.isActivity) {
            window.onload = disableBack();
            window.onpageshow = function (e) {
                if (e.persisted)
                    disableBack();
            }
        }
    }

    // if (!hs.err() && getUrlParameter("dlg-auto-close") == "y") AutoCloseDlg(true);
    

}


function hoverOnPopup(e, en) {
    e = $(e);

    if (e)
        e.addClass("hover");
    else
        e.removeClass("hover");
}



function runLinkUrl(a) {
    var url = $(a).attr("href");
    window.open(url, "prd", "fullscreen=no,scrollbars=yes,menubar=no,left=0,top=0,width=100,height=100");
}

function auto_print() {
    var n = 0;
    var wait_ms = 500;
    $("a.pos-auto-print").each(function () {
        var This = this;
        n++;
        window.setTimeout(function () { runLinkUrl(This) }, n * wait_ms);
    });

    n++;

    setTimeout(function () { hs.page.autoPrint(false); }, n * wait_ms);
}


function on_home_doc_ready() {
    hs.nav.check(true);
}

// -----------------

function onIndexListUpdated() {
    hsEasyForm.adjustLinks();
    markIndexListSelectedItem(false);
}


function markIndexListSelectedItem(load_first) {
    
    $("a.tree-l").on("click", function (e) {
        $("a.tree-l.ui-selected").removeClass("ui-selected");
        $(this).addClass("ui-selected");
    });
    
    
    $("table.fm-index tbody tr").on("click", function (e) {
        $("table.fm-index tbody tr.hs-selected").removeClass("hs-selected");
        $(this).addClass("hs-selected");
    });
    

    $("table.rep_tab.inline-menu.fm-index").keypress(function (event) {
        var keycode = (event.keyCode ? event.keyCode : event.which);
        if (keycode == '40' || keycode == '39') {
            nextIndexListItem(1);
            return;
        }

        if (keycode == '38' || keycode == '37') {
            nextIndexListItem(-1);
            return;
        }
    });
   
  
        var cmd_mode = getUrlParameter("cmd-mode");

        // load the first item, if not add  
        if (load_first && cmd_mode != "add")
            nextIndexListItem(0);
  
  
}

// ----------


function nextIndexListItem(next) {
    var _a;

    if (next == 1)
        _a = $("table.fm-index tbody tr.hs-selected").next().click().find("a");
    else
        if (next == -1)
            _a = $("table.fm-index tbody tr.hs-selected").prev().click().find("a")
        else
            _a = $("table.fm-index tbody tr:first a").click();

    if (_a.length != 0) {
        // $("#itemdata").attr("src", _a.attr("href"));
        hs.clickLink(_a);
        hs.ui.scrollTo(_a[0], "#fm-browse-index"); // check_me
    }
    
}

// -----------------------------------------

// test jquery plugin
/*
jQuery.fn.markBad = function () {

    var obj = this;

    obj.addClass("err");

    return this;
};
*/

function generateSortedList() {
   var result = ""; 
    $("input[type='checkbox']", this).each(function () {
        if ($(this).prop('checked') && $(this).val() != "on") // on => exclude all (or flds with no value)
            result += $(this).val() + ";";
    });

    $("input[type='hidden']", this).val(result);

    hs.trace(result);

    hs.msg.toast(result, 3,true);
}



// ************** SELECT DIALOG BOX


function ShowSelectDlgBox(sel_cmd, ctl_p1_id, ctl_p2_id, ctl_target_id, ctl_p3_id, ctl_p4_id, ctl_p5_id) {

    var al = arguments.length;


    ctl_p1_id = "#" + ctl_p1_id;
    ctl_p2_id = "#" + ctl_p2_id;

    if (al < 5) 
        ctl_p3_id = ctl_p4_id = ctl_p5_id = '';

    if (!hs.isEmpty(ctl_p3_id))
        ctl_p3_id = "&p3=" + $("#" + ctl_p3_id).val();

    if (!hs.isEmpty(ctl_p4_id))
        ctl_p4_id = "&p4=" + $("#" + ctl_p4_id).val();

    if (!hs.isEmpty(ctl_p5_id))
        ctl_p5_id = "&p5=" + $("#" + ctl_p5_id).val();
        

    var url = "/app/?" + sel_cmd + "&p1=" + $(ctl_p1_id).val() + "&p2=" + $(ctl_p2_id).val() + "&target=" + ctl_target_id + ctl_p3_id + ctl_p4_id + ctl_p5_id;

    if (window.top != window)
        ShowDlgF(url);
    else
        ShowDlgM(url);

}

// -----------------------------------


function ReturnSelectDlgResult(target_key_ctl, id_val, name_val) {

    // window.alert("target=" + target_key_ctl + ";id=" + id_val + ";name=" + name_val);

    var callback = parent.hs.fm.select_dlg_callback;

    
    var target = $("#" + target_key_ctl, parent.document.body);
    var e = target;


    if (callback != null) {
        if (!callback(e, id_val, name_val))
            return;

        parent.hs.fm.select_dlg_callback = null;
    };


    if (target.is("select")) 
        target.prepend($('<option />').val(id_val).html(name_val));
    
    if (hs.e.isReadonly(target) && !e.is("returnable")) {
        CloseDlg();
        return;
    };
    
    if (!hs.e.isDisabled(e)) {
        target.val(id_val).focus();
    };

    if (target.is(".ex-list, .ui-exlist")) {
        
        var wgt = parent.getExList(target_key_ctl);
        if (wgt != null)
            wgt.options.select_only = false;


        hs.fm.exListValue(target_key_ctl, id_val, name_val, parent.document.body, true);

        var cached_cl_id = $(e).attr("data-cl_id");
        if (!hs.isEmpty(cached_cl_id)) {
            hs.cache.removeItem(cached_cl_id);
            if (wgt != null && wgt.options.codelist != null) {
                var a = new Array();
                a[0] = id_val;
                a[1] = name_val;
                wgt.options.codelist.push(a);
            };
        }
    }
    else
        target.val(id_val);
    
   
       


     if (hs.fm.isAdd())
        if ($(target).attr("data-rs-refresh") == "yes") // 26.11.2018
            hs.msg.boxAtParent("{0}<hr/> <a href='javascript:hs.fm.refreshData()'>{1}</a>".format("You need to refresh the page", "Refresh Now"))

    $(target).trigger("change"); // 9.8.2018
    
    CloseDlg();
    
}




// **************** HS ******************* //


var hsWin = {

    win: null,

    show: function (url, winName) {

        if (winName == null)
            winName = "";

        if (!url.contains("?"))
            url += "?";

       
        if (!url.contains("ui-wnd="))
            url += "&ui-wnd=" + winName;

        if (!url.contains("sind=y"))
            url += "&sind=y";

       // this.close();
        this.win = window.open(url, winName, "fullscreen=yes,scrollbars=yes,menubar=yes");
        this.win.focus();
        
    },

    close: function () {
        if (this.win != null)
            this.win.close();
        this.win = null;
    },

};



// ********** hsPrintWin Object *************** //

var hsPrintWin = {

   
    win: null,

    show: function (url, sFeatures) {

        var showPrintDlg = false; // $(".pos-auto-print").length <= 0;

        

        if (sFeatures == null) {
            sFeatures = "fullscreen=no,scrollbars=yes,menubar=yes";
            if (url.contains("paper-size=POS"))
                sFeatures += ",left=0,top=0,width=400,height=1000";
            if (url.contains("paper-size=A4"))
                sFeatures += ",left=0,top=0,width=1000,height=1000";
        }
        
        if (this.win != null)
            this.close();
        else {
            this.win = window.open(null, "PrintWindow", sFeatures);
            this.win.close();
        }
        

        this.win = window.open(url, "PrintWindow", sFeatures);
        this.win.focus();

        if (!url.contains("cmd-mode=preview"))
        {
            this.win.onload = function () {
               
            }
        }

        if (showPrintDlg)
            this.win.setTimeout(function () { this.print(); }, 1000);

    },

    close: function () {
        if (this.win != null)
            this.win.close();
        this.win = null;
    },

};



// ***********  hsEasyForm object ************** //

var hsEasyForm = {

    cmd: null,
    fm_id : null,

    

    // ------ initialize form
    onFormUpdated: function () {
        $(".num").numberField();

        $(".closeable-box").closeableBox();

        $(".draggable-box").draggable();
    },

    init: function() {

        this.fm_id = this.getFormId();
        this.cmd = this.getCmd();

      //  if (hs.fm.isEntry())
        //   $(".auto").attr("title", hs.R.auto_fld);

        if (hs.fm.isItem())
            $(".full-screen-box").addClass(this.fm_id);
        

        SetDlgTitle('');

        
        $(document).tooltip();

        var tooltips = $("input[title]").tooltip({
            position: {
                my: "bottom",
                at: "top-20",
                collision: "none"
            }
        });

        $("input, select").on("focus", function () {
            window.focus_id = $(this).attr("id");
            if ($(this).is(".auto") && hs.fm.isEntry()) hs.msg.tip(this, hs.R.auto_fld, 5,400); // hs.msg.toast(hs.R.auto_fld, 3,!0);
        }).on("blur", function () {
            if ($(this).is(".auto") && hs.fm.isEntry()) hs.msg.tip(this, null, 0); // remove it
        });

       // if (hs.fm.isFind()) 
         //   $("select").each(function () { var e = $(this); if (hs.isEmpty(e.val())) { e.prepend($('<option class="all" />').val('').html('الكل')); e.val('');} });

        if (hs.fm.isFind() || hs.fm.isHome()) {
            $(".fm-tabs").addClass("fm-tabs-shade");
        }
        
       
        this.onFormUpdated();
       
        this.datepicker();
        this.timepicker();
        
        if (this.isEasyEntryModeDetected())
            this.enableEasyEntryMode();

        // adjust for POS print
        var print_paper_size = getUrlParameter("paper-size");

        if (!hs.isEmpty(print_paper_size)) { // we are in print mode
            if (print_paper_size == "POS") {
                $("#page, td.tit").addClass("pos");
            };
        };
        
        // ----------------

        $("div.page-status-bar").remove().appendTo("#page"); // to be visible in all tabs

        this.showUserMenu();
        this.adjustLinks();
        this.adjustToolbarPos();

        $(window).resize(function () { hsEasyForm.adjustToolbarPos(); });
        
        focus_first();

        this._configHotKeys();

        if (this.cmd == "list") {

            if ($("#chk-col-show").length > 0)
                $("#chk-col-show").focus();
        };


        
        if (hs.page.isForm()) {

            if (hs.fm.isHome())
                hs.page.addTools(hs.link(hs.R.refresh, hs.fm.getFormUrl() + "&nocache=y", "icon_refresh"));

            if (hs.fm.isView()) {
                if (hs.fm.hasFlag(0x10000))
                    hs.page.addTools(hs.link("سجل التغييرات", hs.fm.getFormUrl() + "&cmd=logact&id=" + hs.fm.itemId(), '', 'F'));
            }
        };

        if (hs.fm.isGrid()) {
            hs.page.addTools(hs.link("نسخ السطر الأول - إلى الحقول الفارغة", "js:hs.dg.copyRowToAll(false)",null,null,"سيتم النسخ إلى الحقول الفارغة فقط"));
            hs.page.addTools(hs.link("نسخ السطر الأول - إلى كل الحقول ", "js:hs.dg.copyRowToAll(true)", null, null, "سيتم فقدان البيانات السابقة، حيث سيتم إستبدالها بالقيم المنسوخة. "));

            hs.dg.setupTools();
        }

        hs.page.addTools(hs.link(hs.R.help, "js:hs.help()", "icon_help"));
        

        if ((hs.fm.isFind() || hs.fm.isList() || hs.fm.isActivity) && !hs.fm.isBatch())
            hs.page.addTools(hs.link("", "js:window.print()", "icon_print",null,null,!0), !0);
        

        if (hs.fm.isCfg() && !hs.fm.isMobile) {
           // hs.showHelpLinks();
        }

        var pt = hs.page.getTitle();
        if (!hs.isEmpty(pt))
            document.title = pt;

      //  hs.msg.info("اضغط مفتاح <a href='javascript:hs.help()'>F1</a> على الحقل لعرض المساعدة");

        $("input.suggest").each(function (e) {
            config_InputTextSuggest(this);
        });

       
    },

    // -----------------------
    
    _configHotKeys:function()
    {
        hs.R.press_ctrl_help = '';

        if (!hs.page.isPublic() || hsEasyForm.fm_id != null)
        {
            hs.R.press_ctrl_help += kv(hs.link("Ctrl+Alt+T", "js:hs.page.showPageTools()"), hs.L('Tools', 'أدوات'));
            hs.R.press_ctrl_help += kv(hs.link("F1", "js:hs.help()"), hs.R.help);

            if (hsEasyForm.fm_id != null) { // show this in form only
                hs.R.press_ctrl_help += kv('F2', hs.L('First field', 'الحقل الأول'));
                hs.R.press_ctrl_help += kv('F9', hs.L('New', 'جديد'));
                hs.R.press_ctrl_help += kv('F11', hs.L('Print', 'طباعة'));
                hs.R.press_ctrl_help += kv('F12', hs.L('Save', 'حفظ'));
            }
        }
        

        $(document).keydown(function (ev) {
            var s = '';

            var k = ev.which;

            if (ev.ctrlKey && ev.altKey) { // Ctrl+Alt
               
                
                if (k === K.T)
                    hs.page.showPageTools();

                return;
            };
            
                        
            /*
                if (ev.ctrlKey) s += "CTRL+";
                if (ev.altKey)  s += "Alt+";
                if (ev.shiftKey) s += "SHIFT+";
                s += k;
                $("#page_foot").html(s);
                */


            if (ev.ctrlKey) {

                if (k === K.UP) {
                 
                    MaximizeMe();
                    focus_first();
                    hs.absorb(ev);
                    return;
                };

                hs.msg.toast(hs.R.press_ctrl_help + hs.R.press_ctrl_help_extra, 5, true);
            };
            

            // ******************** //

           

            // ******************** //

            if (hs.page.isForm()) {

                /*
                if (hs.fm.hasUiFlag(0x0020) && hs.fm.isView() && !$(ev.target).is(":input")) {
                    if (ev.which === K.RGT)
                        hs.clickLink($(".fm-tb a.icon_prev"));
                    else
                        if (ev.which === K.LFT)
                            hs.clickLink($(".fm-tb a.icon_next"));
                };
               */

                if (ev.which === K.F1) {
                    var id = hs.e.getId(ev.target);
                    var fm_hid =  hs.fm.getHelpFmId();
                    
                    if (hs.isEmpty(id))
                        id = fm_hid;
                    
                    if (!hs.isEmpty(id))
                        hs.help(id, fm_hid, true);

                    hs.absorb(ev);
                    return;
                };

                if (ev.which === K.F2) { // F2 = first field
                    focus_first();
                    hs.absorb(ev);
                    return;
                };

                if (ev.which === K.F3) {
                    var fm_id = hs.e.cfg(ev.target, "fm");
                    var fld_id = hs.e.getId(ev.target);
                    if (!hs.isEmpty(fm_id)) {

                        hs.msg.toast(fm_id);

                        hs.absorb(ev);

                        var item_id = hs.fm.val(fld_id);// $(e.target).val();

                        hs.msg.toast(item_id);

                        if (!hs.isEmpty(item_id) && hs.e.isReadonly(ev.targe))
                            ShowDlg("/app/fms/?fm=" + fm_id + "&cmd=view&id=" + item_id);
                        else
                            ShowDlg("/app/fms/?fm=" + fm_id + "&cmd=find&cmd-mode=select&select-target=" + fld_id);

                        return;
                    };
                    
                       
                };


                if (ev.which === K.F9) { 
                    hs.absorb(ev);
                    hs.clickLink($(".fm-tb a.icon_add"));
                    return;
                };

                if (ev.which === K.F10) { 
                    hs.absorb(ev);
                    hs.clickLink($(".fm-tb a.icon_edit"));
                    return;
                };

                if (ev.which === K.F11) { 
                    hs.absorb(ev);
                    hs.clickLink($("a.do-print"));
                    return;
                };

                if (ev.which === K.F12) {  // F12 or Enter - Save
                    DoSubmit();
                    hs.absorb(ev);
                    return;
                };




            }; // end form hk
            
            // ***************** //

            if (ev.which === K.F3) {
                ShowDlg("/app/fms/?fm=user-tools&fm-mode=find-sys");
                hs.absorb(ev);
                return;
            };
        
            if (ev.which === 27) // escape
                on_escape(this);

            if (ev.which === 8) { // Backspace
                if ($("input:focus, textarea:focus").length == 0) {
                    hs.absorb(ev);
                    hs.goBack();
                    return;
                };
            };


            if (ev.which === 13) {
                hsEasyForm.processEnter(ev);
            }; 

            if (ev.which === K.LFT) {
                if (can_focus_right_or_left($(ev.target), ev,true)) {
                    focus_left($(ev.target));
                    hs.absorb(ev);
                };
            }; 

            if (ev.which === K.RGT) {
                if (can_focus_right_or_left($(ev.target),ev,false)) {
                    focus_right($(ev.target));
                    hs.absorb(ev);
                };
            }; 

        });
    },

    processEnter: function (event) { // e=event
        
        if ($(event.target).is("textarea, a"))
            return;

       

        var cmd = hsEasyForm.getCmd();

        if ((/*cmd == "find" ||*/ cmd == "home") && !hs.isEmpty(hsEasyForm.fm_id)) { //  
            DoSubmit();
            return;
        };

        if (cmd == "add") {
            if (event.ctrlKey)
                window.setTimeout(function () { DoSubmit(); }, 100);
            else {
                if ($("#confirm_box").length > 0)
                    DoSubmit();
                else {

                    if ($(event.target).is("input")) {
                        focus_next($(event.target));
                    }
                    else
                        ConfirmUrl("javascript:DoSubmit()", hs.L("Save", "حفظ.."));

                    hs.absorb(event);
                };
            };
        };


    },

    adjustToolbarPos: function () {
     
        var is_sind = getUrlParameter("sind") == "y";
        var is_dlg = !hs.isMainWnd();
        var pg_hdr_hi = $(".page_header").outerHeight() || 0;

        if (is_dlg || is_sind) 
            pg_hdr_hi = 0;

       // if (pg_hdr_hi != 0) pg_hdr_hi += 5; // btm border
                
        var fm_tb$ = $(".fm-tb");

        if (is_dlg)
            fm_tb$.css("padding-left","20px").css("padding-right","20px");
                
        
        fm_tb$.addClass("box-at-top").css("top", pg_hdr_hi + "px");

        var fm_tb_hi = fm_tb$.outerHeight(true) || 0;
              
                             
        var tm = fm_tb_hi + pg_hdr_hi;
      

        $(".full-screen-box, .side-panel, .fm-browse-item").css("margin-top", tm + "px").css("top", "0");

        if ($(".page_footer").length == 0)
            $(".full-screen-box, .side-panel, .fm-browse-item, #fm-browse-index").css("bottom", "0");
      


      //  hs.msg.toast("pg_hdr_hi=" + pg_hdr_hi + "  fm_tb_hi=" + fm_tb_hi + " => tm=" + tm );

      

        return;

        
    },
    // ------------------

    adjustLinks: function () {
        // Force the user to confirm leaving the page if is in edit mode
        var entry_protected = hs.isEntryProtected()
        var is_sind = !hs.isMainWnd() || getUrlParameter("sind") == "y";
        
        if (1) {
            //  act("adjust links");
            $("a").each(function (e) {
                var url = $(this).attr("href");
                var target = $(this).attr("target");
                if (hs.isEmpty(target))
                    target = '';

                if (hs.isEmpty(url) || target == "_blank")
                    return;

                if (target == "D") {
                    url = "javascript:ShowDlg('" + url + "')";
                    $(this).attr("target", "").attr("href", url);
                    return;
                };

                if (target == "F") {
                    url = "javascript:ShowDlgF('" + url + "')";
                    $(this).attr("target", "").attr("href", url);
                    return;
                };

                if (target == "M") {
                    url = "javascript:ShowDlgM('" + url + "')";
                    $(this).attr("target", "").attr("href", url);
                    return;
                };

                if (target == "S") {
                    url = "javascript:ShowDlgS('" + url + "')";
                    $(this).attr("target", "").attr("href", url);
                    return;
                };

                if (target == "T") {
                    url = "javascript:ShowDlgT('" + url + "')";
                    $(this).attr("target", "").attr("href", url);
                    return;
                };

                if (target == "W") {
                    url = "javascript:ShowDlgW('" + url + "')";
                    $(this).attr("target", "").attr("href", url);
                    return;
                };

                var url_changable =(url.indexOf("javascript:") == -1) && (url.indexOf("HsWin:") == -1) && (url.indexOf("#") != 0);

                if (is_sind && url_changable)
                    url = sind(url);

                if (!entry_protected) {
                    if (url_changable) {
                        url ="javascript:go('" + url + "')";
                        $(this).attr("href", url);
                    };

                    return;
                };

                if (!$(this).is(".no-leave-to") && url_changable) { // HsWin: protocol to call win apps
                    if ($(this).is(".link_icon, .cmd, .force-leave-to") || target == "itemdata") {
                        url = "javascript:hs.leaveTo('" + url + "', false,'" + target + "')";
                        $(this).attr("target", "");
                    }
                    else //
                    {
                        if ($(this).is(".no-dlg") || (url.indexOf("cmd=addx") != -1) || (url.indexOf("cmd=browse") != -1))
                            $(this).attr("target", "_blank")
                        else
                            url = "javascript:hs.leaveTo('" + url + "', true,'" + target + "')";

                    };

                    $(this).attr("href", url);
                }
            });
        };
    },

    showUserMenu: function () {

        var is_mobile = hs.fm.isMobile;
        var is_hp = hs.page.isHome();
        var show_um = true;// !(hs.page.getPathQry() == "/app/?menu"); // do not show menu along with side menu // config_me
        var pg_has_menu = true;// is_mobile ? true : !is_hp;
                

        if (pg_has_menu && show_um) {
            var user_menu = hs.cache.getItem('user-menu');
            $("#u-m").html(user_menu);
        };

        if (!is_mobile && is_hp) {
            var user_menu = hs.cache.getItem('user-menu');
            $("#u-mh").html(user_menu);
        }

        // user tools menu

        if (!is_mobile) {
            var app_title_cont = "<a title='Back' class='cmd icon icon_back' href='javascript:hs.goBack()'>&nbsp;</a>&nbsp;&nbsp;&nbsp;<a class='link_icon icon_home' target='_parent' title='الصفحة الرئيسية' href='/app'>" + hs.app_title + "</a>"

            $("#a-t").html(app_title_cont);
        };


        var cont = "";

        
        if (is_mobile)
            cont += "<b class='title'>" + hs.uname + "</b>";

        cont += hs.link(hs.L("Logout","تسجيل خروج"), "/app/auth/login/?cmd=logout", "icon_logoff", '', '');
        cont += hs.link(hs.L("User Tools","أدوات المستخدم"), "js:hs.page.showPageTools()", '');
        cont += K.hr;
        cont += hs.link(hs.L("Users Mgmt", "إدارة المستخدمين"), "/app/fms/?fm=user", "icon_login");
        cont += hs.link(hs.L("System Admin","إدارة النظام"), "/app/?menu&id=sys-menu", "icon_tools","F");
        cont += hs.link(hs.L("System Config","إعدادات النظام"), "/app/fms/?fm=sys-cfg", "icon_config","F");
        cont += K.hr;
        cont += hs.link(hs.L("System Menu","قائمة الأنظمة"), "/app/?menu", "icon-side-panel no-dlg");
        //cont += hs.link(hs.L("Reports", "التقارير"), "/app/?menu&id=app-rep-mnu", "icon_report no-dlg");
        cont += hs.link(hs.R.find, "/app/fms/?fm=user-tools&fm-mode=find-sys", "icon_find", 'D');
        cont += hs.link(hs.L("Open New page", "فتح صفحة جديدة"), "/app/", "icon_home no-dlg","_blank");

        cont += K.hr;
        cont += hs.link(hs.R.help, "js:hs.help()", "icon_help");


        var user_name_link = is_mobile ? "<a class='link_icon_only icon_user user-menu' href='javascript:noop()'>&nbsp;</a>" : "<a class='link_icon icon_user user-menu' href='javascript:noop()'>" + hs.uname + "</a>";
        var quick_links = is_mobile ? "" :
         //   hs.link('القائمة الجانبية', '/app/?menu', 'cmd icon-side-panel icon', null, null, true) +
           // hs.link('لوحة التحكم', '/app/?hs-sys-cp', 'cmd icon_config icon', 'F', null, true)
           "<a title='القائمة الجانبية' class='cmd icon-side-panel icon' href='/app/?menu'>&nbsp;</a><a title='لوحة التحكم' class='cmd icon_config icon' href='/app/?hs-sys-cp'>&nbsp;</a>";
        

        var all_cont = "<span class='popup-box'>" + user_name_link + "<span class='popup-menu' style='width:300px;'>" +
            cont + "</span></span>" +
            (hs.alrts_cnt == 0  ? "" : "<a title='alerts' class='link_small_icon icon_alerts' href='javascript:ShowDlg(\"/app/?hs-sys-alerts\")'>"+hs.alrts_cnt+"</a>") +
            quick_links;

        if (is_mobile)
            all_cont +="<a class='link_icon icon_home' target='_parent' title='الصفحة الرئيسية' href='/app'>" + hs.app_title + "</a>"


      $("#u-tm").html(all_cont).addClass(is_mobile ? "" : "mir-flt");
                
        
     
    },

    getFormId: function () {
        if (this.fm_id != null)
            return this.fm_id;

        this.fm_id = getUrlParameter("fm");
        return this.fm_id;
    },

    // ------------------

    getCmd: function () {
        if (this.cmd != null)
            return this.cmd;

        this.cmd = hs.fm.getCmd();
        return this.cmd;
    },

    // ---------

    isEasyEntryModeDetected: function () {
        
        if (this.fm_id == null || flds_order == null)
            return false;
        
        return this.cmd == "add" || this.cmd == "edit" || hs.fm.isActivity || hs.fm.isCfg() || this.cmd == "find"; // || this.cmd == "home" || this.cmd == null);

    },

    // -------------------
    

    datepicker: function () {
        $(".datepicker:not(:disabled, [readonly='readonly'])").each(function () {
            hs.e.datepicker(this,1);
        });
    },

    // --------------------------

    timepicker: function () {
        if (hs.fm.hasUiFlag(0x0040)) { // 12hrs time fmt
            hsUiTimeFormat = "hh:mm:ss p"; 
            if (hsUiLang == "ar") {
                h_am = h_a = h_AM = "ص";
                h_pm = h_p = h_PM = "م";
            };
        };

        $(".timepicker:not(:disabled, [readonly='readonly'])").timepicker({
            timeFormat: hsUiTimeFormat,
            interval: 30,
            dynamic: false,
            dropdown: true,
            scrollbar: false

        }).attr("autocomplete", "off").addClass("fix-size")
        .on("dblclick", function () {
            var tm = new Date();
            $(this).val(tm.getHours().toString().padStart(2, '0') + ":" + tm.getMinutes().toString().padStart(2, '0') + ":" + tm.getSeconds().toString().padStart(2, '0'));
        });
    },

    
    
    enableEasyEntryMode : function() {
        $(":input").on("keydown", function (ev) {
            if (!ev.ctrlKey && ev.which === 13 && !$(this).is("textarea, :button, :submit")) {
                hs.absorb(ev);
                if (!$(this).is(".no-enter"))
                    focus_next(this);
            };
        });
    }, 

    // ------------------------------- //

    show: function (url) {
        this.close();
        this.win = window.open(url, "PrintWindow", "width=500, height=500");
        this.win.focus();
        this.win.onload = function () {
            this.print();

        };
    },

    close: function () {
        if (this.win != null)
            this.win.close();
        this.win = null;
    },

    addToIndexList: function (cont) {

        //hs.link(

        var index_tab = $("table.rep_tab.inline-menu.fm-index", parent.document.body);
        if (index_tab.length == 0)
            return;

        $("<tr><td>" + cont + "</td></tr>").prependTo(index_tab);

        //markIndexListSelectedItem(false);
        
    },


    onNewItemSaved: function (id, tit, url) {
        
      //  hs.msg.toast(tit + " is added", 15);

        var index_tab = $("table.rep_tab.inline-menu.fm-index", parent.document.body);
        if (index_tab.length != 0) {

            url = "javascript:hs.leaveTo('" + url + "', false,'itemdata')";

            var cont = hs.link(tit, url, null, null);
            $("<tr><td>" + cont + "</td></tr>").prependTo(index_tab);
            return;
        }

        
    }
    
        

    

};

// *************************** //
// *************************** //



// HowbaniSoft object
var hs = {

    app_title:'',
    uname:'',
    alrts_cnt:0,
    log_level: 9, //5
    logs:'',
    amnt_decimals: 2,
    amnt_decimals_hp: 5,
    hlinks: !1,
    R: enRes,

/* ----------------  */

    $: function (_e, _p) { //  get jQry obj of the elem

        if (arguments.length < 2) _p = null;
        if ("string" != typeof _e)
            return $(_e);
        else {
            if (_e.startsWith("#"))
                return $(_e, _p);
            else
                return $("#" + _e, _p);
        };

        
    },

    err: function () {
        if ($(".box-at-bottom .err-msg").length > 0)
            return 1;
        else
            return 0;
    }
    ,
    status:function (user_name, alerts_count, app_title) {
        hs.uname = user_name;
        hs.alrts_cnt = alerts_count;
        hs.app_title = app_title;
    },
    

    L: function (en_text, loc_text) {
        if (hsUiLang == "en")
            return en_text;

        return loc_text;
    },

    isRTL: function () {
        return hsUiLang == "ar";
    },

    trace:function(m) {
        log(m, 8); 
    },

    isMainWnd: function (w) {
        if (arguments.length < 1) 
            w = window;
        
        return w.parent == w;
    },

    isEmpty: function (s) {
        if (typeof (s) === "undefined")
            return true;

        return s == null || s.length == 0;
    },

    sound: function (s) {
        if (hs.isEmpty(s))
            return "";

        var t = "";
        for (var i = 0; i < s.length; i++) {
            var c = s.charAt(i);

            if (c == 'أ' || c == 'إ' || c == 'ء' || c == 'آ')
                c = 'ا'
            else
                if (c == 'ى' || c == 'ئ')
                    c = 'ي'
                else
                    if (c == 'ؤ')
                        c = 'و'
                    else
                        if (c == 'ة')
                            c = 'ه';

            t = t + c;


        }

        // hs.msg.toast("s='" + s + "' t=" + t)

        return t;
    },
    len: function (s) {
        if (hs.isEmpty(s))
            return 0;
        return s.length;
    },

    isFlagEnabled: function (flags, f) {
        return (flags & f) == f;
    },

    pref: function (k, v) { // client preference saved locally
        k = "_" + k;
        if (arguments.length < 2)
            v = hs.cache.getItem(k);
        else
            hs.cache.setItem(k,v);


        return v;
    },

    link: function (tit, url, _class, _target,_confirm_msg,_icon_only) {
        var al = arguments.length;
        if (al < 3) _class = '';
        if (al < 4) _target = '';
        if (al < 5) _confirm_msg = null;
        if (al < 6) _icon_only = false;

        
        var tip='';

        if (_icon_only) {
            tip = tit;
            tit = "&nbsp;";
            _class += " link_icon_only";
        };

        if (hs.isEmpty(tit)) tit = "&nbsp;";


        if (url.startsWith("js:")) url = url.replace("js:", JS);
        if (_target == 'D') { url = JS + "ShowDlg('" + url + "')"; _target = ''; };
        if (_target == 'F') { url = JS + "ShowDlgF('" + url + "')"; _target = ''; };

        if (!hs.isEmpty(_target)) _target = "target='" + _target + "'";
        if (!hs.isEmpty(tip)) tip = "title='" + tip + "'";

        if (_confirm_msg == null)
            return "<a {4} {3} class='{2}' href=\"{1}\">{0}</a>".f(tit, url, _class, _target,tip);
        else {
            if (_confirm_msg == '') _confirm_msg = tit;
            return "<a {4} class='{2}' href=\"javascript:ConfirmUrl('{1}','{3}')\">{0}</a>".f(tit, url, _class, _confirm_msg,tip);
        }

    },
    
    endsWith:function(t, s) {
        var idx = t.length - s.length;
        return idx >= 0 && t.indexOf(s, idx) > -1;
    },

    random: function (n) {
        if (arguments.length < 1)
            n = 6;
        if (n > 10)
            n = 10;
        
        var min = Math.pow(10, n);
        var max = Math.pow(10, n+1);
        
        var r = min + Math.floor(Math.random() * (max-min));
    },

    absorb: function (ev) {
        if (ev == null)
            return;
        ev.cancelBubble = false;
        if (ev.preventDefault)
            ev.preventDefault();
        if (ev.stopPropagation)
            ev.stopPropagation();
       
    },

    isInEditMode: function () {
        var cmd = hs.fm.getCmd();
        return cmd == "add" || cmd == "edit" || cmd == "grid" || hs.fm.isCfg();
    },

    isEntryProtected: function () {
        if (!isEditModeProtected)
            return false;
        var cmd = hs.fm.getCmd();

        var url = window.location.href;

        return cmd == "add" || cmd == "edit" || cmd == "grid" /*|| cmd=="browse" || url.contains("/app/?menu")*/ || $("#itemdata").length > 0;
    },
    
    goBack: function () {
        hs.leaveTo("javascript:history.back()");
        
    },

    // confirm before leaving the current page (if the page is in edit mode)
    leaveTo: function (url, open_dlg, tgt) {

        if (arguments.length < 2)
            open_dlg = false;

        if (arguments.length < 3)
            tgt = '';


        if (hs.isEntryProtected() && hs.fm.dirty()) {
            if (open_dlg)
                ShowDlg(url);
            else
                ConfirmUrl(url, hs.R.exit_no_save, tgt);
        }
        else 
            go(url, tgt);
            
        
    },

    eval: function (x) {
        try { eval(x); } catch (e) { log(x); hs.logEx(e); }
    },
    
    logEx: function (ex, show) {
        log(ex.message);

        if (arguments.length < 2)
            show = false;

        if (show)
            hs.msg.toast(ex.message, 10000);

        return ex.message;
    },

    showLogs: function () {
        hs.ui.showContentDlg('', hs.ui.div('lft ui-box no-bord', hs.logs));
        hs.msg.toast("is_dirty=" + is_dirty,10);
    },

    fmtNumber: function (n, blank_zero) {
        if (arguments.length < 2) blank_zero = false;
        if (blank_zero && n == 0)
            return '';

        if (typeof Intl !== "undefined")
            return new Intl.NumberFormat('en-US', { maximumFractionDigits: hs.amnt_decimals, minimumFractionDigits: 0 }).format(n);
        else
            return n;
    },

    fmtAmount: function (n, blank_zero) {
        if (arguments.length < 2) blank_zero = false;
        if (blank_zero && n == 0)
            return '';

        if (typeof Intl !== "undefined")
            return new Intl.NumberFormat('en-US', { maximumFractionDigits: hs.amnt_decimals, minimumFractionDigits: hs.amnt_decimals }).format(n);
        else
            return n;
    },
     
    fmtAmountHP: function (n, blank_zero) { // like ex-rate
        if (arguments.length < 2) blank_zero = false;
        if (blank_zero && n == 0)
            return '';

        if (typeof Intl !== "undefined")
            return new Intl.NumberFormat('en-US', { maximumFractionDigits: hs.amnt_decimals_hp }).format(n);
        else
            return n;
    },
    
    fmtRate: function (n) { // like ex-rate
        if (typeof Intl !== "undefined")
            return new Intl.NumberFormat('en-US', { maximumFractionDigits: 12 }).format(n);
        else
            return n;
    },
    
    unescapeKeyVal:function(s) {
     
        if (hs.isEmpty(s))
            return s;

        return s.replace(/\[&eq\]/gi, '=').replace(/\[&sq\]/gi, ';').replace(/\[&cr\]/gi, '\n'); 
    },

    clickLink: function (_e, _p) {
        if (arguments.length < 2) _p = null;
        var a = h$(_e,_p);
        if (a.length == 0)
            return;
        a = a.first();
        var src = $(a).attr('href');
        if (src.startsWith('javascript:'))
            hs.eval(src);
        else {
            var target = $(a).attr('target');

            if (hs.isEmpty(target))
                window.location.href = src;
            else
                window.open(src);
        };
    },

    showHelpLinks: function () {
        if (!this.hlinks) {
            this.hlinks = !0;
            $(":input").each(function () {
                if (!$(this).is("input[type='hidden']"))
                    $("<a tabindex='-1' class='hlnk' href='javascript:hs.help(\"" + hs.e.getId(this) + "\")'> ? </a>").insertAfter(this);
            });
        } else {
            $("a.hlnk").remove();
            this.hlinks = !1;
        }
    },

    showFieldNames: function () {
        if (!this.hlinks) {
            this.hlinks = !0;
            $(":input").each(function () {
                if (!$(this).is("input[type='hidden']")) {
                    var eid = hs.e.getId(this);
                    $("<a tabindex='-1' style='color: blue; font-weight: bold;' class='hlnk' href='javascript:hs.help(\"" + eid + "\")'> " + eid + " </a>").insertAfter(this);
                }
            });
        } else {
            $("a.hlnk").remove();
            this.hlinks = !1;
        }
    },

    help: function (help_id, fm, rsrv_focus) {
        
        if (arguments.length < 3)
            rsrv_focus = false;

        if (arguments.length == 0) 
            help_id = fm = hs.fm.getHelpFmId();
       
        help_id = help_id || '';
        fm = fm || '';
        
        if (!rsrv_focus)
            window.focus_id = null;

        var url = "/app/?help&id=" + help_id + "&fm=" + fm;
               
        if (arguments.length == 1)
            do_ajax_callout(url);// h$(help_id));
        else
            ShowDlg(url);
        
    },

    removePageBreaks: function () {
        $("div.page-break").remove();
    },

    parseJson: function (s) {
        var obj = null;
        try {
            obj = JSON.parse(s);
        } catch (ex) {
            hs.logEx(ex);
        };

        return obj;
    },

    // ------------------- //

    copyForm: function () {

        if (!hs.fm.isEntry()) {
         //   hs.msg.error(hs.R.not_allowed);
         //   return;
        };

        var data = ''; // $(document.forms[0]).serialize();
               
            $(":input").each(function () {
                if (!$(this).is("input[type='hidden']")) {
                    var eid = hs.e.getId(this);
                    var val = hs.fm.val(eid);// $(this).val();
                    if (!hs.isEmpty(val))
                        data += eid + "=" + val + ";";
                }
            });
   


        var ex_ctrl = '';

        var help = "";
               

        hs.inputBox.show(true, data, 'نسخ بيانات الشاشة', 'قم بنسخ المحتوى التالي:', ex_ctrl, null, '', help, function (t) {
            $("#inp_box_text").select();
            document.execCommand("copy");
        });

    },

    pasteForm: function () {

        if (!hs.fm.isEntryOrActivity()) {
            hs.msg.error(hs.R.not_allowed);
            return;
        };

              

        var ex_ctrl = ''; // "<input value='Y' id='_chk_auto' name='_chk_auto' type='checkbox' /><label for='_chk_auto'>تعبئة الحقول الفارغة بالقيم التلقائية - سوف تؤشر باللون الأصفر</label>";

        var help = "يجب أن تتطابق الأعمدة في الترتيب والعدد، عمود الإجمالي غير مطلوب<br/>\
    يجب نسخ رقم الصنف و رقم الوحدة، وليس اسم الصنف واسم الوحدة<br/>";
                

        hs.inputBox.show(true, null, 'لصق من اكسل', 'قم بلصق المحتوى الذي نسخته هنا:', ex_ctrl, null, '', help, function (t) {

            var cfg_arr = t.split(';');

            for (var i = 0; i < cfg_arr.length; i++) {
                var one_cfg = cfg_arr[i].split('=');
                var eid = one_cfg[0];
                var val = hs.unescapeKeyVal(one_cfg[1]);

                if (!hs.isEmpty(val))
                    hs.fm.val(eid, val);
                    
            };
        });

        //$("#inp_box_text").focus();
        //document.execCommand("paste");

    },



    

    nav: {

        reqNav: [["unknown", 0], ["Opera", 37], ["MSIE", 10], ["Chrome", 36], ["Safari", 10], ["Firefox", 38]], // browsers minimum supported version// do not change the order

        id: 0,
        ver: 0,
        chrome: false,
        
        detect: function () {
            this.check(false);
        },
                
    
        // adapted from: http://www.javascripter.net/faq/browsern.htm
        check: function (warn_user) {
            var navId=0, navVer= 0;

        
            var nVer = navigator.appVersion;
            var nAgt = navigator.userAgent;
            var fullVersion = '' + parseFloat(navigator.appVersion);
            var majorVersion = parseInt(navigator.appVersion, 10);
            var nameOffset, verOffset, ix;

            // In Opera 15+, the true version is after "OPR/" 
            if ((verOffset = nAgt.indexOf("OPR/")) != -1) {
                navId = 1;
                fullVersion = nAgt.substring(verOffset + 4);
            }
                // In older Opera, the true version is after "Opera" or after "Version"
            else if ((verOffset = nAgt.indexOf("Opera")) != -1) {
                navId = 1;
                fullVersion = nAgt.substring(verOffset + 6);
                if ((verOffset = nAgt.indexOf("Version")) != -1)
                    fullVersion = nAgt.substring(verOffset + 8);
            }
                // In MSIE, the true version is after "MSIE" in userAgent
            else if ((verOffset = nAgt.indexOf("MSIE")) != -1) {
                navId = 2;
                fullVersion = nAgt.substring(verOffset + 5);
            }
                // In Chrome, the true version is after "Chrome" 
            else if ((verOffset = nAgt.indexOf("Chrome")) != -1) {
                navId = 3;
                fullVersion = nAgt.substring(verOffset + 7);
                this.chrome=true;
            }
                // In Safari, the true version is after "Safari" or after "Version" 
            else if ((verOffset = nAgt.indexOf("Safari")) != -1) {
                navId = 4;
                fullVersion = nAgt.substring(verOffset + 7);
                if ((verOffset = nAgt.indexOf("Version")) != -1)
                    fullVersion = nAgt.substring(verOffset + 8);
            }
                // In Firefox, the true version is after "Firefox" 
            else if ((verOffset = nAgt.indexOf("Firefox")) != -1) {
                navId = 5;
                fullVersion = nAgt.substring(verOffset + 8);
            }
                // In most other browsers, "name/version" is at the end of userAgent 
            else if ((nameOffset = nAgt.lastIndexOf(' ') + 1) <
                      (verOffset = nAgt.lastIndexOf('/'))) {
                browserName = nAgt.substring(nameOffset, verOffset);
                fullVersion = nAgt.substring(verOffset + 1);
                if (browserName.toLowerCase() == browserName.toUpperCase()) {
                    browserName = navigator.appName;
                }
            }
            // trim the fullVersion string at semicolon/space if present
            if ((ix = fullVersion.indexOf(";")) != -1)
                fullVersion = fullVersion.substring(0, ix);
            if ((ix = fullVersion.indexOf(" ")) != -1)
                fullVersion = fullVersion.substring(0, ix);

            majorVersion = parseInt('' + fullVersion, 10);
            if (isNaN(majorVersion)) {
                fullVersion = '' + parseFloat(navigator.appVersion);
                majorVersion = parseInt(navigator.appVersion, 10);
            }

            navVer = majorVersion;
            this.id = navId;
            this.ver = navVer;
            
            if (navId > 0 && navVer < this.reqNav[navId][1]) {
                if (warn_user)
                    hs.msg.warn("النظام يتطلب الإصدار ({2} : {0}) من هذا المتصفح. الإصدار الحالي ({1} : {0}) غير متوافق بشكل كامل".f(this.reqNav[navId][0], this.ver, this.reqNav[navId][1]));

                return false;
            };



            return true;
        },


    }, // nav



    // Ajax helper
    ajax: {
        
        get:function (_url, target_eid) {
            $.ajax({
                url: _url,
                cache: false,
                success: function (result) {
                    hs.eval(result); // execute the result as script
                }
            });
        },


        getWait: function (_url, _title ) { // not async

            hs.msg.wait(true,_title);
            
            var jqxhr = $.ajax({
                url: _url,
                cache: false,
                async: false
                
            })
                .done(function (result, textStatus, jqXHR) {
                    // log(result);

                    hs.eval(result); // execute the result as script
                })
                .fail(function (jqXHR, textStatus, errorThrown) {

                    var err_msg = '';

                    if (jqXHR.status == 0)
                        err_msg = hs.R.conn_err;
                    else
                        err_msg = jqXHR.statusText;
                    
                    hs.msg.error(err_msg);

                })
                .always(function () {
                    hs.msg.wait(false);
                });

        },
        
        getJSON: function (_url, _title, callback) { 

            
            hs.msg.toast(_title,10);

            var jqxhr = $.ajax({
                url: _url,
                cache: false,
                async: true,
                dataType: "json"
            })
                .done(function (data) {
                    hs.msg.toast(_title + "... Done", 10);
                    callback(data);
                })
                .fail(function (jqXHR, textStatus, errorThrown) {

                    var err_msg = '';

                    if (jqXHR.status == 0)
                        err_msg = hs.R.conn_err;
                    else
                        err_msg = jqXHR.statusText;

                    hs.msg.error(err_msg);
                    callback(null);

                })
                .always(function () {
                    // hs.msg.wait(false);
                });

        },
        

        // check if if failed in server side
        isRejected: function (jqXHR) {
            return jqXHR.status.toString().contains("202"); // use the http status code (202: HttpStatusCode.Accepted) as indicator that the request is not processed 
        },

        getErrMsg: function (jqXHR) {
            var err_msg = '';

            if (jqXHR.status == 0)
                err_msg = hs.R.conn_err;
            else
                err_msg = jqXHR.statusText;

            return err_msg;
        }

       


    }, // ajax
        

    // **************** // 

    safe: {

        

        appendUrlParam: function (url, k, v) {
            k = k + "=";
            if (url.indexOf(k) < 0) {
                if (url.indexOf("?") < 0)
                    url = url + "?" + k + v;
                else
                    url = url + "&" + k + v;
            }
            return url;
        },

        adjustNum: function (s) {

            if (typeof (s) === "number")
                return s;

            if (!hs.isEmpty(s)) {
                s = s.replace(/,/g, '');
            };

            return s;
        },

        trimCode: function (s) {
            if (hs.isEmpty(s))
                return null;

            var n = parseInt(s, 10);
            if (isNaN(val))
                return s.trim();

            return n.toString();
        },

        parseInt: function (s) {
            s = hs.safe.adjustNum(s);
            var val = parseInt(s, 10) || 0;
            return (isNaN(val)) ? 0: val;
        },

        parseFloat: function (s) {
            s = hs.safe.adjustNum(s);
            var val = parseFloat(s) || 0;
            val = (isNaN(val)) ? 0 : val;
                        
            if (val > 0 && typeof (s) === "string" && (s.startsWith("-") || s.EndsWith("-")))
                val = -1 * val;

            return val;
        },

        ceil: function (n) {
            if (n == null)
                return 0;
            return Math.ceil(n);
        },

        round: function (num, decimals) {
            if (num == null)
                return 0;
            if (arguments.length < 2)
                decimals = hs.amnt_decimals;
            return hs.safe.parseFloat(num.toFixed(decimals)); 
        },

        roundHP: function (num, decimals) {
            if (num == null)
                return 0;
            if (arguments.length == 1)
                decimals = 4;
            return this.parseFloat(num.toFixed(decimals)); // toFixed return string
        },

        fract: function (num) {
            if (num == null)
                return 0;
            return this.round(num - Math.floor(num)); // hs.safe.round(num,0);
        },


        deparam: function (queryString, sParam) {
            var sPageURL = decodeURIComponent(queryString),
                sURLVariables = sPageURL.split('&'),
                sParameterName,
                i;

            for (i = 0; i < sURLVariables.length; i++) {
                sParameterName = sURLVariables[i].split('=');

                if (sParameterName[0] === sParam) {
                    return sParameterName[1] === undefined ? true : sParameterName[1];
                }
            }
        }

        

    }, // safe

    // **************** // 

    // page render setting
    pg : {
        mobile: false
       
    },

    // *************** //

    // Page Class

    page: {
        max_idle : 5,
        idle_time: 0,
        intId: null,

        scrollTo: function (top) {
            if (arguments.length < 1)
                top = 0;
            $("#page").scrollTop(top);
            $("a#results").remove();
        },
        
        resetIdle: function () {

            if (hs.page.intId == null)
                return;

           

            if (hs.page.idle_time < 5) {
                hs.page.idle_time = 0;
                return;
            };

            hs.page.cover(false);
            hs.msg.removeAll();
            hs.page.idle_time = 0;

               

        },

        mon: function () {
            if (hs.fm.isMobile || !hs.fm.hasUiFlag(0x0080)) // idle mon not enabled
                return;
            
            $(document).on("click keydown", function () { hs.page.resetIdle(); });

            

            hs.page.intId = window.setInterval(function () {
                if (hs.page.idle_time++ > 0)
                    hs.msg.toast("Idle time: " + hs.page.idle_time, 3);

                if (hs.page.idle_time == 5) {
                    hs.msg.box("اضغط اي مفتاح للإستمرار..");
                };

                if (hs.page.idle_time >= 5) {
                    hs.page.cover(true);
                };

                if (hs.page.idle_time == 180) {
                    hs.msg.removeAll();
                    hs.msg.fix("تم تجاوز الوقت المسموح به للبقاء خاملا , <a href='" + window.location.href + "'>اضغط هنا للدخول</a>");
                    window.clearInterval(hs.page.intId);
                    hs.page.intId = null;
                };

               

            }, 5*60*1000);

        },

        isPublic:function() {
            return this.isLogin();
        },
        
        isLogin: function () {
            return this.getPath() == "/app/auth/login/";
        },

        isHome: function () {
            if (!hs.isEmpty(window.location.search.substring(1)))
                return false;
           
            return this.getPath() == "/app/";
        },

        isForm: function () {
            return this.getPath() == "/app/fms/";
        },

        getPath: function () { // path without qry or file name
            var path = window.location.pathname;
            if (!hs.isEmpty(path))
                path = path.toLowerCase().trim().replace("default.aspx", "");
            return path;
        },

        getPathQry: function () { 
            return this.getPath() + window.location.search;
        },


        getHomeUrl: function () {
            var home = window.location.protocol + "//" + window.location.hostname;
            if (!hs.isEmpty(window.location.port))
                home += ":" + window.location.port;
            return home
        },

        refresh: function (is_data, force) {

            if (arguments.length < 1)
                is_data = false;

            if (arguments.length < 2)
                force = false;

            if (!force && is_dirty) {
                hs.msg.toast("page refresh not allowed as the form is modified");
                return;
            }

            if (!force && hs.page.hasActiveDlg()) {
                hs.msg.toast("page refresh not allowed due to active dlg");
                return;
            }
           

            if (is_data)
                DoSubmit('refresh');
            else    
                if (hs.fm.isFind())
                    DoSubmit()
                else {
                    var url = window.location.href;
                    window.location.href = url;
                };
        },

        autoRefresh: function (auto_refresh_interval_sec) {
            if (auto_refresh_interval_sec != 0)
                window.setInterval(function () { hs.page.refresh(); }, 1000 * auto_refresh_interval_sec);
        },

        autoPrint: function (chk_pos) {
            var disable_pos_multi_print = true;

            if (fm_rc == 0 &&  hs.fm.getCmd() == "print" && hs.fm.getCmdMode() != "preview") {

                if (chk_pos && $(".pos-auto-print").length != 0)
                    return;
                if (disable_pos_multi_print) {
                    if (hs.fm.getId() == "es-sale-pos") {
                        window.addEventListener('afterprint', function (ev) {
                            $("body > *").remove();
                            hs.msg.box2("تمت الطباعة - أو تم إلغاء الطباعة بواسطة المستخدم");
                            window.close();//works in edge/chrome
                        });
                    };
                }


               hs.msg.info(hs.link(hs.R.print, "js:window.print()", "print-preview link_icon icon_print"));

               window.print();

                
            };

        },

        getTitle: function () {
            var t = hs.fm.title;

            if (!hs.isEmpty(t))
                return t;

            t = $("span.fm-title").text();

            if (!hs.isEmpty(t))
                return t;

            t = $("#dlg_title", parent.document.body).text();
            if (!hs.isEmpty(t))
                return t;

            t = $("a[href='#fm-tabs-0']").text();


            return t;
        },

        getReportTitle: function () {
            var t="";

            

            t += $("span.fm-title").text();

            if (!hs.isEmpty(t))
                t += " : ";
            
            t += $("table.rep_tab.rep-title").text()
            
            if (!hs.isEmpty(t))
                return t;

            t = hs.fm.title;
            if (!hs.isEmpty(t))
                return t;

            t = $("#dlg_title", parent.document.body).text();
            if (!hs.isEmpty(t))
                return t;

            t = $("a[href='#fm-tabs-0']").text();


            return t;
        },

        runUserFavCmd: function (cmd) {

            var title = hs.page.getTitle();


            if (hs.isEmpty(title))
                title = "";

            var page_url = window.location.href.replace(hs.page.getHomeUrl(), '');

            if ( cmd =='add' && hs.fm.isFind()) {
                page_url += "&init-values=y";
                $("input[type='text'], select, input[type='checkbox']").each(function () {
                    var e = $(this);
                    var eid = hs.e.getId(this);
                    if (!hs.isEmpty(eid)) {
                        var val = e.val();
                        if (val == "Y" && e.is("input[type='checkbox']") && !hs.e.isChecked(e))
                            val = '';

                        if (!hs.isEmpty(val))
                            page_url += "&i-" + hs.e.getId(this) + "=" + val;
                    }

                });
            };

            var ex_ctrl = "الرابط <br/><input class='flds round-border lft' style='width: 100%; margin: 10px auto;' id='_favUrl' name='_favUrl' type='text' value='" + page_url + "'/><br/><br/>";

            var help = "";
           
            hs.inputBox.show(false,title,  'المفضلة : ' + hs.fm.cmdTitle(cmd) , 'العنوان' , ex_ctrl, null, '', help, function (t) {

                if (cmd=='add' && hs.isEmpty(t)) {
                    hs.msg.error("العنوان مطلوب");
                    return;
                };

                page_url = escape($("#_favUrl").val().replace(hs.page.getHomeUrl(), ''));

                if (hs.isEmpty(page_url)) {
                    hs.msg.error("الرابط مطلوب");
                    return;
                }
                    

                var url = "/app/ajax/?f=user-fav&cmd=" + cmd + "&title=" + t + "&url=" + page_url
                                               

                hs.msg.wait(true);

                do_ajax(url, null, function (result, rc) {
                                      
                    
                    hs.msg.endWait();
                    hs.msg.box(result);
                });

            });




           
        },


        showClientPref: function (key) {
            var val = '';

            if (arguments.length < 1)
                key = '';

            if (hs.isEmpty(key))
                key = '';
            else
                val = hs.pref(key);
            
            

            var ex_ctrl = "القيمة <br/><input class='flds round-border lft' style='width: 100%; margin: 10px auto;' id='prf_val' name='prf_val' type='text' value='" + val + "'/><br/><br/>";

            var help = "";

            hs.inputBox.show(false, key, 'Client Pref : ' , 'الرمز', ex_ctrl, null, '', help, function (k) {

                if (hs.isEmpty(k)) {
                    hs.msg.error("الرمز مطلوب");
                    return;
                };

                var v = $("#prf_val").val();

                hs.pref(k, v);

            });





        },

        clearUserCache: function (key) {
            if (arguments.length < 1) {
                hs.cache.clear(!0);
                ShowDlg("/app/?hs-user&cmd=clear-cache&id=server");
            }
            else {
                hs.cache.removeItem(key);
                DoSubmit('refresh');
            }

            
        },

        showPageTools: function () {

            
            var cont = '';

            cont += "<a class='icon_pwd' href='javascript:ShowDlg(\"/app/fms/?fm=user-chg-pwd\")'>تغيير كلمة السر</a>";
            cont += "<a href='javascript:ShowDlg(\"/app/auth/login/?cmd=logout\")'>تسجيل الدخول بواسطة مستخدم آخر</a>";

            cont += hs.link("إضافة تذكير", "/app/fms/?fm=hs-snote&cmd=add&fk0=usr-rmnd&fk1=user", "icon_announce", "D");
            cont += hs.link("عرض التذكيرات", "/app/fms/?fm=hs-snote&fm-mode=reminders&cmd=list", "icon_announce", "_blank");
            cont += "<a href='javascript:ShowDlg(\"/app/fms/?fm=user-notifs\")'>التنبيهات</a>";
            cont += "<a href='javascript:hs.page.clearUserCache()'>تصفية الكاش الخاص بالمستخدم</a>";
           
            

            cont += hs.link(hs.L("User defaults","القيم الإفتراضية"), "/app/fms/?fm=user-tools&fm-mode=user-defs", null, "D");
            

            cont += "<b class='title'>المفضلة</b>";
            cont += hs.link("عرض المفضلة", "/app/?menu&id=app-usr-fav");
            cont += "<a href='javascript:hs.page.runUserFavCmd(\"add\")'>إضافة الصفحة للمفضلة</a>";
            cont += "<a href='javascript:hs.page.runUserFavCmd(\"del\")'>إزالة الصفحة من المفضلة</a>";
            cont += "<a href='javascript:ShowDlg(\"/app/ajax/?f=user-fav&cmd=clear\")'>تصفية المفضلة</a>";

            
            
            
            cont += "<b class='title'>اخرى</b>";
           
            
            cont += hs.link("إخفاء القائمة في أعلى الصفحة", "js:hide_page_header();$('.page_header').remove();hsEasyForm.adjustToolbarPos();");
            cont += hs.link("فتح الصفحة في نافذة جديدة", window.location.href, null, "_blank");
            cont += hs.link("عرض روابط المساعدة بجوار الحقول ", "js:hs.showHelpLinks()", "icon_help");
           
            
            cont += "<a class='icon_config' href='javascript:ShowDlg(\"/app/fms/?fm=sys-clt-admin\")'>إدارة نظام العميل</a>"

            cont += "<b class='title'>Developer</b>";
            cont += "<a href='javascript:hs.showLogs()'>Java Client Log</a>";
            cont += hs.link("Fields Technical Names", "js:hs.showFieldNames()", '');

            cont += hs.link("Copy Form", "js:hs.copyForm()", 'icon_copy')
            cont += hs.link("Paste Form", "js:hs.pasteForm()", '')
            // cont += hs.link("Print", "js:CloseDlg();window.print()", 'icon_print');
            cont += "<b class='title'>Client Pref</b>";
            cont += hs.link("Touch Screen", "js:hs.page.showClientPref('touch')");
            cont += hs.link("Change Client Preference Value", "js:hs.page.showClientPref()");

            hs.ui.showToolsDlg(cont);
                 
        },

        cover: function (b) {
            if (arguments.length == 0) b = !0;

            if (b)
                $("<div class='full-screen page-cover close' style='background-color:#888;opacity:0.5;z-index:1009'></div>").appendTo("#page").closeableBox();
            else 
                $(".page-cover , .msg-fixed").hide().remove();
            
        },

        noFooter: function () {
            $(".page_footer").hide();
            $("body").css("margin-bottom", "0");
            $("div.full-screen-box").css("bottom", "0");
        },

        addTools: function (t, on_tb) {

            if (arguments.length < 2)
                on_tb = false;

            var page_menu;

            if (on_tb) 
                page_menu = $("div.fm-tb");
            else {
                page_menu = $("div.fm-tb #fm-tm");
                if (page_menu.length == 0) {
                    page_menu = $("<span class='popup-box has-menu dir-flt'><a href='javascript:popup(\"fm-tm\")' class='link_icon_only icon_tools' >&nbsp;</a><span id='fm-tm' class='popup-menu'></span></span>")
                        .appendTo("div.fm-header div.fm-tb").find("span.popup-menu");
                };
            };
            
            var cont = page_menu.html();
            cont += t;
            page_menu.html(cont);
        },
               

        addContent:function(s) {
            var e= $("#fm_results");
            e.html(e.html() + s);
        },

        hasActiveDlg: function () {
            return hs.e.exists("dlg_frame");
        }


    },


    // ****************** //


    e: { // element

        $: function (_e,_p) { //  get jQry obj of the elem
            if (arguments.length < 2) _p=null;


            if ("string" != typeof _e)
                return $(_e);
            else {
                if (_e.startsWith("#"))
                    return $(_e, _p);
                else
                    return $("#" + _e, _p);
            };
        },

        onkey: function (e, k, handler) {
            e=h$(e);

            $(e).keydown(function (ev) {
                if (ev.which === k) { 
                    if (handler(e, ev))
                        hs.absorb(ev);
                };
            });

        },

        getId: function (_e) {
            var obj = h$(_e);
            var id = $(obj).attr("id");
            if (hs.isEmpty(id))
                id = $(obj).attr("name");

            if (hs.isEmpty(id))
                return null;
            
            return id;
        },

        getElem: function (e) {
            e = h$(e);
            return document.getElementById(hs.e.getId(e));
        },

        data:function(e, key, val)
        {
            if (arguments.length == 3)
                return h$(e).attr("data-" + key, val);
            else
                return h$(e).attr("data-" + key);
        },

        ph: function (e, val) { // set/get placeholder
            if (arguments.length == 2)
                return h$(e).attr("placeholder", val);
            else
                return h$(e).attr("placeholder");
        },
        
        cfg: function (_e, cfg_id, cfg_val) {
            var fld_cfg = h$(_e).attr("data-cfg");

            // enhance_me
            if (arguments.length == 3) { // set 
                if (hs.isEmpty(fld_cfg))
                    fld_cfg = "";
                else
                    fld_cfg += ";";
                fld_cfg += cfg_id + "=" + cfg_val + ";";
                h$(_e).attr("data-cfg", fld_cfg);
                // log("fld cfg=" + fld_cfg);
                return cfg_val;
            };

            // get

            if (hs.isEmpty(fld_cfg))
                return null;

            var cfg_arr = fld_cfg.split(';');
            
            for (var i = 0; i < cfg_arr.length; i++) {
                var one_cfg = cfg_arr[i].split('=');
                if (one_cfg[0] === cfg_id) 
                    return one_cfg[1] === undefined ? "" : hs.unescapeKeyVal(one_cfg[1]);
            };

            return null; // not found

        },

        help: function (e) {
            var help_id = hs.e.cfg(e, "he");
            if (help_id == "0")
                help_id = hs.e.getId(this);

            hs.help(help_id, hs.fm.getId(), true);
        },

       
               

        datepicker: function (_e, cmd, ym) {
            _e = h$(_e);

           // if (hs.fm.isFind() || hs.fm.isGrid())
             //   _e.width(100);

            _e.addClass("fix-size");

            if (cmd == 1) { // setup
                var id = hs.e.getId(_e);
                if (!_e.hasClass("no-icon"))
                    hs.e.inlineTools(_e, "<a tabindex='-1' style='margin:0;' class='link_icon_only icon-cal qv' href='javascript:hs.e.datepicker(\"" + id + "\",2)'>&nbsp;</a>", 0);
            }

            if (cmd == 2) { // show
                if (arguments.length < 3)
                    ym = false;

                if (hsUiLang == "ar")
                    $(_e).datepicker({
                        changeMonth: ym,
                        changeYear: ym,
                        firstDay: 6,
                        dateFormat: hsUiDateFormat,
                        monthNames: ["يناير", "فبراير", "مارس", "ابريل", "مايو", "يونيو", "يوليو", "اغسطس", "سبتمبر", "اكتوبر", "نوفمبر", "ديسمبر"],
                        monthNamesShort: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"],
                        dayNamesMin: ["ح", "ن", "ث", "ر", "خ", "ج", "س"]

                    })
                else
                    $(_e).datepicker({
                        firstDay: 6,
                        dateFormat: hsUiDateFormat,
                        changeMonth: ym,
                        changeYear: ym
                    });

                _e.attr("autocomplete", "off").focus();

                if (hs.fm.isMobile && cmd > 1)
                    $(_e).blur();
            }

           
            
            $(_e).keydown(function (e) {
                if (e.which === K.F3) {
                    hs.e.datepicker(this, 2, ym)
                    hs.absorb(e);
                }}).dblclick(function () { hs.e.datepicker(this, 2, ym); }).addClass('ui-date')
            
            ;

            

        },

      

        focus: function (e) {
            h$(e).focus();
        },

        click: function (e, focus) {
            if (arguments.length < 2)
                focus = false;

            e = h$(e);

            if (e.is('a'))
                hs.clickLink(e);
            else {
                if (focus)
                    e.focus();

                e.trigger('click');
            };
        },

        dblclick: function (e, focus) {
            if (arguments.length < 2)
                focus = false;

            e = h$(e);

            if (focus)
                e.focus();
            
            e.trigger('dblclick');
        },
        
        isReadonly: function (e) {
            return h$(e).attr('readonly') == 'readonly' || hs.e.isDisabled(e);
        },

        isDisabled: function (e) {
            return h$(e).attr('disabled') == 'disabled';
        },
        
        isFocusable: function (e) {
            e = h$(e);
            return !(e.length == 0 || !e.is(":input, a") || e.is(":hidden, :disabled, [readonly='readonly']"));
        },

        isInView:function(e) { // check if e is visible in view port
            e = h$(e);
            var docViewTop = $(window).scrollTop();
            var docViewBottom = docViewTop + $(window).height();

            var elemTop = $(e).offset().top;
            var elemBottom = elemTop + $(e).height();

            return ((elemBottom <= docViewBottom) && (elemTop >= docViewTop));
        },
                

        center: function (e, cover) {
            h$(e).show().position({
                my: "center",
                at: "center",
                of: "#page"
            });//.remove().appendTo("#page");
        },
               

        enable: function (e, en) {
            if (arguments.length < 2)
                en = !0;

            e = h$(e);
            if (e.is("input"))
                e.attr('readonly', en ? null : 'readonly')
            else {
                if (en)
                    $(e).prop('disabled', !1).removeClass('disabled');
                else
                    $(e).prop('disabled', !0).addClass('disabled');
            };

            return e;
        },
        
        disable: function (e) {
            return hs.e.enable(e, !1);
        },

        toggleEnable: function (e) {
            hs.e.enable(e,hs.e.isReadonly(e));
        },

        setTip: function (e, tip) {
            h$(e).attr('title', tip);
        },

        delayHide: function (e, ms) {
            e = h$(e);
            window.setTimeout(function () { e.hide(); }, ms);
        },

        isVisible: function (e) {
            return h$(e).is(':visible');
        },

        isChecked: function (e, en) {
            if (arguments.length < 2)
                return h$(e).prop('checked');

            h$(e).prop('checked', en ? 'checked' : null);
            return en;
        },

        hasValue: function (e) {
            return !hs.isEmpty(h$(e).val());
        },

        exists: function (e) {
            return h$(e).length > 0;
        },

        loading: function (e, ena) {
            if (arguments.length < 2) ena = 1;
            if (ena==1)
                h$(e).addClass('loading');
            else
                h$(e).removeClass('loading');
        },

        isBusy: function (e) {
            return h$(e).hasClass("loading");
        },
        

        angry:function(e) {
            h$(e).css({ position: 'relative' })
                    .animate({ left: '+=25px' }, 'fast')
                    .animate({ left: '-=25px' }, 'fast')
                    .animate({ left: '+=25px' }, 'fast')
                    .animate({ left: '-=25px' }, 'fast');
        },

        enable_focus_next: function (e) {
            h$(e).on("keydown", function (ev) {
                if (!ev.ctrlKey && ev.which === 13 && !$(this).is("textarea, :button, :submit")) {
                    hs.absorb(ev);
                    if (!$(this).is(".no-enter"))
                        focus_next(this);
                };
            });
        },

        checkbox: function (id, title, val, selected, disabled, extra_attribs) { // render checkbox

            if (hs.isEmpty(val))
                val = "Y";

            return "<span style='display: inline-block; width: 300px;'><input type='checkbox' id='{0}' name='{0}' value='{1}' {3} {4} {5}/><label for='{0}'>{2}</label></span>".f(
            id, //0
            val, // 1
            title, // 2

            selected ? "checked='checked' " : null, // 3
            disabled ? "disabled='disabled' " : null, // 4
            extra_attribs // 5
            );
        },

        inputbox: function (id, title, val, width, css_class, disabled, ex_attribs, style) { // render input box

            

            if (width <= 0)
                width = 200;
            
            return "<input name='{0}' id='{0}' type='text' value='{1}' class='flds {5}' style='width: {2}px;{4}' {3} {6} placeholder='{7}'/>".f(
                     id, // 0
                     val,  // 1
                     width,
                     disabled ? " readonly='readonly' " : "",
                     style, // 4
                     css_class, // 5
                     ex_attribs, // 6
                     title // 7
                );



        },

        setupInputBox: function (e, tit) {
            e = h$(e);
            e.on("dblclick", function () {
                hs.inputBox.show($(e).is("textarea"), $(this).val(), tit, '', '', null, '', '', function (t) {
                    if (!hs.e.isReadonly(e))
                        $(e).val(t);
                });
            });
        },
      
        showInputBox: function (e, tit) {
            e = h$(e);
            
                hs.inputBox.show($(e).is("textarea"), $(e).val(), tit, '', '', null, '', '', function (t) {
                    if (!hs.e.isReadonly(e))
                        $(e).val(t);
                });
            
        },

        setupTools: function (e, w, t) {

            var after_e = false;

            e = h$(e);

            if (e.length < 1)
                return;

            var t_id = hs.e.getId(e) + "_tools";
            var t_e = h$(t_id);
            if (t_e.length > 0) {
                t_e.html(t_e.html() + t);
                return;
            };

           
            

            // if (!hs.e.isVisible(e))
            //   return; // some flds hidden in tabs

            if (w < 200)
                w = 200;

            var h = e.height();

            if (h < 24) h = 24;

            var icon_css = "fld-tools-icon";
            var icon_width = 28;

            var is_checkbox = $(e).is("input[type='checkbox']");

            if (is_checkbox)
                after_e = true;
                           

            var html = "<span style='padding:0; margin:0; position:relative;top:-3px; xz-index:20;' class='popup-box'><a tabindex='-1' style='height:{2}px; width:{3}px;padding: 0;margin:0;' class='link_icon_only {4}' href='javascript:noop()'>&nbsp;</a><span id='{5}' class='popup-menu' style='top:20px;width: {1}px'>{0}</span></span>".format(t, w, h, icon_width, icon_css, t_id);

            if (after_e) {
                var mnu = $(html).insertAfter(e);

                icon_width += 2;
                //  if (e.is("SELECT"))
                // icon_width = 0;

               

                if (is_checkbox) {
                    //$(e).css("margin-left", icon_width.toString() + "px");
                }
                else
                    $(mnu).css({ left: icon_width });
            }
            else {
                // before e
                var mnu = $(html).insertBefore(e);

                icon_width += 2;

                if (hs.isRTL())
                    $(mnu).css({ left: -icon_width }).css("margin-right", "-28px");
                else 
                    $(mnu).css({ right: -icon_width }).css("margin-left", "-28px");
                

               // icon_width += 5;

               

                if (hs.isRTL())
                    $(e).css("padding-right", icon_width.toString() + "px");
                else
                    $(e).css("padding-left", icon_width.toString() + "px");

               
            };

        },

        setupTools2: function (e,w, t) {
            var after_e = false;

            e = h$(e);

            if (e.length < 1)
                return;

            if (w < 200)
                w = 200;

            var h = e.height() + 2;

            var icon_css = "fld-tools-icon";
            var icon_width = 28;

            var tools_div = $("<div style='position:relative;display:inline-block;margin-top:2px;' class='fld-tools'></div>").insertAfter(e);

            e.remove().appendTo(tools_div);

            t = "<span class='tip-arrow' style='top:-12px; margin:auto 12px;'>&nbsp;</span>" + t;

            var html = "<span style='position:absolute;' class='popup-box'><a tabindex='-1' style='height:{2}px; width:{3}px;padding: 0;margin:0' class='link_icon_only {4}' href='javascript:noop()'>&nbsp;</a><span class='popup-menu tb' style='z-index: 20; top: 20px;width: {1}px'>{0}</span></span>".format(t, w, h, icon_width, icon_css);

            



            if (after_e) {
                var mnu = $(html).insertAfter(e);
                $(mnu).css({ left: 0 });
            }
            else {
                // before e
                var mnu = $(html).insertBefore(e);

                icon_width += 2;

                icon_width += 5;

                $(e).css(hs.isRTL() ? "padding-right" : "padding-left", icon_width.toString() + "px");
                
            };

        },

        //  hs.e.inlineTools("net_amount", "<a tabindex='-1' style='margin:0;' class='link_icon_only icon_refresh qv' href='javascript:es_doc_recalc()'>&nbsp;</a>", 0,!0);

        inlineTool: function (e, url, _tit, _icon, t_pos, auto_hide) {
            if (arguments.length < 5)
                t_pos = 0;//over

            if (arguments.length < 6)
                auto_hide = !0;

            if (url.startsWith("js:")) url = url.replace("js:", JS);

          //  if (_tit == null) _tit = "";
           // if (_icon == null) _icon = "";

            var t = "<a title='{0}' tabindex='-1' href='{1}' style='margin:0;' class='link_icon_only qv {2}'>&nbsp;</a>".f(_tit, url, _icon);
            

            hs.e.inlineTools(e, t, t_pos, auto_hide);
        },

        inlineTools: function(e, t, t_pos, auto_hide) {
            // t_pos=> -1 : before, 0 : over, 1 : after

            if (arguments.length < 3)
                t_pos = 1;

            if (arguments.length < 4)
                auto_hide = false;
        
            e = h$(e);

            if (e.length < 1)
                return;
            
            var h = e.height() + 2;

            var icon_css = "fld-tools-icon";
            var icon_width = 28;

            var tools_div = $("<div style='position:relative;display:inline-block;background-color:#fff;' class='fld-tools'></div>").insertAfter(e);

            if (hs.fm.isMobile)
                tools_div.css("width", "auto");
                        
            e.detach().appendTo(tools_div);

            if (auto_hide || t_pos == 0) {
                var tid = hs.e.getId(e) + "-ilt";
                $(e).css("padding-left", "28px");
                t = "<span id='{0}' style='position: absolute; margin-top: 3px; left: 0; top:0; {2}'>{1}</span>".f(tid, t, auto_hide ? "display: none; z-index:100" : "");
                                
                if (auto_hide)
                    $(e).hover(() => h$(tid).show(), () => delayHideElm(tid, 3000));
            }

            if (t_pos == -1)
                $(t).insertBefore(e);
            else
                $(t).insertAfter(e);

        }

    }, // e

   


    // ****************** // 


    fm: {
        msg: null,
        id: null,
        title:null,
        url: null,
        rc: 0,
        curOfflinekey: null, // remove if submitted
        options: null, //{flags:0}
        isMobile: false,
        isActivity: false, // is activity form
        fld_tools: true,
        // --- ex Handlers
        onLoad: null,
        onCaching: null,
        onSubmit: null,

        select_dlg_callback: null,
        // ----------------

        Flags : {
            EasyEntry: 0x10000000
        },

        // -------------------- //

        dirty: function (d) {
            if (arguments.length < 1)
                return is_dirty;

            if (is_dirty == d)
                return; // already set

            // hs.msg.toast("dirty:" + d);
            
            is_dirty = d;
            if (window != window.parent) {
                // parent.is_dirty = d;
                parent.hs.fm.dirty(d);
            
            }
        },

        getId: function () {
            return getUrlParameter("fm") || '';
        },

        itemId: function () {
            return getUrlParameter("id");
        },

        getMode: function () {
            return getUrlParameter("fm-mode") || '';
        },

        
       
        // ------------------
        getFormUrl: function () {
            return "/app/fms/?fm=" + this.getId() + (hs.isEmpty(this.getMode()) ? '': "&fm-mode=" + this.getMode());
        },


        getCmd: function () {
            var cmd = getUrlParameter("cmd");
            if (hs.isEmpty(cmd))
                cmd = "home";
            return cmd;
        },

        getCmdMode: function () {
            return getUrlParameter("cmd-mode");
        },

        isCmd: function (_cmd) {
            return hs.fm.getCmd() == _cmd;
        },

        isHome: function () {
            return hs.fm.getCmd() == "home";
        },

        isFind: function () {
            return hs.fm.getCmd() == "find";
        },

        isList: function () {
            return hs.fm.getCmd() == "list";
        },

        isGrid: function () {
            return hs.fm.getCmd() == "grid";
        },

        isAdd: function () {
            return hs.fm.getCmd() == "add";
        },

        isEdit: function () {
            return hs.fm.getCmd() == "edit";
        },

        isView:function() {
            return hs.fm.getCmd() == "view";
        },

        isPrint: function () {
            return hs.fm.getCmd() == "print";
        },

        isEntry:function() {
            return hs.fm.isAdd() || hs.fm.isEdit() || hs.fm.getId() == "sys-cfg";
           
        },

        isEntryOrActivity: function () {
            return hs.fm.isActivity ||  hs.fm.isAdd() || hs.fm.isEdit() || hs.fm.getId() == "sys-cfg";

        },

        isBatch: function () {
            return getUrlParameter("batch-mode") == "yes";
        },

        isCfg : function() {
            return hs.fm.getId() == "sys-cfg";
        },

        getHelpFmId: function () {
            var id = hs.fm.getId();
            if (id != "sys-cfg")
                return id;

            var iid = hs.fm.itemId();
            if (hs.isEmpty(iid))
                iid = id;

            return id + "." + iid;
        },

        isItem: function () {
            return hs.fm.isEntry() || hs.fm.isView();
        },
        
       

        hasFlag:function(f)
        {
            if (hs.fm.options == null)
                return false;

            return (hs.fm.options.flags & f) == f;
        },

        // global forms options
        hasUiFlag: function (f) {
            if (hs.fm.options == null)
                return false;

            return (hs.fm.options.ui & f) == f;
        },

        // -----

        allowToLeave: function () {
            hs.fm.dirty(false); // allow to leave the page
        },

        serialize: function () { // to url encoding
            enableSubmitDisabled(true);
            var data = $(document.forms[0]).serialize();
            enableSubmitDisabled(false);
            return data;
        },

        /*
        serializeToJSON: function (return_obj) {
            var obj = $(document.forms[0]).serializeToJSON({parseBooleans: false, associativeArrays:false});
          //  console.log(obj);

            if (return_obj)
                return obj;

            obj.__VIEWSTATE = "";
			
            var jsonString = JSON.stringify(obj,
                             function (key, value) { if (hs.isEmpty(value)) return undefined; return value; });
    
            return jsonString;
        },
        */

        serializeToJSON: function () {

            var arr = $(document.forms[0]).serializeArray();
            var res = "{";
            jQuery.each(arr, function (i, fld) {
                var val = fld.value;
                if (!fld.name.startsWith("__") && !hs.isEmpty(val)) {

                    if ($("#"+fld.name).hasClass("ex-list"))
                        val = hs.fm.val(fld.name);

                    res += JSON.stringify(fld.name) + ":" + JSON.stringify(val) + ",";
                }
            });

            res += JSON.stringify("fm_rc") + ":" + JSON.stringify("0") + "}";

            return res;
        },

        refreshData: function () {
            DoSubmit('refresh');
        },

        //  ShowDlg('/app/fms/?fm=es-biz-doc&fm-mode=&cmd=find&cmd-mode=select&select-target=src_doc');

        selectDlg: function (fm, fm_mode, target, callback, sel_fld, cmd) {
            if (arguments.length < 4) callback = null;
            if (arguments.length < 5) sel_fld = null;
            if (arguments.length < 6) cmd = 'find'; //

            

            var url = '/app/fms/?fm={0}&cmd={2}&cmd-mode=select&select-target={1}{3}'.f(fm, target, cmd, sel_fld != null ? "&select-field=" + sel_fld : '');

            if (!hs.isEmpty(fm_mode))
                url += "&fm-mode=" + fm_mode;
            
            hs.fm.select_dlg_callback = callback;

            ShowDlg(url);

        },


        onFldDblClick: function (e) {

            hs.msg.toast("dbl click");

            var fm_id = hs.e.cfg(e, "fm");
            if (!hs.isEmpty(fm_id))
                hs.fm.viewFieldItem(e, fm_id, hs.e.cfg(e, "md"));
        },

       

        viewFieldItem: function (efld, _fm, _fm_mode) {
            var fld_key = hs.e.getId(efld);
            var id = hs.fm.val(fld_key);

            if (!hs.isEmpty(id)) {
                var url = "/app/fms/?fm={0}&fm-mode={1}&cmd=view&id={2}".format(_fm, _fm_mode, id);
                ShowDlg(url);
            }
            else {
                hs.msg.tip(fld_key, hs.R.fld_is_blank, 5);
            }
        },
        cloneFieldItem: function (efld, _fm, _fm_mode) {
            var fld_key = hs.e.getId(efld);
            var id = hs.fm.val(fld_key);

            if (!hs.isEmpty(id)) {
                var url = "/app/fms/?fm={0}&fm-mode={1}&cmd=add&clone={2}&cmd-mode=select&select-target={3}".format(_fm, _fm_mode, id, fld_key);
                ShowDlg(url);
            }
            else {
                hs.msg.tip(fld_key, hs.R.fld_is_blank, 5);
            }
        },

        // -----------
        hideFieldRow: function (fld_id) { // this would hide the follower flds
            $("#r_" + fld_id).hide();
        },

        addFieldTools: function (efld, _fm, _fm_mode, isV2, _extra_tools) {
            
            if (!hs.fm.fld_tools || hs.fm.isMobile)
                return;


            // if (!(hs.fm.isEntry() || hs.fm.isView()))
            //   return;

            var fld_key = hs.e.getId(efld);

            if (arguments.length < 3 || _fm_mode==null)
                _fm_mode = '';

            if (arguments.length < 4)
                isV2 = false;

            if (arguments.length < 5)
                _extra_tools = '';
                
            var FT_FMT = "<a tabindex='-1' class=' icon_{4}' href=\"javascript:ShowDlg('/app/fms/?fm={1}&fm-mode={2}&cmd={3}&cmd-mode=select&select-target={0}')\">{5}</a>";
            var fld_tools = '';

            if ($(efld).is(".en-ms-tool")) // enable multi-select tool
            {
                fld_tools += "<a tabindex='-1' class=' icon-{4}' href=\"javascript:ShowDlg('/app/fms/?fm={1}&fm-mode={2}&batch-mode=yes&cmd-mode=select&cmd=find&ret-sel-to={0}')\">{5}</a>".format(fld_key, _fm, _fm_mode, null, "checklist", "إختيار");
            }
            else {

                fld_tools += "<a tabindex='-1' class=' icon_{4}' href=\"javascript:hs.fm.viewFieldItem('{0}','{1}','{2}')\">{5}</a>".format(fld_key, _fm, _fm_mode, null, "qv", "عرض");
                fld_tools += FT_FMT.format(fld_key, _fm, _fm_mode, "find", "find", "بحث");
                fld_tools += FT_FMT.format(fld_key, _fm, _fm_mode, "list", "list", "القائمة");
                fld_tools + "<hr/>";
                fld_tools += FT_FMT.format(fld_key, _fm, _fm_mode, "add", "add", "إضافة جديد");
                fld_tools += "<a tabindex='-1' class=' icon_{4}' href=\"javascript:hs.fm.cloneFieldItem('{0}','{1}','{2}')\">{5}</a>".format(fld_key, _fm, _fm_mode, null, "copy", "نسخ");
            };

            
          //  fld_tools +=  string.Format("/app/fms/?fm={0}&fm-mode={1}&batch-mode=yes&cmd-mode=select&cmd=find&ret-sel-to={2}", fm_id, fm_mode, fld_def.key)

            fld_tools += _extra_tools;
           

            if (isV2)
                hs.e.setupTools2(fld_key, 600, fld_tools);
            else
                hs.e.setupTools(fld_key, 300, fld_tools);

            // $(efld).on("dblclick", function () { hs.fm.onFldDblClick(this) });

            return fld_tools;
        },                
        

        deserialize: function(sdata) { // qs data

            hs.fm.reset();

            var sPageURL = decodeURIComponent(sdata),
               sParamsArr = sPageURL.split('&'),
               sParam,
               sValue,
               sName,
               $fld,
               i;

            for (i = 0; i < sParamsArr.length; i++) {
                sParam = sParamsArr[i].split('=');
                sName = sParam[0];
                sValue = sParam[1];

                if (sName.startsWith("__"))
                    continue; // avoid change view state and others

                $fld = $("#" + sName);

                if ($fld.length == 0)
                    continue;

                sValue = sValue.replace(/\+/g, ' ');

                $fld.removeClass("ui-changed");

                if ($fld.is(".ex-list")) { 
                    if (!hs.isEmpty(sValue)) 
                        $fld.attr("data-sel_id", sValue.split(' : ')[0]).attr('placeholder', '');
                    else 
                        $fld.attr("data-sel_id", '').attr('placeholder','');
                };


                if ($fld.is("select") && $fld.find("option[value='" + sValue + "']").length == 0) {
                    hs.list.addItem($fld, sValue, sValue);
                };

                $fld.val(sValue);

                if (!hs.isEmpty(sValue))
                    $fld.addClass("ui-changed"); 

            }; // for


            $(".ui-changed").trigger("blur");
        },


        
        reset: function () {
            act("fm.reset started");

            is_page_loaded = false;
            hs.fm.dirty(false);
            document.forms[0].reset();
          
            $(".ex-list").attr("placeholder", "").attr("data-sel_id","");

            if ((typeof document._on_form_reset) !== "undefined")
                document._on_form_reset();
                      
            if (hs.fm.onLoad != null)
                hs.fm.onLoad();
                                  
            focus_first();
            is_page_loaded = true;

            act("fm.reset completed");
        },

        // ---------------------
        
        
        exListValue: function (_key, _val, _title, _parent, _is_new) {
            var al = arguments.length;
            var is_set_val = al >= 2;
            
            if (al < 4)
                _parent = null;

            if (al < 5)
                _is_new = false;


            var $fld = h$(_key, _parent);

            if ($fld.length <= 0)
                return null;

            var $sel = null;
            var $inp = null;
                        

            // selectOrInput
            if ($fld.is("select"))
            {
                $sel = $fld;
                $inp = h$($fld.attr("data-sel-inp-id"), _parent);
            }
            else
            {
                $inp = $fld;
                $sel = h$($fld.attr("data-inp-sel-id"), _parent);
                if ($sel.length == 0)
                    $sel = null;
            };

            if (is_set_val) { // set

                if (al < 3)
                    _title = _val;

                if ($sel != null) {
                    if (!hs.isEmpty(_val)) { 
                        var opt = $sel.find("option[value='" + _val + "']");
                        if (opt.length == 0)
                            $sel.prepend($('<option />', _parent).val(_val).html(_title));
                        else
                            _title = opt.text();
                    };

                    $sel.val(_val);

                
                    
                };
                
                if (!hs.isEmpty(_val))
                    $inp.attr("data-sel_id", _val).attr('placeholder', '').val(_val + ' : ' + _title);
                else
                    $inp.attr("data-sel_id", '').attr('placeholder', '');

                
                

            }
            else  { // get
                

                if ($sel != null)
                    _val = $sel.val();
                else
                    _val = $inp.attr("data-sel_id");

                return _val;

            };


              
        },

        val: function (_key,_val) {
            var al = arguments.length;
            var $f = h$(_key);

            if ($f.length == 0)
                $f = $("#_" + _key);//hidden

            if ($f.length <= 0)
                return null;

            if ($f.is("input[type='checkbox']")) {
                if (al == 2)
                    hs.e.isChecked($f, !hs.isEmpty(_val)); 

                return hs.e.isChecked($f) ? $f.val() : '';
            };


            // ExList
            if ($f.is(".ex-list, .ui-exlist")) {
                if (al == 2) 
                    return hs.fm.exListValue(_key, _val);
                else
                    return hs.fm.exListValue(_key);
            };
            // -------
            
            
            if (al == 2) {
                $f.val(_val);
                return _val;
            }
            else
                return $f.val();
        },

        // get/set offline mode status: offline(bool)
        isOffline: function (_enable) { 
            var offline_mode_key = "offline_mode";

            // set
            if (arguments.length > 0) {
                if (_enable) {
                    hs.cache.setItem(offline_mode_key, _enable);
                    hs.msg.toast("تم التحويل إلى وضع عدم الإتصال");
                }
                else {
                    hs.cache.removeItem(offline_mode_key);
                    hs.offline.sendAll(false);
                    hs.msg.toast("تم العودة إلى وضع الإتصال");
                };

                return;
            };
            
            // get
            _enable = hs.cache.getItem(offline_mode_key);
            if (_enable == null)
                return false;

            return _enable;
        },

      
        clearCurrentOfflineForm: function () {
            hs.offline.remove(hs.fm.curOfflinekey);
            hs.fm.curOfflinekey = null;
        },

        // ---------------------

        selectOrInput: function (sel_id, inp_id, _on_item_changed) {
            
            if (hs.fm.isFind()) {
                $(".fld-tools > div.tools").hide();
                $("#" + inp_id).css("width", "200px");
                $("#" + sel_id).css("width", "400px");
                hs.select.toExList(sel_id);
                return;
            }

            //  if (hs.fm.isMobile) return; // cmntd: 18.5.2019

            var $inp = h$(inp_id);
            var $sel = h$(sel_id);

            if (hs.fm.isEntry() && hs.e.isDisabled($sel)) {
                
                hs.e.disable($sel);
                h$(inp_id).hide();
                return;
            };

            var code_arr = hs.select.getOptionCodes(sel_id);

            $sel.attr("data-sel-inp-id", inp_id).hide();
            $inp.attr("data-inp-sel-id", sel_id);
            $inp.addClass("ui-exlist");
            hs.fm.hideFieldRow(sel_id);
            
            $inp.exList({
                codelist: code_arr,
                selectListId: sel_id,
                select_only: false,
                on_item_changed: _on_item_changed
            });

            var code = $sel.val();

            if (!hs.isEmpty(code))
                $('#' + inp_id).attr('data-sel_id', code).exList('select');

        },

        applyFieldsConfig: function () {

            $("input, select").each(function () {
                var fld = $(this);
                var fld_fm = hs.e.cfg(fld, "fm");
                
                if (!hs.isEmpty(fld_fm)) 
                    hs.fm.addFieldTools(this, fld_fm, hs.e.cfg(fld, "md"));
               

                var help_id = hs.e.cfg(fld, "he");
                if (!hs.isEmpty(help_id)) {
                    if (help_id == "0") help_id = hs.e.getId(this);
                    hs.e.setupTools(this, 300, "<a class='icon_help' href='javascript:hs.help(\"{0}\")'>{1}</a>".f(help_id, hs.R.help));
                }

            });
        },

        cmdTitle: function (cmd) {
            switch (cmd) {
                case "add": return "إضافة";
                case "del": return "حذف";

                case "view": return "عرض";
                case "edit": return "تعديل";
                case "list": return "عرض القائمة";
            }

            return cmd;
        },

        getData: function (fm_id, fm_mode, id, callback) {
            var url = "/app/fms/?ajax=y&json=y&cmd=view&fm=" + fm_id + "&id=" + id + "&fm-mode=" + fm_mode;
           

            return hs.ajax.getJSON(url, "loading:" + url, callback);

        }

    }, // fm


    // ******************** // 

    list: {

       getCodeName : function (items, code) { // items=array
            for (var i = 0; items != null && i < items.length; i++) {
                if (items[i][0] == code)
                    return items[i][1];
            };

            return null;
        },

        val: function (e, value) {
            e = h$(e).val(value);
            h$(hs.e.getId(e) + "_inp").val(value);
        },

        exValue: function (e) {
            e = h$(e);
            return $(e).find("option:selected").first().attr("data-ex-val");
        },

        empty: function (e) {
            e = h$(e).empty().append($('<option />'));
            h$(hs.e.getId(e) + "_inp").val('');
        },

        addItem: function (e, val, text) {
            h$(e).append($('<option />').val(val).html(text));
        },

        addTitle: function (e, title) {
            h$(e).append($("<option />").val('').html(title).addClass('ui-tit')); // ex-list does not show blanks
        },

        fill: function (e, data, show_codes) {
            if (arguments.length < 3)
                show_codes = 0;

            if ("string" == typeof data)
                data = hs.cache.getItem(data);

            var cl = new CodeList(data);
            cl.fillSelectList(e, show_codes);
        },

        exFindTimer: null,

      

        exFindCode: function (eList, key) {
            
            //hs.msg.toast(key);

            if (hs.isEmpty(key))
                return null;

            var nKey = hs.safe.parseInt(key);
            
            var opts = $(eList).children();
            var i;

            for (i = 0; i < opts.length; i++) {
                var opt_val = $(opts[i]).val();
                var opt_tit = $(opts[i]).html();

                if (opt_val == key || (nKey != 0 && hs.safe.parseInt(opt_val) == nKey))
                    return opt_val;

                // hs.msg.toast(opt_val);

                
            };

            return null; // no match found
        },

        onCodeChange: function (inpE, selId) {
            hs.msg.toast(selId);

            var inp_val = $(inpE).val();

            var code = hs.list.exFindCode($("#" + selId), inp_val);
            if (code != null) {
                $("#" + selId).val(code).trigger('change');
                $(inpE).val(code);
            }
            else {
                $("#" + selId).val(inp_val);
                if (!hs.isEmpty(inp_val))
                    hs.msg.tip(inpE, 'تاكد من القيمة المدخلة');
            }

        },

        exFind: function (eList, key) {
            
          //  hs.msg.tip(eList, key,10,50,!1,!0);
           

            var code = hs.list.exFindCode(eList, key);
            if ( code != null) {
               // hs.msg.toast("Match:" + code);
                $(eList).val(code).trigger('change');
                return 1; // found
            }


            var opts = $(eList).children();
            var i;

            for (i = 0; i < opts.length; i++) {
                var opt_val = $(opts[i]).val();
                var opt_tit = $(opts[i]).html();

                if (opt_tit.startsWith(key)) 
                    return 0;
                

                                               
                if (opt_val.startsWith(key)) {
                    hs.msg.tip(eList, opt_val);
                    return 0;
                }
            };

            // contains
            for (i = 0; i < opts.length; i++) {
                var opt_val = $(opts[i]).val();
                var opt_tit = $(opts[i]).html();

                if (opt_tit.contains(key)) {
                    $(eList).val(opt_val);
                    return 0;
                };



                if (opt_val.contains(key)) {
                    $(eList).val(opt_val);
                    return 0;
                }
            };

            return -1; // no match found
        },

        addExInputBox: function (e) {
            e = h$(e);
            var sel_id = hs.e.getId(e);
            var inp_id = sel_id + "_inp";
            var ex_attrs = "";

            if (hs.e.isReadonly(e))
                ex_attrs = "readonly='readonly'";

            var tools = "<input id='{3}' class='flds fix-size' type='text' style='width:70px; background-color:#def; color:#000; text-align:center;font-weight:bold;' value='{1}' {2} onchange='hs.list.onCodeChange(this,\"{0}\")'></input>".f(sel_id, $(e).val() || '', ex_attrs, inp_id);

            hs.e.inlineTools(e, tools, -1);

            focus_insert(sel_id, inp_id, true);


            $(e).on("change", function () {
                $("#" + hs.e.getId(this) + "_inp").val($(this).val() || '');
            });
        },

        setupExFindAll: function () {
            if (hs.fm.hasUiFlag(0x00800000)) { // flag=attach input box
                $("SELECT").each(function () {

                    if (!$(this).is(".ex-list,.no-exfind")) {
                        hs.list.addExInputBox(this);
                    };
                    
                    hs.list.SetupExFind(this);
                });

                return;
            } else
                $("SELECT.ex-find").each(function () {
                    hs.list.addExInputBox(this);
                });
                      

            $("SELECT").each(function () {
                hs.list.SetupExFind(this);
            });


            
       
        },

        setInput: function (e, s) {
            $(e).attr("data-match", s);
            hs.msg.tip(e, s, 120, 80, !1, !1);
        },


        SetupExFind: function (e) {

            e = h$(e);

            if (hs.fm.isFind()) {
                /*hs.list.addItem(e, "", " ");
                hs.list.addItem(e, "*", "يجب أن يحوي قيمة");
                hs.list.addItem(e, "!", "فارغ - لا يحوي قيمة");*/

                // works but needs more UI improve
              //  if (!$(e).is(".ex-list") && hs.fm.getCmdMode() == "advanced")
                 //   e.addClass("multi").attr("multiple", "multiple"); // convert to multi-select
            };

            $(e).on("keypress", function (ev) {
               
                if (isControlKey(ev.which))
                    return;

                var match = $(this).attr("data-match") || '';
                match = match + String.fromCharCode(ev.which)
                var res = hs.list.exFind(this, match);
                if (res == -1) {
                    match = String.fromCharCode(ev.which);
                    res = hs.list.exFind(this, match);

                };

             //   if (res != 0) match = '';

                if (res == 1) // full match
                    hs.absorb(ev);

              //  $(this).attr("data-match", match);

                hs.list.setInput(this, match);

            }).on("keydown", function (ev) {
                if (ev.which == K.BS) {
                    var s = $(this).attr("data-match") || '';
                    s = s.slice(0, -1);
                    hs.list.setInput(this, s);
                    hs.list.exFind(this, s);
                    hs.absorb(ev);
                    return;
                };

                if (ev.which == K.DEL) {
                    $(this).val('');
                    hs.list.setInput(this, '');
                    hs.absorb(ev);
                    return;
                };

                

            }).on("blur", function () {
                hs.list.setInput(this, '');
            });

            ;
        }

    }, // list

    // ******************* //

    select : {

        getOptionCodes: function (sel_fld_id) {

            var $opts = $("#" + sel_fld_id + " option");
            var arr = new Array($opts.length);
                       
            
            $.each($opts, function(i, opt) {
                arr[i] = new Array();
                arr[i][0] = $(opt).val();
                arr[i][1] = $(opt).html();
            });
            
            return arr;

        },
           
        toExList: function (sel_id) {

         //   if (hs.fm.isMobile) return; // cmntd: 18.5.2019

            var $sel = h$(sel_id);

            if (hs.e.isDisabled($sel))
                return;

            var code_arr = hs.select.getOptionCodes(sel_id);

            var inp_id = sel_id + "_inp";

            var cfg = $sel.attr("data-cfg");

            $sel.attr("data-sel-inp-id", inp_id).attr("data-cfg","").hide();
               

            $("<input name='" + inp_id + "' id='" + inp_id + "' type='text' value='' class='flds round-border' style='width: " + $sel.width() + "px;' />").insertAfter("#" + sel_id);

            var $inp = h$(inp_id).addClass("ui-exlist").attr("data-cfg", cfg).attr("data-inp-sel-id", sel_id);

                                   
            focus_replace(sel_id, inp_id);
            
            $inp.exList({
                codelist: code_arr,
                selectListId: sel_id,
                select_only: true
            });
            

           

            var code = $sel.val();

            if (!hs.isEmpty(code))
                $('#' + inp_id).attr('data-sel_id', code).exList('select');

            return $inp;
        }



    },





    
    // ******************** // 

    task: {

        intId: null,
        check_count: 0,

        mon: function (t) {
            var _url = "/app/ajax/?f=get-task-status&task-id=" + t.id;

             hs.page.cover(true);

            $("<div id='wait-status-box' class='info-msg has-shadow'>" + t.msg + "</div>").appendTo($("div.page-content")).position({
                my: "center",
                at: "center",
                of: "#page"
            });

            this.intId = window.setInterval("hs.ajax.get('" + _url + "',null)", t.refresh);
            this.check_count = 0;
        },

        //----------------------

        status: function (t) {
            var s = t.msg;
            
            if (this.check_count++ >= 10)
                s += K.br + hs.ui.span("note", "#" + this.check_count + " on " + t.on + " Status: (" + t.status + ").");

            

            $('#wait-status-box').html(s).show();

            if (t.status == "F" || t.status == "E") {
                hs.page.cover(false);
                clearInterval(this.intId);
                $('#wait-status-box').hide();
               // $("#full_screen_cover").hide();

                if (t.status == "E")
                    hs.msg.error(t.msg);
                else
                    hs.msg.success(t.msg);
            }
        }
    }, // task


    // ********************* // 


    msg: {

        type: { info: 0, success: 1, warn: 2, error: 9 },

        msg: function (t, m) {
            switch (t) {
                case 1: hs.msg.success(m); break;
                case 2: hs.msg.warn(m); break;
                case 9: hs.msg.error(m); break;
                default:
                case 1: hs.msg.info(m); break;
            }
        },
        
        success: function (m) {
            $("<div class='success-msg has-shadow'>" + m + "</div>").closeableBox().atBottom().appendTo("#page");
        },

        error: function (m) {
            $("<div class='err-msg has-shadow'>" + m + "</div>").closeableBox().atBottom().appendTo("#page");
            log(m);
        },

        warn: function (m) {
            $("<div class='warn-msg has-shadow'>" + m + "</div>").closeableBox().atBottom().appendTo("#page");
            log(m);
        },

        info: function (m) {
            $("<div style='z-index: 4000;' class='info-msg has-shadow print-preview'>" + m + "</div>").closeableBox().atBottom().appendTo("#page");
        },

        box: function (m, id) {
            if (arguments.length < 2)
                id = "msg-box0";

            $("#" + id).remove();

            $("<div id='" + id + "' class='msg-box ui-info has-shadow'>" + m + "</div>").closeableBox().appendTo("#page").position({
                my: "center",
                at: "center",
                of: "#page"
            });
        },

        box2: function (m, id) {
            if (arguments.length < 2)
                id = "msg-box0";

            $("#" + id).remove();

            $("<div id='" + id + "' class='msg-box ui-info has-shadow'>" + m + "</div>").closeableBox().appendTo("body").position({
                my: "center",
                at: "center",
                of: "body"
            });
        },

        boxAtParent: function (m) {
            $("<div class='msg-box ui-info has-shadow'>" + m + "</div>").closeableBox().appendTo($("div.page-content", parent.document.body)).position({
                my: "center",
                at: "center",
                of: "#page"
            });
        },

        appendToParent: function (m) {
            $("<div>" + m + "</div>").appendTo($("div.page-content", parent.document.body));
        },
        
        fix: function (m,close) {
           var e=$("<div class='msg-fixed ui-info has-shadow'>" + m + "</div>").appendTo($("div.page-content")).position({
                my: "center",
                at: "center",
                of: "#page"
           });

            if (arguments.length < 2)
                close = false;

            if (close)
                $(e).closeableBox();

        },

        dlg: function (cont, cls) {
            if (arguments.length < 2)
                cls = "ui-info";
            hs.msg.removeAll();
            cont = "<div style='margin: 0; height: 100%; padding: 10px; text-align:center' class='"+ cls+"'>" + cont + "</div>";
            ShowContentDialog(cont, { width: 500, height: 200 });
        },

        _toast_tmr: 0,

        toast: function (m, _ms, _clear) {
            var ms = (typeof (_ms) !== "undefined") ? _ms : 3000;
            if (arguments.length < 3)
                _clear = false;

            if (ms < 60) ms *= 1000;
            if (ms < 1000) ms = 1000;
            if (ms > 60000) ms = 6000;

            log("toast: " + m);

            if (_clear)
                $("#toast-msg").remove();

            if (!hs.isEmpty(m)) {
                var toast_obj = $("#toast-msg");
                if (toast_obj.length > 0) {
                    toast_obj.html(toast_obj.html() + "<hr/>" + m);
                }
                else
                    $("<div id='toast-msg' name='toast-msg' class='toast-box has-shadow no-print'>" + m + "</div>").appendTo("#page").closeableBox();
            }
            
            window.clearTimeout(this._toast_tmr);
            this._toast_tmr = window.setTimeout("DelElm('toast-msg')", ms);
        },

        adjustTimeoutSec: function (sec, min, max) {
            if (sec < min)
                return min;

            if (sec > max)
                return max;

            return sec;
        },

        required: function (e) {
            hs.msg.tip(e, hs.L("Field is required..", "الحقل مطلوب"),8);
        },

        updated:function(e) {
            e=h$(e);
            hs.msg.tip(e, "تم تحديث قيمة الحقل: " + e.val() );
        },
        
        // sec < 10 => no close by msg.removeAll
        // pass null msg to remove active msg
        tip:  function (e, m, sec, _width, fixed, above) {
            var al = arguments.length;
            e = h$(e);

            if (al < 3)
                sec = 10;

             sec = hs.msg.adjustTimeoutSec(sec, 1, 180);
            
             if (al < 4) {
                 _width = e.width();
                 if (_width < 300)
                     _width = 300;
             };

             if (_width == 0)
                 _width = 200;

             if (al < 5)
                 fixed = false;

             if (al < 6)
                 above = false;


             var tip_id = "tip-msg";
             var css_class = null;
                        
             if (sec < 10) {
                 tip_id += hs.e.getId(e);
                 css_class="remove-on-esc";
             }


             $("#" + tip_id).remove();

             if (hs.isEmpty(m))
                 return; // just for remove

             var div;

             if (above) {
                 div = $("<div id='" + tip_id + "' style='position: absolute;margin:20px;'  class='tip-box ui-box ui-info has-shadow'>"+ m +"<span class='tip-arrow-below'>&nbsp;</span></div>")
                  .width(_width)
                  .addClass(css_class)
                  .insertAfter(e).position({
                      my: "center bottom",
                      at: "center top",
                      of: e,
                      collision: "flipfit flipfit"
                  });
             }
             else {
                 div = $("<div id='" + tip_id + "' style='position: absolute;'  class='tip-box ui-box ui-info has-shadow'><span class='tip-arrow'>&nbsp;</span>" + m + "</div>")
                      .width(_width)
                      .addClass(css_class)
                      .insertAfter(e);
             };

            

            if (fixed)
                $(div).css({ position: 'fixed', top: $(e).offset().top + $(e).height });//.height(200);
             


             if (sec > 1 && sec != 120) 
                 $(div).closeableBox();
             
             if (sec < 180 && sec != 120)
                delayDelElm(tip_id, sec * 1000);
        },

       

        // box_id => do not remove the box until esc
        // _sec = seconds to last (0=forever)
        callout: function (m, e, _sec, _class, _width, _pos, box_id) {

            if (hs.isEmpty(m))
                return;

            var al = arguments.length;

            if (al < 2)
                e = null;

            if (al < 3)
                _sec = 0;

            if (al < 4 || _class==null)
                _class="ui-info";

            if (al < 5)
                _width = h$(e).width() || 300;

            if (al < 6)
                _pos = 2;

            if (al < 7)
                box_id = null;

           

            var box;
           


            if (hs.isEmpty(box_id)) {
                box_id = "tip-msg";
                $("#" + box_id).remove();
            }
            else {
                box = $("#" + box_id);
                if (box.length > 0) {
                    box.html(m).closeableBox();
                    return;
                };
            };
            
            var top = 50, left = 10;
            
            if (_pos == 10)
                top = left = 10;

            var arrow = ''; 
            
            var html = "<div id='{3}' style='position: absolute;z-index: 805;'  class='ui-callout has-shadow remove-on-esc {0}'>{2}{1}</div>".format(_class, m, arrow, box_id);

            box = $(html)
                .width(_width).closeableBox().draggable();

            if (e != null) {
                box.css("max-height", "600px");
                box.insertAfter(h$(e)).position({
                    my: "center bottom",
                    at: "center top",
                    of: e,
                    collision: "flipfit flipfit"
                });

            }
            else {
                $(box).appendTo("#page").css({
                    position: 'fixed',
                    top: top,
                    left: left
                });

              

            };

           
            if (_sec > 0)
                delayDelElm(box_id, _sec*1000);
        },

        status: function (m) {
            var $status_dlg = $("#dlg_frame");
            if ($status_dlg.length > 0) {
                $("<div></div>").html(m).appendTo($status_dlg);
            }
            else
                hs.msg.toast(m);

        },

        wait: function (_ena, m)
        {
            if (arguments.length < 1)
                _ena = true;
                       
            if (arguments.length < 2 || hs.isEmpty(m))
                m = hs.R.wait ;
           
            if (_ena) {
                hs.page.cover(true);
                $("<div id='wait-status-box' class='info-msg has-shadow round-border'>" + m + "</div>").appendTo($("div.page-content")).position({
                    my: "center",
                    at: "center",
                    of: "#page"
                });
            }
            else {
                // $('#wait-status-box').remove();
                //hs.page.cover(false);
                this.endWait();
            };
        },

        endWait: function () {
            hs.page.cover(false)
            $('#wait-status-box').remove();
        },
        
        
       

        removeAll: function () {
            $("#confirm_box, .success-msg, .err-msg, .warn-msg, .info-msg, .msg-box, #tip-msg, .popup").not(".ui-fmc").remove();
            $(".ui-tooltip-content").parents('div').remove();
          //  $(".popup-content-no-hover").hide();
            $(".hide-on-esc").hide();
            $(".cfm-msg").hide();
        }

    }, // msg

    // *********** EasyForm Data Grid *********** //

    dg: {

        getRowFld$: function (row_idx, fld_key) {
            return $("#r" + row_idx.toString() + "-" + fld_key);
        },

        getRowsCount: function (grid_flds_arr) {
            var fldNm = grid_flds_arr[0];
            var rc = 0;
            for (var r = 0; r < 9999; r++) {
                if (this.getRowFld$(r, fldNm).length == 0)
                    break;
                rc++;
            }

            return rc;
        },

        setFieldValue: function (e, val, overwrite) {
            if (hs.e.isReadonly(e))
                return;
            if (hs.e.hasValue(e) && !overwrite)
                return;

            e.val(val).css("background-color","#ffffaa");
        },

        lineCopy: function (grid_flds_arr, f, t, overwrite) {
            for (i = 0; i < grid_flds_arr.length; i++) {
                var fldNm = grid_flds_arr[i];
                if (!hs.isEmpty(fldNm)) {
                    this.setFieldValue(this.getRowFld$(t, fldNm), this.getRowFld$(f, fldNm).val(), overwrite)
                }
            }
        },

        

        setColomnVal: function (col_fld_key, val, rc, overwrite) {
            for (var r = 0; r < rc; r++) {
                this.setFieldValue(this.getRowFld$(r, col_fld_key), val, overwrite);
            };
        },

        setupTools: function () {
            var grid_flds_arr = $("#_grid_flds_").val().split(';');
            if (grid_flds_arr == null)
                return;

            for (c = 0; c < grid_flds_arr.length; c++) {
                var fk = grid_flds_arr[c];
                if (hs.isEmpty(fk))
                    continue;

                var e = this.getRowFld$(0, fk);
                if (hs.e.isReadonly(e)) continue;

                var tools = hs.link("نسخ إلى الحقول الفارغة", "js:hs.dg.copyRowToAll(false,0," + c.toString() + ")", "icon_down");
                tools += hs.link("نسخ إلى كل الحقول", "js:hs.dg.copyRowToAll(true,0," + c.toString() + ")", "icon_down");
                //hs.e.inlineTools(e, tools, -1);
                hs.e.setupTools(e, 0, tools);
            }
        },

        copyRowToAll: function (overwrite, src_row_idx, src_col_idx) {
            var al = arguments.length;
            if (al < 2)
                src_row_idx = 0;

            if (al < 3)
                src_col_idx = -1; // all

            var grid_flds_arr = $("#_grid_flds_").val().split(';');
            if (grid_flds_arr == null)
                return;

            var rc = this.getRowsCount(grid_flds_arr);

            for (c = 0; c < grid_flds_arr.length; c++) {
                if (src_col_idx >= 0 && src_col_idx != c)
                    continue;
                var fk = grid_flds_arr[c];
                if (!hs.isEmpty(fk)) 
                    this.setColomnVal(fk, this.getRowFld$(src_row_idx, fk).val(), rc, overwrite);
            };
        },

        getFldIndex: function (fld) {
            var flds = $('table.rep_tab:not(.fm-index) thead tr th');
            for (var i = 0; i < flds.length; i++) {
                if ($(flds[i]).attr("data-id") == fld)
                    return i + 1;
            }

            return -1; // not found
        },


        getFldVal: function (rid, fld) {
            var fld_idx = hs.dg.getFldIndex(fld).toString();
            return $("table.rep_tab:not(.fm-index) tbody tr[data-rid=" + rid + "] td:nth-child(" + fld_idx + ")").html();
        },


        setFldCont: function (rid, fld, val, cont) {
            var fld_idx = hs.dg.getFldIndex(fld).toString();
            return $("table.rep_tab:not(.fm-index) tbody tr[data-rid=" + rid + "] td:nth-child(" + fld_idx + ")").html(cont).attr("data-val", val);
        },

        render: function (tid, row_alt) {
           
           // hs.msg.toast('Render table:' + tid);

            var tab = $('#' + tid);

            if (row_alt == 1)
                $(tab).find('tr:even').addClass('r1');
               $(tab).find('tr:odd').addClass('r0');
            // $('#' + tid + ' tr:even').addClass('r1');



            var url = $(tab).attr('data-url');

            if (hs.isEmpty(url))
                return;

            $(tab).find('tr:not(.head)').each(function () {
                var td = $(this).find('td')[1]; 
                var rid = $(this).attr('data-rid');
                if (hs.isEmpty(rid))
                    return;

                if (hs.fm.isGrid())
                    return;

                var tools = '';

                if (!hs.fm.isMobile) {
                    tools += "<a class='no-export icon {3} qv_show_idx' href='javascript:ShowDlg(\"{0}&cmd={2}&id={1}\")'>&nbsp;</a>&nbsp;".f(url, rid, 'view', 'icon_qv');
                   
                     // config_me
                    
//                    tools += "<a class='no-export icon icon_del qv_show_idx' onclick='do_ajax_remove(\"" + url + "&cmd=del&id=" + rid + "\",this)'>&nbsp;</a>";
                }

                var _FMT = "<a class='no-export icon {3} qv_show_id' href='{0}&cmd={2}&id={1}'>{4}</a>";
                var _FMT_DLG = "<a class='no-export icon {3} qv_show_id' href='javascript:ShowDlg{5}(\"{0}&cmd={2}&id={1}\")'>{4}</a>";

                var tm = '';
                tm += _FMT.f(url, rid, 'view','','عرض','');
                tm += _FMT.f(url, rid, 'edit', 'icon_edit', 'تعديل','');

                tm = "<span style='padding:0; margin:0; position:relative;top:0px;' class='no-print popup-box'><a tabindex='-1' style='height:{2}px; width:{3}px;padding: 0;margin:0;' class='link_icon_only {4}' href='javascript:noop()'>&nbsp;</a><span class='popup-menu' style='top:10px;width: {1}px'>{0}</span></span>".format(tm, 200, 20, 16, "fld-tools-icon");

               
                             
                tools += tm;


                var cont = tools + "&nbsp;&nbsp;" + $(td).html();
                $(td).html(cont);
            });





        },

        getTab: function (e) {
            return $(e).parents('table').first();
        },

        onRowDblClick: function (row) {
            $(row).toggleClass("hs-selected2");

            var tab = this.getTab(row);
            var url = $(tab).attr('data-url');
            var rid = $(row).attr('data-rid');

            if (hs.isEmpty(url) || hs.isEmpty(rid))
                return;
            
            url = url + '&cmd=view&id=' + rid;

            ShowDlg(url);

        },

        adjustRepTable: function () {

            $(".rep-controls").each(function () {
                var e = this;
                var cont = $(e).html();

                cont += hs.link('', "js:hs.page.refresh(0,1)", "icon icon_refresh");
                cont += hs.link('', "js:window.print()", "icon icon_print");

                $(e).html(cont);
            });

            // add hover to rows of data lists
            $("table.rep_tab:not(.fm-index, .data-grid, .no-hilight) tbody tr:not(.title)").hover(
        function (e) { $(this).addClass("ui-hilight"); },
        function (e) { $(this).removeClass("ui-hilight"); })
        .on("click", function (e) { $(this).toggleClass("hs-selected"); }).on("dblclick", function () { hs.dg.onRowDblClick(this) });



            $("table.rep_tab.data-grid:not(.no-hilight) tbody tr:not(.title)").hover(
                function (e) { $(this).addClass("ui-hilight"); },
                function (e) { $(this).removeClass("ui-hilight"); }).on("dblclick", function () { $(this).toggleClass("hs-selected"); });


            $("table.rep_tab.alt-rows").each(function () {
                hs.dg.render(hs.e.getId(this), !0);
            });


            var th = $('.rep_tab th');
            var c, td = 0;
           

            for (c = 0; c < th.length; c++) {
                var css = $(th[c]).attr("data-class");
                var colspan = hs.safe.parseInt($(th[c]).attr("colspan"));
                if (colspan == 0) colspan = 1;
                for (cs = 0; cs < colspan; cs++) {
                    td++;
                    if (!hs.isEmpty(css))
                        $('.rep_tab:not(.rep-title) td:nth-child(' + (td) + ')').addClass(css);
                }
            };
        }
        
        
    }, // dg

    // ********** // 
    // UI.helper
    ui: {

        br: "<br/>",
        hr: "<hr/>",
        nbsp: "&nbsp;",

        span: function (cls, cont) {
            return "<span class='{0}'>{1}</span>".f(cls, cont);
        },

        div: function (cls, cont) {
            return "<div class='{0}'>{1}</div>".f(cls, cont);
        },

        code: function (cont, cls) {
            if (arguments.length < 2) cls = 'ui-info';
            return "<pre class='{0}'><code>{1}</code></pre>".f(cls, cont);
        },

        // show busy 
        busy: function (_enable) {
            if (_enable)
                $("<img id='dlg_loading_img' class='mem_icon dlg_loading' src='/app/s/images/loading.gif' />").insertBefore("#page");
            else
                $("#dlg_loading_img").remove();
        },


        showInputBox: function (div_id, but_caption, but_callback) {
            $("#" + div_id).closeableBox().show().addClass("box-at-top ui-input-box has-shadow");
        },

        // ---------------
        showToolsDlg:function(cont) {
              hs.msg.removeAll();
             cont = "<div style='margin: 10px' class='inline-menu'>" + cont + "</div>";
             ShowContentDialog(cont,{width:400, height:500});
        },

        showContentDlg: function (tit, cont) {

            var page_size = getPageSize();
            var min_width = 300;
            var min_hight = 400;
            var pct = 70; // 70%

            pct = pct / 100;
            page_size[0] = page_size[0] * pct;
            page_size[3] = page_size[3] * pct;

            if (page_size[0] < min_width) page_size[0] = min_width;
            if (page_size[3] < min_hight) page_size[3] = min_hight;

            var opt = {};
            opt.width=page_size[0];
            opt.height=page_size[3];
            ShowContentDialog(cont, opt );
        },

        setFrameUrl:function(f, _url) {
            h$(f).attr("src", _url);
        },
        
        setFrameHtml: function (f, _html) {
            var src = 'data:text/html;charset=utf-8,' + encodeURIComponent(_html);
            h$(f).attr("src", src);
        },

        fmtFrameDataUrl: function (_html) {
            return 'data:text/html;charset=utf-8,' + encodeURIComponent(_html);
        },

        scrollTo: function (e, container) {

            var _a = h$(e);
            var _container;

            if (arguments.length == 2)
                _container = h$(container);
            else
                _container = $("#page");
            
            
            var top = _a.position().top + _container.scrollTop() - _container.height() + _a.height();
            _container.scrollTop(top);
                        

            //hs.msg.toast(top.toString());
        },
        
        

        countMe: function (e,t) {
            var l = h$(e).val().length;
            
            if (l > 0)
                h$(t).html(l.toString());
            else
                h$(t).html('');
        },

       
        

    }, // hs.ui      

    // ********************** //

    inputBox: {
        value: null,
        onOK: null, // callback
        dlg_h: 300,
        dlg_w: 500,
        auto_focus: "inp_box_text",
        onChange : null,
        
        init: function (w, h,af ) {
            this.dlg_h = h;
            this.dlg_w = w;
            this.auto_focus = af;
            return this;
        },

        keypress: function (ev) {
            if (ev.keyCode == '13')
                hs.inputBox._onOK();
        },

        changed: function (ev) {
            this.onChange && this.onChange(ev);
        }
        ,
        show: function (is_long_text, init_val, dlg_title, inst, ex_ctrls, buttons, ex_attribs, help_text, callback) {

            if (hs.isEmpty(init_val))
                init_val = "";

            if (hs.isEmpty(dlg_title)) dlg_title = "";
           // if (hs.isEmpty(inst)) inst = "";
           // if (hs.isEmpty(ex_attribs)) ex_attribs = "";
           // if (hs.isEmpty(help_text)) help_text = "";
            

            if (buttons == null)
                buttons = "<div class='center-text'><a style='min-width: 200px' class='link_icon ui-cmd icon_go round-border' href='javascript:hs.inputBox._onOK()'>موافق</a></div>";
                                    
            this.onOK = callback;

            if (!hs.isEmpty(help_text))
                help_text = "<hr/><div class='inst'>" + help_text + "</div>";


            var cont;
            
            if (is_long_text) {
                this.dlg_h = 500;
                cont = "<div class='ui-input-box' {4} id='d_{0}'>{2}<textarea name='{0}' id='{0}' class='flds round-border' style='width: 100%; margin: 10px auto;height:200px;'>{7}</textarea>{6}{3}{5}</div>";
            }
            else {
                
                cont = "<div class='ui-input-box' {4} id='d_{0}'>{2}<input onchange='hs.inputBox.change(event)' onkeydown='hs.inputBox.keypress(event)' value='{7}' type='text' name='{0}' id='{0}' class='flds round-border' style='width: 100%; margin: 10px auto;' />{6}{3}{5}</div>";
            }

            cont = cont.f(
                "inp_box_text", //0
                dlg_title,// 1
                inst,//2
                buttons,//3
                ex_attribs,//4
                help_text, // 5
                ex_ctrls, // 6
                init_val // 7
                ) + "<div style='text-align: center' id='_ib_msg'></div>";

            // window.alert(cont);

            ShowContentDialog(cont, { width: this.dlg_w, height: this.dlg_h }, dlg_title);
                                   
            $("#" + this.auto_focus).focus().select();

            

        }, // show

        _onOK: function () {
            var inp_val = $("#inp_box_text").val();
            if (hs.isEmpty(inp_val)) {
                $("#inp_box_text").focus();
            };

            hs.inputBox.value = inp_val;

            
            
            if (hs.inputBox.onOK != null) {
                var err_msg = hs.inputBox.onOK(inp_val);
                if (!hs.isEmpty(err_msg)) {
                    // hs.msg.error(err_msg);
                    $("#_ib_msg").html(hs.ui.span("ui-err", err_msg));
                    return;
                }
            };
            
            
             HsDialog.CloseDialog();
            
        }

        

    },
    
    // ********************** //

   

    nol: { // NamedObjectList

        get_item: function (nolList, item_id) {
            var i;

            for (i = 0; nolList != null && i < nolList.items.length; i++) {
                if (nolList.items[i].id == item_id)
                    return nolList.items[i];
            };

            return null;
        },

        foreach: function (nolList, callback) {
            var i;
            for (i = 0; nolList != null && i < nolList.items.length; i++) {
                callback(nolList.items[i], i);
            };
        },

        getCodeListArray: function (nolList, _getId, _getName) {
            if (nolList == null)
                return null;

            var arr = new Array(nolList.items.length);
            var i;
            for (i = 0; i < nolList.items.length; i++) {
                arr[i] = new Array();
                arr[i][0] = _getId == null ? nolList.items[i].id : _getId(nolList.items[i]);
                arr[i][1] = _getName(nolList.items[i]);
            };

            return arr;
        }



    }, // nol
    
    // ********************** //

    cache: { // web storage api helper object
        storage: null,



        // https://developer.mozilla.org/en-US/docs/Web/API/Web_Storage_API/Using_the_Web_Storage_API
        isAvailable: function () {
            var s;
            try {
                s = window['localStorage'];
                var x = '_test_';
                s.setItem(x, x);
                s.removeItem(x);
                this.storage = s;
                return true;
            }
            catch (e) {
                return e instanceof DOMException &&
                    (e.code === 22 || e.code === 1014 || e.name === 'QuotaExceededError' || e.name === 'NS_ERROR_DOM_QUOTA_REACHED') &&
                    (s && s.length !== 0);
            }
        },

        getItemFromServer: function (key, title) {
            
            var url = "/app/ajax/?f=get-cached-item&cache-key=" + key;

            title = hs.L("Loading: ", "تحميل بيانات: ") + title;


            hs.trace("starting : " + title);

            hs.ajax.getWait(url, title);

            hs.trace("Finishing : " + title);
        },

        setItem: function (key, data) {

            try {
                localStorage.setItem(key, JSON.stringify(data));
            } catch (ex) {
                hs.logEx(ex);
                return false;
            };

            if (key.startsWith("offline."))
                hs.msg.toast("تم الحفظ إلى التخزين المؤقت على هذا الجهاز - المساحة المستخدمة: " + hs.cache.getDiskUsage() + " KB" );
            else
                hs.msg.toast('Caching user data: ' + key);

            return true;
        },

        getItem: function (key) {
            try {
                  var item = localStorage.getItem(key);
                  if (item != null)
                      return JSON.parse(item);
            } catch (ex) {
                hs.logEx(ex);
            };

            return null;
        },

     

        getCoding: function (k) {
            
            var cl = this.getItem(k);
            if (cl != null)
                return cl;
            
            
            var url = "/app/ajax/?f=get-coding&id=" + k;

            var title = hs.L("Loading: ", "تحميل بيانات: ") + k;


            hs.trace("starting : " + title);

            hs.ajax.getWait(url, title);

            hs.trace("Finishing : " + title);

            cl = this.getItem(k);
            if (cl != null)
                return cl;

            return null;

        },
                

        removeItem: function (k) {
            try {
                localStorage.removeItem(k);
            }
            catch (ex) {
                hs.logEx(ex);
            };
        },

        clear: function (all) {

            if (all) {
                localStorage.clear();
                return;
            };

            for (var i = 0; i < localStorage.length; i++) {
                var k = hs.cache.getKey(i)
                if (!(k.startsWith("offline.") || k.startsWith("_")))
                    hs.cache.removeItem(k)
            }

        },

        getKey: function (index) {
            return localStorage.key(index);
        },

        getTextValue: function (k) {
            return localStorage.getItem(k);
        },

        getLength: function()  {
            return localStorage.length;
        },

        // Returns the total amount of disk space used (in MB) by localStorage for the current domain.
        getDiskUsage: function() {
            var total = 0;

            for (var i = 0; i < localStorage.length; i++) {
                var k = hs.cache.getKey(i);
                total += hs.cache.getTextValue(k).length * 2; // * 2 as the data stored in utf-16
            };

            return (total / 1024).toFixed(2);
        },

        showItems: function (startWith, showValues) {
            var s = "<div class='lft' style='margin-bottom:100px'>";
            for (var i = 0; i < localStorage.length; i++) {
                var k = hs.cache.getKey(i);

                if (startWith != null && k.indexOf(startWith) != 0)
                    continue;

                s += i.toString() + " - " + k + "<br/>";
                s += "<a href=\"javascript:hs.offline.processFormData('" + k + "')\">Send to server</a><br/>";
                if (showValues) {
                    s += hs.cache.getTextValue(k) + "<hr/>";
                };
            };

            s += "<br/>Disk Usage: " + hs.cache.getDiskUsage() + " KB";

            s += "<a href=\"javascript:hs.offline.sendAll(false)\">Send all to server</a><br/>";

            s += "</div>";

            return s;
        }



    } // storage
    ,

    // ********** Offline Mode ************ //
    // OfflineData helper
    offline: {

        modeTitle: function() 
        {
            return hs.L("Offline Mode", "وضع عدم الإتصال");
        },

        dataTitle: function () {
            return hs.L("Offline Data", "البيانات المخزنة مؤقتا على هذا الجهاز");
        },

        confirmOffline: function () {
            ConfirmUrl("javascript:hs.fm.isOffline(true)", hs.offline.modeTitle());
        },

        // ----------------------

        getDataKey: function (data) {
            return "offline." + data.client + "." + data.fm + "." + data.cmd + "." + data.time.toJSON();
        },

        isDataKey: function (k) {
            return k.startsWith("offline.");
        },

        forEach: function (_item_handler) {

            for (var i = 0; i < hs.cache.getLength() ; i++) {
                var k = hs.cache.getKey(i);

                if (!hs.offline.isDataKey(k))
                    continue;

                _item_handler(k);
            };

        },

        // ----------------------

        remove: function (key) {
            if (!hs.isEmpty(key))
                hs.cache.removeItem(key);
        },

        removeAll: function (confirmed) {

            if (!confirmed) {
                ConfirmUrl("javascript:hs.offline.removeAll(true);CloseTopDlg()", hs.R.remove_offline_data);
                return;
            };

            hs.offline.forEach(function (key) {
                hs.offline.remove(key);
                hs.offline.showStatus();
            });
        },

        // -----------------------
        // bool,bool
        save: function (_fm_reset, _switch_offline) { 
            
            var fm_data = hs.fm.serialize();

            var data = {};

            data.url = window.location.href;
            data.fm = hsEasyForm.getFormId();
            data.title = hs.fm.title;
            data.cmd = hsEasyForm.getCmd();
            data.time = new Date();
            data.client = hs.fm.val("sys_client_id");
            data.fm_data = fm_data;
            

            var key = hs.offline.getDataKey(data);

            hs.cache.setItem(key, data);

            if (_fm_reset) {
                hs.fm.reset();
                hs.msg.removeAll();
            };

            if (_switch_offline)
                hs.fm.isOffline(true);

            hs.msg.warn("تم الحفظ إلى التخزين المؤقت على هذا الجهاز. " + "<hr/><a href='javascript:hs.offline.showStatus()'>إستعراض التخزين المؤقت</a>");
            
        },

        // process one form data (normall that had an issue)
        processFormData: function (key) { 
            var data = hs.cache.getItem(key);
            var sdata = data.fm_data;
            
            hs.fm.deserialize(sdata);
            hs.fm.curOfflinekey = key;
            CloseTopDlg();

        },
       
        sendAll: function (confirmed) {

            if (!confirmed) {
                ConfirmUrl("javascript:hs.offline.sendAll(true)", hs.R.send_offline_data);
                return;
            };

            hs.ui.showContentDlg("",hs.R.send_offline_data + ":<hr/>");

            hs.offline.forEach(function (key) {
                hs.msg.status(hs.R.sending + ": " + key);
                hs.offline.sendFormData(key);
            });

            hs.msg.status(hs.R.send_complete);
        }
        ,

        sendFormData: function (key) {
            var data = hs.cache.getItem(key);
            if (data == null) {
                hs.msg.status("data is null");
                return;
            };

            hs.offline.submitFormData(key, data);
        },

        // --------------

        submitFormData: function (key, data) {
           
            var _url =data.url + "&ajax=y&offline-data-key=" + key;

            var jqxhr = $.ajax({
                type: "POST",
                url:  _url ,
                data: data.fm_data
       
            })
                .done(function (result, textStatus, jqXHR) {
                    if (hs.ajax.isRejected(jqXHR)) {
                        hs.eval(result); // execute the result as script
                        var err_msg = key +  " - " + data.title + " - " + hs.R.failed;
                        hs.msg.status(err_msg);
                    }
                    else {
                        hs.cache.removeItem(key);
                        hs.msg.status(key + " - " + data.title + " - " + hs.R.done);
                    };
                })
                .fail(function (jqXHR, textStatus, errorThrown) {
                    var err_msg = key + " - " + data.title + " : " + hs.R.failed + " - " + jqXHR.statusText;
                    log(err_msg);
                    hs.msg.status(err_msg);
                })
                .always(function () {
                    hs.ui.busy(false);
                });
        },

        // ------------------------

        switchMode: function (e_chkbox) {
            if (hs.e.isChecked(e_chkbox))
                hs.fm.isOffline(true);
            else
                hs.fm.isOffline(false);
        },

        // show offline status/data
        showStatus: function () {
            var c = 0, s = "";
            var isOffline = hs.fm.isOffline();
            hs.offline.forEach(function(k) {

                var data = hs.cache.getItem(k);
                c++;
               
                s += hs.ui.hr;

                if (data.url === window.location.href && !isOffline)
                    s += "<a class='icon icon_view' href=\"javascript:hs.offline.processFormData('" + k + "')\">&nbsp;</a>&nbsp;&nbsp;";

                s += "<a class='icon icon_upload' href=\"javascript:hs.offline.sendFormData('" + k + "');hs.offline.showStatus();\">&nbsp;</a>&nbsp;&nbsp;";
              //  s += "<a class='icon icon_del' href=\"javascript:hs.offline.remove('" + k + "');hs.offline.showStatus();\">&nbsp;</a>&nbsp;&nbsp;";

                s += c.toString() + " - " + k + " - " + data.title + "<br/>";

                if (data.url === window.location.href)
                    s += data.fm_data;
            }); // for

            if (c == 0)
                s = 'لا يوجد أي بيانات في التخزين المؤقت';

            var cont = "<div style='padding: 5px;'>";
           
            
            cont += "<div class='inst round-border closeable-box'><input type='checkbox' " + (hs.fm.isOffline() ? "checked='checked'" : "") + " value='1' onchange='hs.offline.switchMode(this)'/>";
            cont += hs.ui.nbsp + hs.offline.modeTitle() + hs.ui.br;
            cont += "عند وضع عدم الإتصال يقوم النظام بتخزين البيانات مؤقتا على جهاز المستخدم، وهذا يسمح بمواصلة العمل وإدخال البيانات حتى لو أن الإتصال مقطوع أو بطيئ، وعند عودة الإتصال يمكن توقيف هذا الوضع وسيتم إرسال البيانات إلى النظام الرئيسي";
            cont += "</div>";
            
            if (c > 0) {
                cont += "<a class='link_icon icon_upload' href='javascript:hs.offline.sendAll(false)'>" + hs.R.send_offline_data +"</a>&nbsp;";
               // cont += "<a class='link_icon icon_del' href='javascript:hs.offline.removeAll(false)'>" + hs.R.remove_offline_data + "</a>&nbsp;";
                cont += "<a class='link_icon icon_refresh' href='javascript:hs.offline.showStatus()'>" + hs.R.refresh + "</a>&nbsp;";
                
            };

            cont += "<hr/>Disk Usage: " + hs.cache.getDiskUsage() + " KB";
            
            cont +="<hr/>" + s;

            cont += "</div>";

            hs.ui.showContentDlg(hs.offline.modeTitle(), cont);

            
        }

    },

    // ********************** //

    cl: {

        find: function (cl_items, item_code) {
            if (cl_items == null)
                return -1;

            var i = 0;
            for (i = 0; i < cl_items.length; i++) {
                if (hs.isEmpty(cl_items[i][0]))
                    continue;

                if (item_code == cl_items[i][0]) {
                    return i;
                };
            };

            return -1; // not match
        } 

    } // cl



    
}; // hs


// *************************** //
// *************************** //

function do_select(inp_id, code) {

    hs.fm.dirty(true);

    if (hs.isEmpty(code))
        $('#' + inp_id).attr('data-sel_id', code).exList('reset').focus();
    else {

        hs.trace("exlist.do_select via menu: " + inp_id + " = " + code);

        $('#' + inp_id)
            .attr('data-sel_id', code)
            .attr('data-sel_via_menu','Y')
            .exList('select');
    }

};

function ms_select(inp_id, code) {

    hs.fm.dirty(true);

    fill_doc_line_items('items', 'id', code, true, true);

   // hs.msg.toast(code + " selected");
};


(function ($) {
    $.widget('hs.exList', {

        // --------------

        options: {
            codelist: null,
            selectListId: null,
            select_only: false,
            auto_select: true,
            multi_select: false,
            auto_open_on_focus: false,
            on_item_changed: null,
            on_focus: null,
            on_create:null,
            support_bc: false, // supports barcode scanning
            scroll_into_view: true,
            max_list_item_count: 999, // to view
            max_find_hits: 100,
            suggest_delay_ms: 250,
            find_exact: false,
            auto_complete: false, // the control is just auto complete suggest - no real codes
            ex_cont:'' // extra cont
        },

        // ----------------

       

        _create: function () { // called on doc load
           
            this._showTimerId = null;
            this._selected = false;
            this._saved_value = null; // whole input value
            this._prev_code = null;
            this._inp_id = this.element.attr('name');
            this.saved_support_bc = this.options.support_bc;
            this.cur_kc = 0;
            this.prev_kc = 0;

            // hs.trace("exlist.create: " + this._inp_id);

            if (this.options.selectListId != null) {
                var $sel = $("#" + this.options.selectListId);
               
                if ($sel.hasClass("has-invalid-value")) {
                    this.element.addClass("has-invalid-value");
                    $sel.removeClass("has-invalid-value");
                };

                
            };
           
            var _width = this.element.width();
            if (_width < 400)
                _width = 400;


            this._listDiv = $('<div></div>').css({  position: 'absolute',  width: _width }).addClass('suggest-box inline-menu ui-corner-all has-shadow')
          //  .attr('title', 'بحث بالاسم أو الرقم، مفتاح الإدخال للإختيار، مفتاح الحذف لإلغاء الإختيار')
            .hide()
            .insertAfter(this.element) ;

                      
           
            

            this.element
                .addClass("ex-list") // mark it
                .attr("autocomplete", "off")
                .on('blur.hs-exList', $.proxy(this._blur, this))
                //.attr("title", this.element.val())
            ;


            if (hs.fm.isMobile)
                this.element.on('keyup.hs-exList', $.proxy(this._keydown, this)); // 18.5.2019 - chrome mobile - does not support keydown
            else
                this.element.on('keydown.hs-exList', $.proxy(this._keydown, this));
            

            this.element.on('focus.hs-exList', $.proxy(this._focus, this));
            this.element.on('click.hs-exList', $.proxy(this._open, this));

            /*
            if (this.options.auto_open_on_focus)
                this.element.on('focus.hs-exList', $.proxy(this._open, this));
            else {
                this.element.on('click.hs-exList', $.proxy(this._open, this));
                this.element.on('focus.hs-exList', $.proxy(this._focus_no_open, this));
                
                
            }
            */
           
            if (this.options.on_create != null)
                this.options.on_create(this.element, this);

           // if (this.options.select_only && this.options.on_focus != null)
             //   this.options.select_only = false; // 26.3.2019 - test_me
            
           // this._saved_value = $(this.element).val();
            //this._prev_code = this.get_code();

            

            if (hs.fm.isFind()) {
                this.options.select_only = false;
            }
        },

      

       
        _open: function () {
            if (hs.e.isReadonly(this.element))
                return;


            this.trace("exlist.open");

            this._show_drop_list(0, null);
           
        },

        // ---------------------

        _focus: function () {
            if (hs.e.isReadonly(this.element))
                return;

            this._saved_value = this.element.val();
            this._prev_code = this.get_code();

            this.trace("exlist.focus prev_val=" + this._saved_value + " prev_code=" + this._prev_code);

            if (this.options.on_focus != null)
                this.options.on_focus(this.element, this);

            

            $(this.element)
                .attr('placeholder', this._saved_value)
               // .attr("title", this._saved_value)
                .data("prev_code", this.get_code())
                .val('');

            if (this.options.auto_open_on_focus)
                this._open();
        },

        

        _blur: function () {

            if (this.options.multi_select)
                return;

            if (hs.e.isReadonly(this.element))
                return;

            this.trace("exlist.blur");
                     

            if (!this.isOpen()) {
                var code = this.get_code();
                this.set_list_ctrl_val(code);

                if (!this.options.support_bc)
                    this._restore();
                else
                    this._close();

                return; // already closed
            };


           

            
            if (this._showTimerId != null) {
                window.clearTimeout(this._showTimerId);
            };


            
            var $this = this;
            
            // delay menu closing on blur due to menu select using mouse (it fires blur before select take place)
            window.setTimeout(function () {
                if ($this.isOpen()) {

                    if (!$this.element.val().contains("*"))
                        $this.select(0); 

                    $this.trace("exlist.closed using timeout");
                    $this._close();
                }
                else
                    $this.trace("exlist.closed already in timeout");
               
            },  250);
        },

        // ----------
        trace: function (m) {
            hs.trace(this._inp_id + " : " + m );
        },
        
        _close: function () {

            if (this.options.multi_select)
                return

            var was_open = this.isOpen();
            

            if (this._showTimerId != null) {
                window.clearTimeout(this._showTimerId);
                //showExListTimerId = 0;
            };

            this._listDiv.hide();

            this.options.support_bc = this.saved_support_bc;
            this.element.removeClass('find-mode');

            if (was_open)
                this.trace("exlist.closed");

        },

        // -----------
        
        select: function (kc) {

            if (arguments.length < 1)
                kc = 1; // called by item link select
            
                        

            var code = this.get_code();

            if (hs.isEmpty(code)) {
                this._show_drop_list(kc, null);
                code = this.get_code();
            };

           // code = this._adjustCode(code);

           

            this.set_list_ctrl_val(code);
            
            if (hs.isEmpty(code)) {
                if(!this.options.select_only && hs.isEmpty(this.element.val())) 
                    this.element.val(this._saved_value);

                if (!this.options.select_only)
                    return;
            };

            
            this.select_code(code, true);

                        

            this._close();
           
        },

        // ----------

        reset: function () { // on delete

            this.set_code('');
            this.element.attr('placeholder', '').attr("title",'');
            this._saved_value = null;
            this._prev_code = null;
            this.element.val('');

            this.set_list_ctrl_val('');
            

            hs.fm.dirty(true);

            
            if (this.options.on_item_changed != null)
                this.options.on_item_changed(this.element, false);
                
        },

       
        isOpen: function () {
            return hs.e.isVisible(this._listDiv);
        },


        get_code_title: function (code) {
            var cl_items = this.options.codelist;
            if (cl_items == null)
                return null;

            var i = 0;

            for (i = 0; i < cl_items.length; i++) {
                if (code == cl_items[i][0]) {
                    return cl_items[i][1];
                };
            };

            return null; // not match
        },

        // ----------------------
        set_list_ctrl_val: function (s) {
            if (this.options.selectListId != null)
                hs.fm.val(this.options.selectListId, s);

                //h$(this.options.selectListId).val(s)
        },

        select_code: function (item_code, move_next) {

            this.trace("select_code(" + item_code + ") move_next=" + move_next)

            if (this.options.multi_select) {
              //  ms_select(null, item_code);
              //  return
            };


            //hs.msg.toast(item_code);
            if (this.options.codelist == null)
                return;

            var idx = hs.cl.find(this.options.codelist, item_code);
          
            var inp_id = this._inp_id;
            var cl_items = this.options.codelist;
            if (idx >= cl_items.length)
                return;
                       
            var cur_code = this.get_code();

            this.element.attr('placeholder', '');

            if (idx >= 0) {
                var s = item_code;

                if (!this.options.auto_complete) {
                    var item_name = cl_items[idx][1];
                    s += ' : ' + item_name.replace(/_/gi,'') ;
                };

                this.element.val(s);
                
                this.set_code(item_code);
            }
            else {
              
                if (this.options.select_only) {
                    this.element.val('');
                };
                
                item_code = '';

                this.set_code(item_code);
            };

            //if (this._timerId != 0) {
            window.clearTimeout(this._showTimerId);
            //this._timerId = 0;
            // };

            // this._listDiv.hide();

           this.trace("item_code=" + item_code + " prev=" + this._prev_code);

            if (is_page_loaded)
            {
                if (item_code != this._prev_code) {
                    hs.fm.dirty(true);
                    if (this.options.on_item_changed != null)
                        this.options.on_item_changed(this.element, move_next);
                    else {
                        focus_next(this.element);
                    }
                }
                else {
                    if (move_next)
                        focus_next(this.element);
                }
            };
           
        },

        

        get_code: function () {
            return $(this.element).attr('data-sel_id');
        },

        set_code: function (code) {
            var title = '';
            var item_name = this.get_code_title(code);

            if (!hs.isEmpty(code)) {
                title = code;
                if (!this.options.auto_complete)
                    title += ' : ' + item_name;
            };

            return this.element.attr('data-sel_id', code).attr('placeholder', title); // .attr("title", title); //18.12.2020
        },

        // ----------------------

        _scroll_to_selected: function () {
           
            var _container = this._listDiv;
            var _a = _container.find("a.hs-selected");
            if (_a.length == 0)
                return;

            if (this.options.scroll_into_view)
                _a[0].scrollIntoView(false);
            else
                hs.ui.scrollTo(_a[0], _container);
        },


        _select_next: function (next) {

            var inp_id = this._inp_id;
            var _container = this._listDiv;
            var _a;

            if (next == 1) {
                _a = _container.find("a.hs-selected").next();
                if (_a.length == 0)
                    _a = _container.find("a").first();
            }
            else
                if (next == -1)
                    _a = _container.find("a.hs-selected").prev();
            
            if (_a.length != 0) {
                _container.find("a").removeClass("hs-selected");
                this.set_code(_a.attr("id"));
                _a.addClass("hs-selected");
            };

            this._scroll_to_selected();

        },

        _draw_list_item: function (inp_id, item_code, item_name, selected) {

            var item_title = this.options.auto_complete ? item_name : "<span class='mir-flt' style='width:100px; display:inline-block;overflow:hidden;padding:0;margin:0;'>" + item_code + "</span>&nbsp;" + item_name;
            
            var css_class = selected ? " class='hs-selected' " : null;

            if (hs.isEmpty(item_code) && !hs.isEmpty(item_name))
                css_class = " class='ui-tit' ";

            if (this.options.multi_select)
                return "<a tabindex='-1' id='" + item_code + "'" + css_class + " href=\"javascript:ms_select('" + inp_id + "','" + item_code + "')\">" + item_title + "</a>";
            else
                return "<a tabindex='-1' id='" + item_code + "'" + css_class + " href=\"javascript:do_select('" + inp_id + "','" + item_code + "')\">" + item_title + "</a>";
        },
       

        getMatchedItemId: function (code) {
            if (hs.isEmpty(code))
                return null;

            if (hs.cl.find(this.options.codelist, code) >= 0)
                return code;

            if (this.options.support_bc) { // special case for es_items
                var item = get_item_by_code(code);
                if (item != null)
                    return item.id;
            };
            
            return null;
        },


        isMatchedItem: function (i, inp_val) {

            if (!this.saved_support_bc)
                return 0;

            var cl_items = this.options.codelist;

            // special case for es_items
            
            if (!hs.isEmpty(cl_items[i][2]) && cl_items[i][2].startsWith(inp_val)) // item code
                return 2;

            if (!hs.isEmpty(cl_items[i][3]) && cl_items[i][3].contains(inp_val)) // item for_name 
                return 2;

            return 0;
        },

      

        _show_drop_list: function (kc, event) {
            var inp_id = this._inp_id;

            var item_is_detected = false;
            var cl_items = this.options.codelist;
           
            if (cl_items == null)
                return;

            var cur_sel_code = null;
            
            
            var item_code = null;
            var inp_val = this.element.val();

            var i = 0;
            var suggest_cont = '';
            var med_suggest_cont = '';
            var low_suggest_cont = '';


            if (hs.isEmpty(inp_val))
                inp_val = null;

            item_code = inp_val;


            if (hs.isEmpty(inp_val))
                item_code = null;
            else {

               
                item_code = inp_val;

                if (kc == 9 || kc == 13) {
                   
                    
                    item_is_detected = true;
                    item_code = this._getDetectedCode();


                    var item_id = this.getMatchedItemId(item_code);

                    if (item_id == null)
                        item_id = this.get_code();
                    
                    if (item_id != null) {

                        if (event != null)
                            hs.absorb(event);
                                                
                        this.select_code(item_id,true);
                        this._close();
                        return;
                    };
                   



                };

                // 28.3.2018 - not tested with bc
                if ((!this.options.support_bc) && ( kc == 13)) {
                    var item_id = this.get_code();
                    if (item_id != null) {
                        //this.select_code(item_id);
                        //this._close();
                        hs.trace("calling _blur.. !!");
                        this._blur();
                        return;
                    };

                };
                
            };


            if (hs.isEmpty(item_code) && hs.isEmpty(inp_val)) { // draw all

                cur_sel_code = this.get_code();

                suggest_cont += this._draw_list_item(inp_id, '', '', false); // blank

                for (i = 0; i < cl_items.length; i++) {
                    var key = cl_items[i][0];
                    var text = cl_items[i][1];

                    if (hs.isEmpty(key)) {
                        if (!hs.isEmpty(text))
                            suggest_cont += this._draw_list_item(inp_id, key, text, false);
                        continue;
                    };

                    text = text.replace(/_/gi, '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;')

                    if ((key == cur_sel_code) && !hs.isEmpty(cur_sel_code)) {
                        suggest_cont += this._draw_list_item(inp_id, key, text, true);
                        continue;
                    };
                    suggest_cont += this._draw_list_item(inp_id, key, text, false);

                    if (i > this.options.max_list_item_count) {
                        suggest_cont += this._draw_list_item(inp_id, '', '--يمكن عرض ' + this.options.max_list_item_count.toString() + ' عنصر، قم بالبحث لإظهار المزيد--', false);
                        break;
                    };


                }; // for


            }
            else {


                // draw the matched items

                cur_sel_code = null; // reset it, to auto select the first match

                
                var hits_cnt = 0;
                var low_hits_cnt = 0;

                var max_hits = this.options.max_find_hits;
                if (max_hits < 100) max_hits = 100;

                var inp_val_first_word = inp_val.split(' ')[0];

                var inp_val_sound = hs.sound(inp_val);
                
                for (i = 0; i < cl_items.length; i++) {
                    var key = cl_items[i][0];

                    if (hs.isEmpty(key))
                        continue;
                                        
                    var text = cl_items[i][1];
                    text = text.replace(/_/gi, '');

                    if ((item_code != null) && (item_code == key)) {
                        cur_sel_code = key;

                        if (item_is_detected) {
                            this.set_code(item_code);
                            this.select(1);
                        }
                        else {
                            this.set_code(cur_sel_code);
                            suggest_cont += this._draw_list_item(inp_id, key, text, true);
                        };

                        break;
                    };


                    if (inp_val != null) {

                        if (text.startsWithEx(inp_val) || key.startsWithEx(inp_val) || hs.sound(text).startsWith(inp_val_sound)) {
                            if (cur_sel_code == null && this.options.select_only && this.options.auto_select) { // 27.8.2018
                                cur_sel_code = key;
                                this.set_code(cur_sel_code);
                                suggest_cont += this._draw_list_item(inp_id, key, text, true);
                            }
                            else
                                suggest_cont += this._draw_list_item(inp_id, key, text, false);

                            hits_cnt++;
                        }
                        else {

                            if (!this.options.find_exact) {
                                var contains_all = inp_val.length >= 1 && (text.containsAllEx(inp_val) || hs.sound(text).containsAllEx(inp_val_sound));

                                if (text.startsWithEx(inp_val_first_word) && contains_all) {
                                    med_suggest_cont += this._draw_list_item(inp_id, key, text, false);
                                    hits_cnt++;
                                }
                                else
                                    // check if the name contains 
                                    if (low_hits_cnt < max_hits && (contains_all || key.contains(inp_val) || this.isMatchedItem(i, inp_val) > 0)) {
                                        low_suggest_cont += this._draw_list_item(inp_id, key, text, false);
                                        low_hits_cnt++;
                                    }

                            };
                        };
                        

                        if (hits_cnt > max_hits) { 
                            low_suggest_cont += this._draw_list_item(inp_id, '', '--يمكن عرض ' + max_hits + ' عنصر، قم بالبحث لإظهار المزيد--', false);
                            break;
                        };
                    };

                }; // for

                 
                

                if (hs.isEmpty(cur_sel_code) && !this.options.select_only)
                    this.set_code('');
               
            };

           
            
            low_suggest_cont +=  this.options.ex_cont;

            var all_suggest_content = suggest_cont + med_suggest_cont + low_suggest_cont;

            if (hs.isEmpty(all_suggest_content))
                this._listDiv.hide();
            else {

                // this._createListBox();
                fix_suggest_element(this._listDiv, this._inp_id);

                this._listDiv.html(all_suggest_content).show();

                this._listDiv.scrollTop(0);
            };

           
            
        },

        _enableFindMode: function (enable) {
            if (enable) {
                this.element.addClass('find-mode');
            };
        },

        _isListControlKey: function (k) {
            return k==9 || k == 13 || k == 27 || k == 38 || k == 40 || k == 46 || k == 8 || k == 114;
        },

        _isBarcodeKey: function (k) { // via BC scanner
            return (k >= 48 && k <= 90) || // k <= 57) ||
                this._isListControlKey(k) ||
                (k == 188 || k == 32 || k == 13 || k == 40) || // bc seps, 188 = comma
                (k == 16 || k == 17 || k == 18 || k == 37 || k == 39) ||
                (k == 173 || k == 189) || k > 160;
        },

        _isCodeDetected: function () { // via BC scanner
            return (this.options.support_bc) && (
                (this.cur_kc == 32 && this.prev_kc == 188) || // symbol: space + comma
                (this.cur_kc == 40 && this.prev_kc == 13)) // honeywell: down-arrow + enter
        },

        _cleanCode: function (s) {
            if (hs.isEmpty(s))
                return null;

            s = s.replace(" ", "");
            s = s.replace(",", "");
            s = s.replace("و", "");
            s = s.replace("،", "");
            return s;
        },
       

        _getDetectedCode: function () {
            var s = this._cleanCode(this.element.val());
                        
            if (hs.isEmpty(s))
                return null;

            var item_code = s;

            if (s.startsWith("277") && s.length == 13) { // wieghted
                var idx = wq_bc_fmt.pre;
                item_code = s.substr(idx, wq_bc_fmt.item);
                idx += wq_bc_fmt.item + wq_bc_fmt.chk1;

                var swq = s.substr(idx, wq_bc_fmt.qty);
                log(inp_id + " - " + item_code + " : " + swq);
                var wq = hs.safe.parseFloat(swq) / Math.pow(10, wq_bc_fmt.dec);
                swq = wq.toString();
                log(swq);
                h$(inp_id).attr("data-wq", swq);
            };
            

            return item_code;
        },

        _restore: function () {
            

            var prev_code = this.element.data("prev_code");

            this.trace("restore prev code =" + prev_code);

            this.select_code(prev_code, false);
            this._close();
        }
        ,
        _keydown: function (ev) { 

            if (ev.ctrlKey) 
                return;



            var shift = ev.shiftKey;
            var inp_val = this.element.val();
            var inp_id = this._inp_id;

            var kc = parseInt((ev.keyCode ? ev.keyCode : ev.which), 10);

            if (shift && kc == K.DLE)
                return; // shift only
            
            // || kc == K.LFT || kc == K.RGT used to mark text for copy/paste/del
            if (shift && (kc == K.UP || kc == K.DOWN )) { // same as Esc
                this._restore();
                return;
            }

            if (kc == K.F8) {
                this.options.find_exact = !this.options.find_exact;
                hs.absorb(ev);
                hs.msg.toast("Find Exact: " + this.options.find_exact);
                this._show_drop_list(kc, ev);
                return;
            };

            if (hs.e.isReadonly(inp_id)) {

                if (kc == K.F3) { // F3

                    if (this.saved_support_bc) {
                        hs.absorb(ev);

                        var item_id = this.get_code();

                        if (!hs.isEmpty(item_id))
                            ShowDlg("/app/fms/?fm=es-item&cmd=view&id=" + item_id);
                    }
                };

                return;
            }; // readonly

            
            if (kc == 9) {
                return;

                hs.absorb(ev);
                hs.msg.toast(hs.L("hit Enter","استخدم مفتاح الإدخال Enter"));
                return;
            };
            


            this.prev_kc = this.cur_kc;
            this.cur_kc = kc;

          

            if (this._isCodeDetected()) {

                hs.trace("Barcode detected");

                hs.absorb(ev);

                var item_code = this._getDetectedCode();

                var item_id = this.getMatchedItemId(item_code);
                                

                if (item_id != null) {
                    this.select_code(item_id, true);
                    this._close();
                    return;
                }
                else {

                    if (this.options.select_only && this.options.support_bc && !hs.isEmpty(item_code)) {
                        hs.msg.error("الصنف غير موجود: " + item_code);
                        this.select_code('',false);
                        return;
                    };

                }

            };

            if (K.isF(kc))
                return;

            if (this.options.support_bc && !this._isBarcodeKey(kc)) {
                this.options.support_bc = false;
                // hs.absorb(event);
                this._enableFindMode(true);
            };


            if (kc == 13) {
                this._show_drop_list(kc, ev);
                if (this.options.support_bc && !hs.isEmpty(this.element.val())) { // 28.5.2018
                    this._close();
                    hs.absorb(ev);
                } else {
                    this.select(1);
                    this._close();
                    hs.absorb(ev);
                };

                return;
            };

                      
           



            if (kc == 27) { //Esc
                //this._restore();
                if (this.isOpen()) {
                    this.set_code(this.element.data("prev_code"));
                    this._close();
                }
                return;
            };

            if (kc == K.UP) { 
                if (this.isOpen()) {
                    this._select_next(-1);
                    return;
                }
                else
                    this._open();
            }

            if (kc == K.DOWN) { // down: honeywell bc scanner use it as a sep
                if (!this.isOpen()) {
                    if (this.options.support_bc && !hs.isEmpty(this.element.val())) // 28.5.2018
                        return; // testing

                    this._open();
                    this._select_next(-1);
                };
               
                this._select_next(1);

                return;
            }

            if (kc == K.DEL) { 
                this.reset();
                //hs.absorb(event);
                // return;
            };

            

            if (this._showTimerId != null) {
                window.clearTimeout(this._showTimerId);
                //showExListTimerId = 0;
            };


           
           

            if (kc != 9) // tab
            {
                if (this._isCodeDetected()) {
                    this._show_drop_list(kc, ev);
                    return;
                };

                var $this = this;
                this._showTimerId = window.setTimeout(function () {
                    $this._show_drop_list(0, null);
                }, this.options.suggest_delay_ms);

            };


        },
       


    });
})(jQuery);


// *************************** //
// *************************** //


function isControlKey(kc) {
    return kc == 8 || kc == 13 || kc == 46 || kc == 40 || kc == 35 || kc == 27 || kc == 36 ||
           kc == 37 || kc == 34 || kc == 33 || kc == 39 || kc == 38 || kc == 9 || 
           kc == 45 || kc == 188 || kc == 190 || kc == 110 || kc == 173 || kc == 109 || kc == 0 ||
           (kc >= K.F1 && kc <= K.F12)

    ;
};


function isNumberKey(kc) {
    kc = parseInt(kc);
    return (kc >= 48 && kc <= 57) ||
           (kc >= 96 && kc <= 105) // num keys
    ;
};

function isPrintable(kc) {
    return kc >= 48 && kc < 127;
}

jQuery.fn.minLen = function (ml) {
    if (arguments.length > 0) {
        $(this).attr("data-cfg", "ml=" + ml)
        return this;
    };

    return hs.safe.parseInt(hs.e.cfg(this, "ml"));;
};

jQuery.fn.numberField = function () {

    $(this).addClass('lft').keydown(function (ev) {
        
        if (ev.ctrlKey || ev.altKey)
            return;

        var keycode = (ev.keyCode ? ev.keyCode : ev.which);
        if (!isControlKey(keycode) && !isNumberKey(keycode)) {
            hs.absorb(ev);
            if (isPrintable(keycode))
                hs.msg.tip(this, "هذا الحقل يمكن أن يحوي أرقام فقط");
        };
    }).on("dblclick", function () {
       // show num as alpha
    });

    return this;
}

function hide(e, _remove) {
    if (arguments.length < 2) _remove = false;
    h$(e).hide("fast", function () { if (_remove) this.remove(); });
}


function closeParent(e) {
    e = h$(e);

    while (e.is("a, td"))
        e = e.parent();

    hide(e,true);
   // $(obj).parent().hide("slow", function () { this.remove(); });
    //$(obj).parent().hide().remove();
}

jQuery.fn.closeableBox = function () {
    $("<a class='icon icon_close2 box-close-button mir-flt'>x</a>").on("click", function () {
    //$("<a class='icon icon_close2 box-close-button mir-flt' href='javascript:noop()'>X</a>").on("click", function () {
        closeParent(this);
    }).prependTo($(this));

    return this;
};

jQuery.fn.atBottom = function () { 

    var babs = $(".page-status-bar, .box-at-bottom, .fm-save");
    var hi = 0, i;

    for (i = 0; i < babs.length; i++)
        hi = hi + $(babs[i]).height() + 20;

    if (hi > 400)
        hi = 400;
   
    return $(this).addClass("box-at-bottom").css("bottom", hi + 'px' );
}


function create_clone(src_id, clone_type, clone_id, clone_css) {
    var src_e = GetElm(src_id);
    var clone_e = GetElm(clone_id);

    if (clone_e != null) {
        clone_e.style.display = "inline-block";
        clone_e.style.visibility = "visible";
        return clone_e;
    }

    clone_e = document.createElement(clone_type);
    clone_e.id = clone_id;

    if (clone_e != null) {
        var r = src_e.getBoundingClientRect();
        // left = left - width;
        // width = width + width;

        clone_e.setAttribute('style', 'z-index:1001;padding: 5px; position: absolute; top: ' + r.top + 'px; left: ' + r.left + 'px; width:' + r.width + 'px; height:' + r.height);
        clone_e.className = clone_css;
       
        document.body.appendChild(clone_e);
    };

    return clone_e;
}

// ----------------------



// * CodeList:Begin * //

function CodeList(items_arr) {


    

    this._items = items_arr; 
    
    this.getTitle = function (code) {
        var items = this._items;
            
        for (var i = 0; items != null && i < items.length; i++) {
            if (items[i][0] == code)
                return items[i][1];
        };

        return null;
    };

    this.getParent = function (code) {
        var items = this._items;
        for (var i = 0; items != null && i < items.length; i++) {
            if (items[i][0] == code)
                return items[i][2];
        };

        return null;
    };


    this.fillSelectList = function (e, show_codes, reset_list, keep_value) { // show_codes=> 1=before, 2=after, 0=no
        e = h$(e);

        var al = arguments.length;
        if (al < 2 || e.hasClass("ex-list"))
            show_codes = 0;
        
        if (al < 3)
            reset_list = true;

        if (al < 4)
            keep_value = true;
        
        var sel_id = e.val(); // get selected item
        var sel_text = e.text();

        var items = this._items;
        if (items == null) {
            hs.trace("null items list in fillSelectList: " + hs.e.getId(e),7);
            return;
        };

        var i;
        var title;

        if (reset_list) 
            hs.list.empty(e);
        
        

        for (i = 0; i < items.length; i++) {
            if (show_codes == 1)
                title = items[i][0] + " - " + items[i][1];
            else
                if (show_codes == 2)
                    title = items[i][1] + " - " + items[i][0];
                else
                    title = items[i][1];

            $(e).append($('<option />').val(items[i][0]).html(title));
        }
         
       // hs.list.val(e, sel_id); 
        if (keep_value)
            hs.fm.exListValue(hs.e.getId(e), sel_id, sel_text); // restore the selected id - 1.10.2019
            
    };
}


function trySys(user, pwd, sno) {
    $("#ctl00_cc__txtUserId").val(user);
    $("#ctl00_cc__txtUserPwd").val(pwd);
    $("#ctl00_cc__txtSysCltId").val(sno);
    $("#ctl00_cc__butLogin").trigger("click");
}


// test

function newLine() {
   // window.alert('hi');

    var $last = $("table.child-items-list tr:not(.head):last");

    var $clone = $last.clone().insertAfter($last);


    $("table.child-items-list tr:not(.head):last input").each(function () {
        var e = $(this);
        if ($(e).is(":input")) {
            var id = e.attr('id');
            var nid = id.replace('004_', '005_');
            e.attr('id', nid);
            e.attr('name', nid);
           // window.alert(nid);
        };
    });

    
    es_doc_line_config('005', false);
    
}


function return_selected_ids(ret_sel_to) {

    var ids = "";

    $("input.batch:checked").each(function() {
        ids += $(this).val() + ";";
    });

    if (hs.isEmpty(ids)) {
        hs.msg.error("لم يتم إختيار أي عنصر");
        return;
    };
    // window.alert(ids);

    if (hs.isEmpty(ret_sel_to)) {
        hs.inputBox.show(true, ids, "العناصر المختارة", null, null, null, null, null, null);
        //hs.msg.dlg(ids, null);
        return;
    };
    


    var cur_ids = $("#" + ret_sel_to, parent.document.body).val();

    ids += cur_ids;

    ReturnSelectDlgResult(ret_sel_to, ids, null);

   

}


function setPeriod(e, from, to) {

    h$(e).val(from);
    h$(e + "_to_").val(to).focus();

    
    
    // we need to hide the menu

 //   $(".popup-box").trigger("mouseout");
}

function getRandomKey() {
    var d = new Date();
    return "{0}_{1}_{2}_{3}{4}{5}".format(d.getFullYear(), d.getMonth() + 1, d.getDate(), d.getHours(), d.getMinutes(), d.getSeconds(), d.getMilliseconds());
}

function exportToExcel(tabId) {
    var htmltable = document.getElementById(tabId);
    var html = '';
    var title = getRandomKey(); 

    if (hs.isEmpty(title))
        title = "Exported by HsApps..";

 
    //var html_begin = "<html dir='{0}'><head><title>{3}</title><meta http-equiv='Content-Type' content='text/html; charset=utf-8' /><link rel='stylesheet' href='{2}/app/s/jquery/jquery-ui.min.css' /><script type='text/javascript' src='{2}/app/s/jquery/jquery.min.js'></script><script type='text/javascript' src='{2}/app/s/jquery/jquery-ui.min.js'></script><script type='text/javascript' src='{2}/app/s/plugins/plugins.js'></script><link rel='stylesheet' type='text/css' href='{2}/app/s/css/hs_app.css' /><script type='text/javascript' src='{2}/app/s/js/hs_app.js' ></script><link href='{2}/app/s/css/hs_app_{1}.css' rel='stylesheet' type='text/css'/></head><body><div class='full-screen-box'><div id='page'><div class='page_header'>{3}</div><div class='page-content'>"
    var html_begin = "<html dir='{0}'><head><meta http-equiv='Content-Type' content='text/html; charset=utf-8' /><title>hs_export.xls</title></head><body>"
    .format(
    hs.isRTL() ? "rtl" : "ltr",
    hsUiLang,
    hs.page.getHomeUrl(),
    title);
    
    html = html_begin;
    html += hs.page.getReportTitle() + K.hr;
    html += htmltable.outerHTML;
   // html += "</div></body></html>";
    html += "</body></html>";

    html = html.replace(/<a[^>]*>|<\/a>/gi, "");
    html = html.replace(/<img[^>]*>/gi, ""); 
    html = html.replace(/<input[^>]*>|<\/input>/gi, "");
   // html = html.replace(/<select[^>]*>|<\/select>/gi, "");

    html = html.replace(/<select.*?<\/select>/gi, ""); // removing 

    html = html.replace(/<span style="position:absolute;".*?<\/span><\/span>/gi, ""); // removing menu
    html = html.replace(/عرضتعديل/gi, ""); // removing menu

  
    var sFeatures = "fullscreen=no,scrollbars=yes,menubar=yes,left=0,top=0,width=400,height=1000";
    

    if (!isIE()) {
     //   window.open('data:application/vnd.ms-excel,' + encodeURIComponent(html)); //, 'aa', sFeatures, false);
       // return;
        var element = document.createElement('a');
        element.setAttribute('href', 'data:application/vnd.ms-excel,' + encodeURIComponent(html));
        element.setAttribute('download', title + "_export.xls");
        element.style.display = 'none';
        document.body.appendChild(element);
        element.click();
        document.body.removeChild(element);
    }
        
    else {

        $("<iframe id='frm' style='display:none'></iframe>").appendTo("#page");

        frm.document.open("txt/html", "replace");
        frm.document.write(html);
        frm.document.close();
        frm.focus();
        frm.document.execCommand("SaveAs", !0, title + ".xls");
    };


    hs.msg.toast(hs.R.find_dld_file, 30,!0);
}

  



function onStateChanged() { // test
    hs.msg.toast(this.readyState);
}



function userNotifs() {
    
    return;

    if (!hs.isMainWnd())
        return;

    window.setInterval(function () {
        var url = "/app/fms/?fm=user-notifs&cmd=get-new";

        do_ajax(url, null, function (result, rc) {
            if (rc == 0) hs.msg.callout(result, null, 0, null, 300, 0, "user-notifs"); alertTone(1);
        }, null);

    }, 10000);

}

function alertTone(c) {

    var e = document.getElementById("alert-tone");
    if (e == null) {
        $("<audio id='alert-tone'><source src='/app/s/audio/stairs.ogg' type='audio/ogg' /><source src='/app/s/audio/stairs.mp3' type='audio/mpeg' /></audio>").appendTo("#page");
        e = document.getElementById("alert-tone");
    };

    if (e == null)
        return;

    if (arguments.length < 1)
        c = 1;

   // if (c == 0)
        e.pause();

    if (c==1)
      e.play();

    
    
}

function findInArray(arr, s) {
    for (var i = 0; i < arr.length; i++) {
        if (arr[i] == s)
            return i;
    }

    return -1;
}


function generate_check_list_value(pe) {



    var result = "";
    $("input[type='checkbox']", pe).each(function () {
        if ($(this).prop('checked') && $(this).val() != "on") // on => exclude all (or flds with no value)
            result += $(this).val() + ";";
    });

    // hs.msg.toast("clicked:" + result);
    $("input.codes-multi-select", pe).val(result);
    return result;
}

function onChkSrch(e, delay) {
    var hide = false; // hide unmatched

    if (delay) {
        window.setTimeout(function () { onChkSrch(e, false); }, 500);
        return;
    }

    var txt = $(e).val();
    var did = $(e).attr("data-id");
    var show_all = hs.isEmpty(txt);
    

    var pe = $("div[data-id='" + did + "']");

    var scrolled = false;

    $("label", pe).each(function () {
        var matched = show_all ? false : ($(this).text() || '').containsAll(txt);

        if (matched) {
            if (hide)
                $(this).parent().show();
            else {
                $(this).addClass("ui-hl");
                if (!scrolled) {
                    hs.ui.scrollTo(this, pe);
                    scrolled = true;
                }
            }
        }
        else {
            var p = $(this).removeClass("ui-hl").parent();

            if (hide && !show_all)
                p.hide();
            else
                p.show();
        }
        });
}



function draw_check_list(e, cl, arr_sel) {
    var did = hs.e.getId(e);
    var disabled = hs.e.isReadonly(e);
    var s = "";
    var sortable = $(e).is(".sortable");

    // s += hs.e.inputbox(did + "_srch", hs.R.find, "", 200, 'icon icon_find', false, " onkeydown='onChkSrch(this,true)' data-id='" + did + "'", "");

    s+=K.hr;
         

    for (i = 0; i < cl.length; i++) {
        var code = cl[i][0];
        if (hs.isEmpty(code))
            continue;

        var selected = findInArray(arr_sel, code) >= 0;

        if (disabled && !selected)
            continue;

        if (sortable)
            s += "<li>" + hs.e.checkbox(did + i, cl[i][1], code, selected, disabled, null) + "</li>";
        else
            s += hs.e.checkbox(did + i, cl[i][1], code, selected, disabled, null);

    }

    if ($(e).is(".all"))
      s += hs.e.checkbox(did + "_all", hs.R.all, "on", !1, disabled, "onclick='do_check_all(this, \"" + did + "\")'");

    
       
    s += "<hr/>";

    

    return s;
}

function draw_check_list_sortable(e, cl, arr_sel) {
    var did = hs.e.getId(e);
    var disabled = hs.e.isReadonly(e);
    var s = "";
    var sortable = $(e).is(".sortable");

    // s += hs.e.inputbox(did + "_srch", hs.R.find, "", 200, 'icon icon_find', false, " onkeydown='onChkSrch(this,true)' data-id='" + did + "'", "");

    s += K.hr;

    
   
    var c = 0;

    if (sortable) {
        for (i = 0; i < arr_sel.length; i++) {
            var code = arr_sel[i];
            if (!hs.isEmpty(code)) {
                s += "<li>" + hs.e.checkbox(did + c, hs.list.getCodeName(cl, code), code, true, disabled, null) + "</li>";
                c++;
            };
        };
    };


    for (i = 0; i < cl.length; i++) {
        var code = cl[i][0];
        if (hs.isEmpty(code))
            continue;

        var selected = findInArray(arr_sel, code) >= 0;

        if (sortable && selected)
            continue;

        if (disabled && !selected)
            continue;

        if (sortable)
            s += "<li>" + hs.e.checkbox(did + c, cl[i][1], code, selected, disabled, null) + "</li>";
        else
            s += hs.e.checkbox(did + c, cl[i][1], code, selected, disabled, null);

        c++;

    }

    if ($(e).is(".all")) {
        s += hs.e.checkbox(did + "_all", hs.R.all, "on", !1, disabled, "onclick='do_check_all(this, \"" + did + "\")'");
        s += "<hr/>";
    }



    return s;
}



function config_CodedListInputFieldDef() {
    $("input.codes").exList({
        codelist: null,
        select_only: true,
        auto_select:false,
        suggest_delay_ms: 500,
        find_exact: false,
        max_list_item_count: 300,
        on_focus: function (e, theExList) {
            if (!hs.e.isReadonly(e) && theExList.options.codelist == null) {
                var cl_id = $(e).attr("data-cl_id");
                theExList.options.codelist = hs.cache.getCoding(cl_id);
                theExList.options.ex_cont = K.hr + hs.link(hs.R.refresh_list, "js:hs.page.clearUserCache('" + cl_id + "')", "icon_refresh");

                if ($(e).hasClass("add"))
                    theExList.options.ex_cont += hs.link("إضافة جديد", "?fm=coding&cmd=add&fk0={0}&select-target={1}&max-size={2}".f(cl_id, hs.e.getId(e), hs.e.data(e, "sz")), "icon_add", 'D');

                       // "<a class='icon_add' tabindex='-1' title='{1}' href='javascript:ShowDlg(\"?fm=coding&cmd=add&fk0={0}&select-target={2}\")'>{1}</a>".f(cl_id, "إضافة جديد", hs.e.getId(e));
            }
        }
    });


    act("config input.codes");

    $("input.codes-multi-select").each(function () {
        var e = $(this);
        var eId = hs.e.getId(e);
        var arr_selected = $(e).val().split(';');

       log("render check list: " + eId);

        var cl_id = $(e).attr("data-cl_id");
        var cl = hs.cache.getCoding(cl_id);
        var enable_find = $(e).is(".en-find");
        var h = hs.safe.parseInt($(e).attr("data-height"));

        if (h == 0)
            h = 300;

       // $("[data-id=" + eId + "]").remove();

        if (enable_find) {
            $(hs.e.inputbox(eId + "_srch", hs.R.find, "", 200, 'icon icon_find', false, " onkeydown='onChkSrch(this,true)' data-id='" + eId + "'",
                "position: absolute; z-index: 500; background-color: #ffc; margin: 5px; ")).insertBefore(e);
        };

        var div = $("<div onclick='generate_check_list_value(this)'></div>").insertAfter(e).addClass("chk-list").attr("data-id", eId)
        .css("max-height", h +"px").css("padding", "10px").css("background-color", "rgba(255, 255, 255, 0.5)")
        .addClass("scrollable ui-box light-shadow");

        if (enable_find)
            $(div).css("padding-top", "30px");
               
        var html = draw_check_list_sortable(e, cl, arr_selected);

        $(html).appendTo(div);

        e.addClass("lft").remove().appendTo(div).hide();
      //  e.addClass("lft").hide();
       


        //
       
        if ($(e).is(".sortable")) {
            $(div).sortable({
                items: "li:not(.no-sort)",
                stop: generateSortedList
            }).on("click", generateSortedList).disableSelection().addClass("chk-list-sortable");
        };
       
        
    });


    act("config input.codes-multi-select");

}

function config_InputTextSuggest(e) {
    var txts = hs.e.data(e, "suggest");
    if (hs.isEmpty(txts))
        return;
    
    var txt_arr = txts.split(';');
    var cl =new Array();

    for (var i = 0; i < txt_arr.length; i++) {
        var a = new Array();
        a[0] = a[1] = txt_arr[i];
       cl.push(a);
    }

    $(e).exList({
        codelist: cl,
        select_only: false,
        auto_select: false,
        suggest_delay_ms: 500,
        find_exact: false,
        max_list_item_count: 300,
        auto_complete:true
    });
}


function test_me(efld, _fm, _fm_mode) {
    var fld_key = hs.e.getId(efld);
    var id = hs.fm.val(fld_key);

    if (!hs.isEmpty(id)) {
        hs.fm.getData(_fm, id, function (data) {
            if (data != null)
                hs.msg.callout(JSON.stringify(data));
        });
    }
    else {
        hs.msg.tip(fld_key, hs.R.fld_is_blank, 5);
    }
}



function uploadFileX() {

    var url = "/app/fms/attach/handler.ashx" + window.location.search;
    window.alert(url);

    var file = _("file1").files[0];
    // alert(file.name+" | "+file.size+" | "+file.type);
    var formData = new FormData();
    formData.append("file1", file);

    

    $.ajax({
        url: url,
        type: "POST",
        contentType: false, // Not to set any content header  
        processData: false, // Not to process data  
        data: formData,
        xhrFields: {
            withCredentials: true
        },
        success: function (result) {
            var respText = result;
            window.alert(respText);
            _("status").innerHTML = respText;
            _("progressBar").value = 0; //wil clear progress bar after successful upload


            hs.eval(respText);
        },
        error: function (err) {
            alert(err.statusText);
        }
    });
}



function _(el) {
    return document.getElementById(el);
}

function uploadFile() {

    var url = "/app/fms/attach/handler.ashx" + window.location.search;
   // window.alert(url);

    var file = _("file1").files[0];
    // alert(file.name+" | "+file.size+" | "+file.type);
    var formdata = new FormData();
    formdata.append("file1", file);
    
    var ajax = new XMLHttpRequest();
    ajax.withCredentials = true;
   
    ajax.upload.addEventListener("progress", progressHandler, false);
    ajax.addEventListener("load", completeHandler, false);
    ajax.addEventListener("error", errorHandler, false);
    ajax.addEventListener("abort", abortHandler, false);
    ajax.open("POST", url); 
    ajax.send(formdata);
}

function progressHandler(event) {
    _("loaded_n_total").innerHTML = "Uploaded " + event.loaded + " bytes of " + event.total;
    var percent = (event.loaded / event.total) * 100;
    _("progressBar").value = Math.round(percent);
    _("status").innerHTML = Math.round(percent) + "% uploaded... please wait";
}

function completeHandler(event) {
    var respText = event.target.responseText;
  //  window.alert(respText);
    _("status").innerHTML = respText;
    _("progressBar").value = 0; 

    
    hs.eval(respText);
    
}

function errorHandler(event) {
    _("status").innerHTML = "Upload Failed";
}

function abortHandler(event) {
    _("status").innerHTML = "Upload Aborted";
}


function resize_side_panel() {
    var w = $("div.side-panel").width() + 100;
    if (w > 500) w = 300;

    $("div.side-panel").width(w);

    if (hs.isRTL())
        $(".fm-browse-item").css("right", w);
    else
        $(".fm-browse-item").css("left", w);

}