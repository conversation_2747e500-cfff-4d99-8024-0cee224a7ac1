﻿<?xml version="1.0" encoding="utf-8"?>
<SystemHelp xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <items>

    <Entry>
      <id>acc_type</id>
      <title>نوع الحساب</title>
      <content>
        <![CDATA[
نوع الحساب مثل حساب صندوق او حساب بنكي او عميل أو مورد أو موظف و غيره

هذا الحقل سيحدد ظهور الحساب في الشاشات و المستندات فمثلا عند عمل سند صرف نقدي ستظهر كل الحسابات التي من النوع صندوق و هكذا بالنسبة للعملاء في فواتير البيع أو الموردين في فواتير المشتروات
]]>
      </content>
    </Entry>
    
    
       <Entry>
     <id>acc_hier_cat</id>
     <title>رئيسي/فرعي</title>
     <content>
       <![CDATA[
هذا الحقل يحدد هل الحساب هو حساب رئيسي أو فرعي.
1 : حساب رئيسي
2 : حساب فرعي
]]>
     </content>
        <linked>acc_parent</linked>
   </Entry>


    <Entry>
      <id>ma_acc_no</id>
      <title>رقم الحساب *</title>
      <content>
        <![CDATA[
هو رقم الحساب في الدليل المحاسبي ، و يجب أن يكون فريد و لا يتكرر في أي حساب آخر مهما كان نوع الحساب
و يمكن أن يتكون من أرقام وحروف

وعند إنشاء الحساب يمكن ترك الرقم فارغا و سيقوم النظام آليا بتحديد رقم الحساب بناءا على نوع الحساب و كذلك على الحساب الرئيسي، حيث سيتم إنشاء حسابات متسلسلة تحت الحساب الرئيسي
مع عمل تسلسل مختلف بناءا هل الحساب المضاف فرعي أو رئيسي 

الحساب  يجب أن يكون لديه عملة رئيسية بأي عملة في النظام - و يفضل أن تكون العملة الأساسية هي العملة المحلية
و يمكن إضافة عملات لهذا الحساب على نفس رقم الحساب

و لا يمكن تعديل رقم الحساب بعد إضافة الحساب

]]>
      </content>
    </Entry>
    
    
       <Entry>
     <id>fi_acc_no</id>
     <title>رقم الحساب</title>
     <content>
       <![CDATA[
رقم الحساب مع العملة المرتبطة به

و لا يمكن تعديل رقم الحساب بعد إضافة الحساب

]]>
     </content>
   </Entry>
    
    
       <Entry>
     <id>acc_crncy</id>
     <title>عملة الحساب</title>
     <content>
       <![CDATA[
العملة التي ستحدد رصيد الحساب و كذلك كشف الحساب
    
ويمكن تعديل عملة الحساب طالما أن الحساب ليس عليه حركات سابقة وحتى و لو كانت ملغية 

و يقوم النظام بعمل تحويل للمبالغ التي بعملات آخرى إلى عمله الحساب إذا كانت عملة الحركة تختلف عن عملة الحساب

و يمكن إعداد النظام بحيث يشترط أن تطابق عملة الحساب عملة الحركة او المستند  
       
]]>
     </content>
   </Entry>
    
    
       <Entry>
     <id>acc_name</id>
     <title>اسم الحساب</title>
     <content>
       <![CDATA[
       الإسم الذي سيظهر به الحساب في الشاشات والقوائم
       
يفضل أن يتم إختيار اسم معبر عن الحساب بحيث يسهل البحث عنه

     و يمكن تعديل اسم الحساب
]]>
     </content>
   </Entry>


    <Entry>
      <id>acc_depth</id>
      <title>الرتبة</title>
      <content>
        <![CDATA[
الرقم الذي يحدد رتبه الحساب في الفرع الذي يتبعه - وهذا الرقم يحدد آليا و يتم تعديله عند نقل الحساب إلى فرع آخر عن طريق تعديل الحساب الرئيسي

والنظام يدعم 9 مستويات في الدليل المحاسبي
      
]]>
      </content>
    </Entry>
    
    
       <Entry>
     <id>acc_status</id>
     <title>الحالة</title>
     <content>
       <![CDATA[
حالة الحساب هل مفعل أو موقف أو غير محقق

     لن يتم السماح بأي حركات مالية على الحساب إذا كان غير مفعل
     
     في النسخة الإحترافية يتم إنشاء الحساب بحالة (غير محقق) وهذا يسمح للشركة بعمل صلاحيات إنشاء حساب و تحقيق حساب. فمثلا يمكن منح صلاحية إضافة الحساب للمحاسب و تحقيق الحساب لمدير الحسابات
     
]]>
     </content>
   </Entry>

    
       <Entry>
     <id>fi-period</id>
     <title>إدارة الفترات المحاسبية</title>
     <content>
       <![CDATA[
        للوصول إلى شاشة الفترات المحاسبية: الحسابات - البيانات الأساسية - السنة المالية 
       
    الفترة المحاسبية يمكن أن تكون سنة أو أكثر أو أقل من ذلك كربع السنة أو أشهر من السنة وتختلف باختلاف طبيعة النشاط ومدى أهمية التقارير المالية للإدارة.
    
     يوفر النظام مرونة في تحديد الفترات المحاسبية لأي مدة بحيث لا تقل عن شهر. 
    إمكانية تغيير مدة الفترة (تقصير/تمديد/تقسيم) - بشرط عدم وجود قيود مالية خلال فترة التقصير أو التمديد.
    إمكانية تعليق الفترة كلها أو تعليق أشهر معينة خلال الفترة،
    إمكانية العمل على عدة فترات مفتوحة في نفس الوقت.
    
 <h6>إغلاق الفترة المفتوحة</h6>   
    يوفر النظام أدوات تسهل عملية الإغلاق وبخطوات مرتبة وميسرة بحيث يتم تنفيذها خطوة بعد أخرى حتى تكتمل عملية الإغلاق بشكل مرن وسلس. يفضل بشدة أن يتم إتباع هذه الخطوات، و لكن ذلك ليس إجباريا.      يمكن الوصول لخطوات الإغلاق من القائمة الرئيسية: الحسابات - الترحيل والإغلاق - خطوات إغلاق الفترة المحاسبية
    
    لإغلاق الفترة يجب أن تكون كل الفترات السابقة لها مغلقة، و كل المستندات التابعة للفترة مرحلة
    
    
    إمكانية محاكاة إغلاق الفترات بدون عمل إغلاق فعلي – وذلك لفحص إمكانية إغلاق الفترات.
    ينتج عن عملية الإغلاق مستند نظام آلي   ومرتبط به عدة قيود مالية - يمكن الإطلاع عليها من خلال تقارير اليومية العامة، أو مستندات النظام الآلية.
    يمكن عكس التأثيرات المالية الناتجة عن عملية الإغلاق عن طريق عكس القيود المالية عن عملية إقفال الفترة وذلك بالغاء ترحيل مستند النظام الآلي .

<h6>إعادة فتح الفترة المالية المغلقة</h6>
    يمكن إعادة فتح الفترة المالية المغلقة بشرط أن تكون الفترات التي بعدها كلها مفتوحة، عند إعادة فتح الفترة المغلقة لن يقوم النظام بعمل عكس آلي للقيود الناتجة عن عملية الإغلاق (لأن ذلك غير ضروري) ولكن إذا كان هناك حاجة لعمل عكس القيود يمكن للمستخدم عمل ذلك يدويا وذلك بالغاء ترحيل مستند النظام الآلي الناتج عن عملية الإغلاق. بعد اعادة الفتح يمكن إضافة و ترحيل المستندات، و يمكن بعدها إغلاق الفترة مرة أخرى بشكل طبيعي. 


]]>
     </content>
   </Entry>


    <Entry>
      <id>prd-suspended</id>
      <title>الفترة معلقة</title>
      <content>
        <![CDATA[
عند تعليق الفترة المحاسبية ، لن يسمح النظام بإنشاء أو تعديل أو ترحيل أي مستندات خلال تلك الفترة. وكذلك لن يتم السماح بتعديل


يمكن اللجوء إلى هذه الخطوة قبل الإقفال الفعلي  بحيث يتم السماح بإعطاء فرصة لتصفية المستندات العالقة. 

]]>
      </content>
    </Entry>


    <Entry>
      <id>fi-crncy</id>
      <title>إدارة العملات في النظام</title>
      <content>
        <![CDATA[
        يجب أن يتم تعريف العملات التي سيتعامل بها النظام، و يجب على الأقل تعريف عملة واحدة هي العملة المحلية، وبشكل آلي فإن أول عملة يتم تعريفها في النظام سيعتمدها النظام كعملة محلية، و سيتم إظهار ملاحظة تبين ذلك للمستخدم قبل الحفظ.
        يوفر النظام عدد من المزايا:
<ul>
<li>يسمح النظام بتعريف عملة محلية واحدة وأي عدد من العملات الأجنبية</li>
<li>إمكانية تحديد سعر التحويل (الصرف) للعملة الأجنبية إلى العملة المحلية على مستوى النظام وكذلك تحديد الحد الأعلى والأدنى لسعر التحويل، و يمكن تعديل سعر التحويل في اي وقت عند الحاجة</li>
<li>إمكانية السماح للمستخدم من تغيير سعر الصرف على مستوى المستند / قيد اليومية (إذا كان لديه الصلاحية) وبشرط عدم تجاوز الحد الأدنى والأعلى لصرف العملة والمحددين في بيانات العملة.</li>
<li>يمكن تعطيل العملة وبالتالي إيقاف التعامل معها في كل المستندات &ndash; وعدم حفظ أو ترحيل أي مستند بهذه العملة.</li>
<li>يقوم النظام آليا بحساب المقابل المحلي لكل العمليات التي بالعملة الأجنبية.</li>
<li>يسمح النظام بعمل تقييم للعملات الأجنبية خلال أي وقت &ndash; الأمر الذي ينتج عنه ربح أو خسارة تقيد بتاريخ نهاية الفترة المالية. وفي حال وجود أكثر من فترة مالية مفتوحة فإن التقييم سيتم على مستوى الفترة الواحدة بحيث يتم حساب الربح والخسارة الناتج عن فروق العملة حسب كل فترة.</li>
<li>يسمح النظام بعمل محاكاة لعملية تقييم العملات &ndash; لمعرفة أثر حركة تقييم العملة بدون التأثير الفعلي على الحسابات.</li>
<li>عملية التقييم يجب أن تتم على مستوى كل عملة أجنبية، ويفضل أن تتم هذه العملية خلال عملية إغلاق الفترة المالية.</li>
<li>يمكن عكس التأثير المالي لحركة تقييم العملات &ndash; وذلك بعمل إلغاء ترحيل القيد المالي الناتج عن عملية التقييم.</li>
<li>يوفر النظام أدوات وشاشات للتحويل بين الحسابات التي بعملات مختلفة، دون الحاجة لعمل قيود يدوية.</li>
<li>يمكن إعداد النظام للسماح بعمل تحويل بين عملتين أجنبيتين وذلك بتوسيط سعر الصرف للعملة المحلية لكل عملة منهما</li>
</ul>
يمكن الوصول إلى شاشة إدارة العملات من خلال  القائمة :<b> الحسابات / البيانات الأساسية / العملات</b>
]]>
      </content>
    </Entry>

    <Entry>
      <id>cur_name</id>
      <title>اسم العملة</title>
      <content>
        <![CDATA[
        الإسم الكامل للعملة مثل (دولار أمريكي)،  سيتم إظهار هذا الأسم عند عرض المبالغ كتابة.. 
]]>
      </content>
    </Entry>

    <Entry>
      <id>cur_short</id>
      <title>الإسم المختصر</title>
      <content>
        <![CDATA[
  مثل: دولار أو $  أو USD . هذا الإسم المختصر سيظهر في الوثائق و القوائم و أثناء الطباعة
]]>
      </content>
    </Entry>

    <Entry>
      <id>cur_fraction</id>
      <title>اسم الفكة</title>
      <content>
        <![CDATA[
         الفكة هي كسور الواحد من العملة، مثلا (سنت) للدولار، و(فلس) للريال. سيتم إظهار هذا الأسم عند عرض المبالغ كتابة إذا كان المبلغ يحوي كسور. مثلا عند عرض مبلغ 150.20$ ستظهر كتابة: مائة وخمسون دولار أمريكي و عشرون سنت.
]]>
      </content>
    </Entry>

    <Entry>
      <id>cur_status</id>
      <title>حالة العملة</title>
      <content>
        <![CDATA[
يجب أن تكون العملة مفعلة حتى يمكن التعامل بها.       
يمكن تعطيل العملة وبالتالي إيقاف التعامل معها في كل المستندات – وعدم حفظ أو ترحيل أي مستند بهذه العملة
]]>
      </content>
    </Entry>

    <Entry>
      <id>ex_rate</id>
      <title>سعر التحويل</title>
      <content>
        <![CDATA[
سعر تحويل العملة الأجنبية إلى العملة المحلية ، سيتم إعتماد هذا في كل الفواتير والمستندات مالم تم تحديد سعر تحويل مخصص على مستوى المستند أو القيد. 
]]>
      </content>
    </Entry>

    <Entry>
      <id>min_rate</id>
      <title>الحد الأدنى لسعر تحويل العملة</title>
      <content>
        <![CDATA[
الحد الأدنى لسعر التحويل للعملة، سيرفض النظام عند تحديد سعر تحويل أقل من هذا السعر في الفواتير والمستندات.
القيمة صفر تعني إنه لن يكون حد أدنى، و يفضل دوما تعيين قيمة بحيث تساعد عند حصول خطأ في إدخال سعر التحويل يدويا.
]]>
      </content>
    </Entry>

    <Entry>
      <id>max_rate</id>
      <title>الحد الأقصى لسعر تحويل العملة</title>
      <content>
        <![CDATA[
 سيرفض النظام عند تحديد سعر تحويل أعلى من هذا السعر في الفواتير والمستندات.
 القيمة صفر تعني إنه لن يكون حد أعلى، و يفضل دوما تعيين قيمة بحيث تساعد عند حصول خطأ في إدخال سعر التحويل يدويا.
]]>
      </content>
    </Entry>

    
    
    
       <Entry>
     <id>acc_grp</id>
     <title>مجموعة الحساب</title>
     <content>
       <![CDATA[حقل مجموعة الحساب يستخدم لتقسيم الحسابات إلى مجموعات بحيث يمكن تقسيم إدارة الحسابات بين المحاسبين، 
مثلا يمكن أن يتم تحديد مجموعة للموردين وتحديد هذه المجموعة مثلا لمحاسب معين، و بالتالي سوف يكون له صلاحية التعامل مع حسابات هذه المجموعة.

في حال ترك مجموعة الحساب فارغة في بيانات الحساب، هذا يعني إنها مفتوحة لكل المحاسبين.

يمكن تعيين المجموعات المحاسبية للمستخدم من الصلاحيات الإضافية للمستخدم.

إذا لم يكن للمستخدم الصلاحية على مجموعة الحساب فلن يتمكن من:

-  إدارة بيانات الحساب ( إضافة , تعديل, عرض و غيرها) بيانات الحساب
- عرض رصيد الحساب
- عرض كشف الحساب
- ادارة بيانات الموازنة التقديرية للحساب

]]>
     </content>
   </Entry>
    
       <Entry>
     <id>post_purch_discount</id>
     <title>ترحيل تخفيضات ومسموحات المشتروات - إضافة مبلغ الخصم على كلفة المخزون</title>
     <content>
       <![CDATA[
في حال تفعيل هذا الخيار سيقوم النظام بإضافة قيد مالي (من حـ/المخزون - إلى حـ/الخصم المكتسب) بمبلغ الخصم. و بالتالي رفع كلفة الأصناف

]]>
     </content>
   </Entry>
    
       <Entry>
     <id>post_purch_free</id>
     <title>ترحيل الكميات المجانية للمشتروات - إضافة قيمة الكميات المجانية على كلفة المخزون</title>
     <content>
       <![CDATA[
في حال تفعيل هذا الخيار سيقوم النظام بإضافة قيد مالي (من حـ/المخزون - إلى حـ/الخصم المكتسب) بكلفة الكمية المجانية. و بالتالي رفع كلفة الأصناف
]]>
     </content>
   </Entry>


    


    <Entry>
      <id>fi-rep-dsgn</id>
      <title>مصمم التقارير والقوائم المالية</title>
      <content>
        <![CDATA[
مصمم التقارير والقوائم المالية هو أداة تساعدك في تصميم تقارير الحسابات الختامية و التقارير المالية التحليلة و القوائم المالية مثل قائمة الدخل والتدفقات النقدية وغيرها.



]]>
      </content>
    </Entry>

	  <Entry>
		  <id>fi_rep_id</id>
		  <title>كود التقرير</title>
		  <content>
			  <![CDATA[
يتم إختياره آليا بواسطة النظام، وهو يستخدم لتنفيذ التقرير لاحقا

]]>
		  </content>
	  </Entry>

	  <Entry>
		  <id>rep_name</id>
		  <title>عنوان التقرير</title>
		  <content>
			  <![CDATA[
يظهر عند طباعة التقرير كعنوان للتقرير

]]>
		  </content>
	  </Entry>

	  <Entry>
		  <id>rep_memo</id>
		  <title>بيان التقرير</title>
		  <content>
			  <![CDATA[
حقل إختياري، يظهر عند طباعة التقرير

]]>
		  </content>
	  </Entry>

	  <Entry>
		  <id>rep_foot</id>
		  <title>تذييل التقرير</title>
		  <content>
			  <![CDATA[
حقل إختياري، يمكن إضافة أي تذييل أو ملاحظات في نهاية التقرير

]]>
		  </content>
	  </Entry>

	  <Entry>
		  <id>rep_desc</id>
		  <title>الوصف</title>
		  <content>
			  <![CDATA[
حقل إختياري، و لا يطبع مع التقرير، و يمكن إستخدامه لإضافة أي ملاحظات أو وصف للتقرير خاص بمصمم التقرير 

]]>
		  </content>
	  </Entry>

	  <Entry>
		  <id>fi-rep-dsgn:lines</id>
		  <title>تفاصيل التقرير</title>
		  <content>
			  <![CDATA[
			  
رقم الحساب:
رقم الحساب المراد إظهار رصيده في البند و الحساب ممكن يكون فرعي أو رئيسي. و سوف يتم إظهار اسم الحساب إذا لم يتم ادخال اسم البند يدويا.

رمز البند:
رمز البند يجب أن يبدأ بحرف A مثلا A1, A2, A3 ,... وهذه الرموز يمكن أن تستخدم في المعادلات. 

اسم البند:
هو الإسم الذي سوف يظهر في القرير، إذا تم تركه فارغا سيتم إستخدام اسم الحساب. وإذا لم يكن هناك حساب سيظهر كسطر فارغ في التقرير

نوع الرصيد:
هو نوع الرصيد للحساب المحدد.

متوسط رصيد الفترة: هو مجموع رصيد بداية الفترة + رصيد نهاية الفترة مقسموما على 2

أجنبي:
 عند تفعيل هذا الخيار سوف سيتم جلب رصيد العملة الأجنية للحساب، و لذلك يجب إختيار العملة عند تنفيذ التقرير.

المعادلة:
يمكن إضافة معادلات لإجراء عمليات رياضية بين قيم البنود و يمكن الإشارة إلى البند بإستخدام رمز البند مثلا يمكن إضافة معادلة في السطر الرابع مثلا بالشكل
A1 + A2 - A3
وذلك لجمع قيمة السطر الأول مع الثاني ثم طرح الصف الثالث من المجموع
أو مثلا 
A1 * A2 / A3
وذلك لضرب قيمة السطر الأول مع الثاني ثم قسمة الناتج على قيمة الصف الثالث.
يمكن استخدام العمليات الرياضية (الجمع والطرح والضرب والقسمة) في المعادلات. مع العلم أن العمليات تنفذ من اليسار إلى اليمين

تمييز:
نص يظهر بعد الرصيد أو ناتج المعادلة، مثلا يمكن إضافة علامة النسبة المئوية عند الحاجة

إخفاء:
عند تفعيل هذا الخيار لن يتم عرض البند في التقرير. يمكن إستخدامه لإخفاء البنود قيد التطوير، أو لإخفاء البنود التي تستخدم لعمل مجاميع وسيطة أثناء تطبيق المعادلات و لا يوجد داعي لعرضها.

]]>
		  </content>
	  </Entry>
    
    
            <Entry>
      <id>fi-gl-entry</id>
      <title>شاشة القيود اليومية</title>
      <content>
        <![CDATA[
للدخول على شاشة القيود اليومية:  الحسابات –  القيود اليومية

الغرض من هذه الشاشة هو إدخال القيود اليومية اليدوية وغالبا ماتستخدم في إدخال قيود التسويات أو الإستحقاقات أو العمليات التي ليس لها شاشات مخصصة في النظام.
أما العمليات والمستندات التي يوفرها النظام مثل فواتير المبيعات والمشتروات وسندات القبض والصرف والعمليات المخزنية وغيرها فإن النظام يقوم بإضافة القيود آليا عند ترحيل هذه المستندات.

]]>
      </content>
    </Entry>
    
            <Entry>
      <id>fi-gl-entry:doc_no</id>
      <title>رقم القيد</title>
      <content>
        <![CDATA[
رقم المستند: هو الرقم المميز للمستند في النظام حيث يمكن إستخدامه في البحث عن المستند او الفاتورة،
بشكل تلقائي يتم إعداد النظام على التسلسل الآلي للفواتير، و يكون هذا الحقل مخفيا عند الإدخال، حيث يقوم النظام بإنشاء أرقام تسلسلية للفواتير تبدأ بالرقم المحدد في إعدادات النظام ، وكل نوع من أنواع الفواتير له تسلسله الخاص به.
يمكن إعداد النظام على إظهار هذا الحقل وأيضا تعطيل الترقيم الآلي للفواتير بحيث يتم إجبار المستخدم على الترقيم اليدوي للفواتير.

في النسخة الإحترافية، يمكن عمل تسلسل خاص للفواتير حسب السنة/الفرع/الصندوق/المخزن/الفئة (أو أي خليط من هذه البيانات). ويمكن إعداد هذا على مستوى نوع الفاتورة.
]]>
      </content>
              <linked>doc_no</linked>
    </Entry>

    <Entry>
      <id>fi-gl-entry:amount</id>
      <title>إجمالي المبلغ</title>
      <content>
        <![CDATA[
إجمالي الميلغ و يجب أن يكون بالعملة المحلية، ويجب أن يطابق إجمالي المدين و إجمالي الدائن للمقابل المحلي.
]]>
      </content>
    </Entry>
    
        <Entry>
      <id>fi-gl-entry:doc_crncy</id>
      <title>العملة</title>
      <content>
        <![CDATA[
سوف تظهر آليا العملة المحلية ولن يسمح النظام بتعديلها، وتم إظهارها فقط لتنبيه المستخدم إن إجمالي المبلغ يجب أن يكون بالعملة المحلية
]]>
      </content>
    </Entry>


    <Entry>
      <id>lines</id>
      <title>تفاصيل مستند قيد اليومية</title>
      <content>
        <![CDATA[
               
<h6>نوع الحساب:</h6>
حقل إحتياري يحدد النوع للحسابات المراد إظهارها في حقل (رقم الحساب) وهذا لغرض التسهيل فقط، في هذه القائمة تظهر أنواع الحسابات وكذلك قائمة الحسابات الرئيسية. و يسمح النظام بالبحث في القائمة بالرقم أو الإسم للنوع أو الحساب الرئيسي.
مثلا إذا تم إختيار (نوع: الصناديق) فقط سيتم إظهار الحسابات التي من النوع (صندوق) في حقل (رقم الحساب), وإذا تم تركه فارغا سيتم إظهار كل الحسابات بما في ذلك الحسابات التحليلية.


<h6>رقم الحساب:</h6>
رقم واسم الحساب الذي سيتأثر رصيده بالمبلغ (مدين / دائن)، و ستظهر قائمة بالحسابات بناءا على النوع الذي تم إختياره في حقل (نوع الحساب)، أو قائمة بكل الحسابات في حال لم يتم تحديد نوع معين. يمكن البحث برقم أو اسم الحساب في الحقل نفسه وذلك بالكتابة داخل الحقل نفسه وبدون الحاجة إلى إستخدام رموز مخصصة مثل (* أو %). و يمكن البحث بإستخدام أجزاء من الإسم ولا يشترط الترتيب.
مثلا للبحث عن حساب (مصروفات المخزون التالف) يمكن البحث بإستخدام  ( مص تا )  أو (تا مص) أو (مص مخز) أو حتى لو تم إدخال الحروف باللغة الإنجليزية (بالغلط) سوف تحصل على نفس النتيجة.


<h6>مدين:</h6>
يتم إدخال المبلغ للحساب المدين بالعملة التي سيتم تحديدها في حقل العملة.


<h6>دائن:</h6>
يتم إدخال المبلغ للحساب الدائن بالعملة التي سيتم تحديدها في حقل العملة.


<h6>العملة:</h6>
عملة المبلغ (مدين/دائن) للحساب (المدين/الدائن) 
وهذه العملة يجب أن تكون أحد العملات المرتبطة بالحساب


<h6>سعر التحويل:</h6>
- هذا الحقل يحدد سعر تحويل العملة الأجنبية المحددة في حقل العملة إلى العملة المحلية وعلى أساس هذا السعر سيتم إحتساب المقابل المحلي آليا. 
- بمجرد إختيار نوع العملة سوف يظهر النظام سعر التحويل المحدد في بيانات العملة الأجنية آليا، والسماح للمستخدم بتغيير هذا السعر إذا كان لديه الصلاحية.
- يجب أن يكون سعر التحويل المحدد في هذا الحقل بين الحد الأدنى والأعلى لسعر التحويل المحدد للعملة في بيانات العملات. 
- عند تعطيل خيار (السماح بتغيير سعر التحويل في المستندات) من إعدادات المستندات لن يتم إظهار هذا الحقل و في هذه الحالة سوف يعتمد النظام سعر التحويل المحدد في بيانات العملة الأجنية.
- سعر التحويل يتم تحديده على مستوى القيد الواحد في المستند وكل قيد يمكن أن يكون السعر مختلفا حتى لنفس العملة
- إذا رغب المحاسب بإدخال المقابل المحلي يدويا و توجيه النظام إلى حساب سعر التحويل أو حساب المبلغ الأجنبي آليا، يمكن ذلك فقط يقوم بالنقر المزدوج على حقل (مدين محلي ) أو (دائن محلي) و ستظهر علامة (قفل) على الحقل وهذا يعني إنه لن يتم حساب المقبل المحلي آليا. و يمكن استخدام قائمة الأدوات في حقل سعر التحويل.
- في حالة العملة المحلية سوف يتم تثبيت السعر 1 في هذا الحقل ولن يتم السماح بتغييره 


<h6>مدين / محلي:</h6>
المقابل المحلي للمبلغ في حقل المدين. سوف يتم حساب هذا المبلغ آليا إعتمادا على سعر تحويل العملة الأجنبية المحددة في القيد.


<h6>دائن / محلي:</h6>
المقابل المحلي للمبلغ في حقل الدائن. سوف يتم حساب هذا المبلغ آليا إعتمادا على سعر تحويل العملة الأجنبية المحددة في القيد.


<h6>نسبة مئوية:</h6>
يتم إظهار هذا الحقل في حال تفعيل الخيار (إظهار عمود النسبة المئوية في شاشة القيود اليومية والسندات متعددة الأطراف) من إعدادات نظام الأستاذ العام.
سوف يحسب النظام النسبة آليا بناءا على نسبة مبلغ المقابل المحلي إلى قيمة حقل (إجمالي المبلغ)
في حال إدخال  النسبة يدويا سوف يقوم بحساب مبالغ القيد آليا بناءا على النسبة المدخلة من قيمة حقل (إجمالي المبلغ ) المدخل في بيانات المستند. يتم إدخال نسبة سالبة للتأثير في الجانب المدين من القيد.

يمكن للمستخدم تغيير (إجمالي المبلغ) وتنفيذ (إعادة حساب المبالغ بتطبيق النسب) من قائمة أدوات الشاشة وذلك لإعادة توزيع المبلغ الجديد على القيود حسب النسب. هذه الطريقة مفيدة عند وجود قيود دورية يختلف فيها المبلغ الإجمالي و لكن يتم توزيع المبالغ بنسب ثابتة دوريا مثلا توزيع إجمالي فاتورة الكهرباء على الأقسام بنسب ثابتة شهريا لغرض تقييد المصاريف حسب الأقسام. حيث يمكن نسخ القيد الدوري إلى قيد جديد وإدخال المبلغ الجديد ومن ثم تطبيق إعادة التوزيع طبقا للنسب.


<h6>البيان:</h6>
يتم إدخال البيان التوضيحي على مستوى الحساب، حيث سوف يظهر في حركة الحساب واليومية العامة،  وفي حال ترك البيان فارغا، سوف يظهر البيان الذي تم إدخاله في البيانات الرئيسية للمستند 

]]>
      </content>
    </Entry>

    <Entry>
      <id>linked_acc_no</id>
      <title>الحساب الفرعي التجميعي</title>
      <content>
        <![CDATA[
الحساب الفرعي التجميعي هو حساب أستاذ عام فرعي - يقبل أن ترتبط به حسابات تحليلية ( تعتبر فرعية تحته ) و بحيث أن أي عملية على الحساب التحليلي تؤثر أيضا في الحساب الفرعي. 
كمثال على الحساب الفرعي التجميعي: عملاء المركز الرئيسي

الحساب التحليلي لا يظهر الدليل المحاسبي الخاص بحسابات الأستاذ و لا في القوائم المالية بل يظهر الحساب الفرعي التجميعي بدلا عن كل الحسابات التحليلية التي تتبعه.


وهذا يستخدم غالبا مع حسابات العملاء والموردين والموظفين. و يمكن أيضا أن يتم إستخدامه مع أي حسابات أخرى مثل  الصناديق والبنوك أو غيرها.


]]>
      </content>
      <linked>linked_acc_no;ana_acc_no;gl_acc_no;acc_parent</linked>
    </Entry>


    <Entry>
      <id>ana_acc_no</id>
      <title>الحساب التحليلي</title>
      <content>
        <![CDATA[
هو حساب لا يظهر في القوائم المالية و لا في دليل حسابات الأستاذ العام.
لا يتبع دليل حسابات الأستاذ العام - و لكن يتبع أحد الأدلة المساعدة مثل دليل حسابات العملاء.
و يجب أن يرتبط بحساب أستاذ عام فرعي تجميعي -  بحيث أن العمليات التي تجري عليه تؤثر في حساب الأستاذ العام الفرعي المرتبط به.
يظهر في اليومية العام تحت اسم الحساب التحليلي المرتبط بالحساب الفرعي.
عدا ذلك يعتبر حساب عادي له رصيد و يمكن إضافات العملات له و له كشف حساب .

الغرض من الحساب التحليلي هو إنشاء أدله حسابات مساعدة - بحيث لا تشكل ضغطا على دليل حسابات الأستاذ العام.
و خاصة في الجهات التي لها عملاء كثر مثل حسابات الطلاب في المدارس والجامعات وحسابات المرضى و حسابات العملاء في شركات التوزيع

بشكل تلقائي النظام يتعامل مع حسابات العملاء والموردين والمندوبين و الموظفين كحسابات تحليلية - مع إمكانية إضافة هذه الحسابات كحسابات استاذ عام فرعية.

المحاسب المختص يمكنه تحويل أي نوع من الحسابات إلى تحليلية مثل الصناديق و البنوك - وخاصة في الشركات التي لها حسابات صناديق و بنوك كثيرة

لإنشاء الحساب كحساب تحليلي يجب فقط ربطه بحساب أستاذ عام فرعي تجميعي - و يجب أن تتم هذه العملية قبل أن يتم أي حركات على الحساب التحليلي

]]>
      </content>
      <linked>linked_acc_no;ana_acc_no;gl_acc_no;acc_parent</linked>
    </Entry>


    <Entry>
      <id>gl_acc_no</id>
      <title>حساب استاذ عام فرعي</title>
      <content>
        <![CDATA[
هو حساب استاذ عام يتفرع من حساب رئيسي، و له رصيد فعلي و يمكن استخدامه في القيود المحاسبية و يمكن إظهاره في القوائم المالية

وهو لا يتبع أي حساب فرعي (تجميعي)  وإلا سيعتبر حسابا تحليليا له.  

]]>
      </content>
    </Entry>

    <Entry>
      <id>acc_parent</id>
      <title>الحساب الرئيسي</title>
      <content>
        <![CDATA[
هو حساب استاذ عام بدون رصيد فعلي و رصيده هو مجموع أرصدة حسابات الأستاذ العام التي تحته . و لا يستخدم في القيود المحاسبية.
الغرض منه هو ترتيب الدليل المحاسبي على هيئة شجرة للحصول على تجميع هرمي لأرصدة للحسابات التي تحته لأغراض إصدار القوائم والتقارير المالية والإحصائيات بالتقسيم المناسب لدى الإدارة المالية. 
و يمكن أن يتفرع إلى حسابات رئيسية و فرعية

يمكن إعادة ترتيب الدليل بسهولة بإضافة حسابات رئيسية و نقل حسابات معينه تحتها و بدون أن يؤثر ذلك على الحسابات


]]>
      </content>
    </Entry>
    

    <Entry>
      <id>acc_flags12</id>
      <title>حساب فرعي له حسابات تحليلية</title>
      <content>
        <![CDATA[
عند تفعيل هذا الخيار سوف يتم السماح للحسابات التحليلية بالإرتباط بهذا الحساب. 
وذلك يعني أن أي عملية على الحساب التحليلي سوف تؤثر على الحساب الفرعي المرتبط به. و سوف يعتبر هذا الحساب الفرعي وكيلا (حساب تسوية) لكل الحسابات التحليلية المرتبطة به في نظام الأستاذ العام

يطبق هذا الخيار فقط على الحسابات الفرعية وليس له تأثير على الحسابات الرئيسية
]]>
      </content>
      <linked>linked_acc_no;ana_acc_no</linked>
    </Entry>
    
    
       <Entry>
     <id></id>
     <title></title>
     <content>
       <![CDATA[

]]>
     </content>
   </Entry>
    
       <Entry>
     <id></id>
     <title></title>
     <content>
       <![CDATA[

]]>
     </content>
   </Entry>
    

    <!--   
   <Entry>
     <id></id>
     <title></title>
     <content>
       <![CDATA[

]]>
     </content>
   </Entry>
-->
  </items>
</SystemHelp>